(this.webpackJsonpdemo=this.webpackJsonpdemo||[]).push([[16],{1807:function(e,t,a){e.exports={sliderRoot:"ListStyles_sliderRoot__3yDua",row1Root:"ListStyles_row1Root__1yYnu",listHeader:"ListStyles_listHeader__1ah34",darkScrollableList:"ListStyles_darkScrollableList__3CFvV",lightScrollableList:"ListStyles_lightScrollableList__3C4Vw",listItem:"ListStyles_listItem__1zuVv",selectedItem:"ListStyles_selectedItem__3fj9p",listItemIcon:"ListStyles_listItemIcon__1ui2F",listItemText:"ListStyles_listItemText__1ttS8",listItemTextInset:"ListStyles_listItemTextInset__2862S"}},1808:function(e,t,a){"use strict";a.d(t,"a",(function(){return m})),a.d(t,"b",(function(){return x}));var r=a(1),i=a(1596),n=a(0),o=a.n(n),l=a(1888),s=a.n(l),c={open:!1,anchor:"bottom",classes:{paperAnchorBottom:s.a.drawerPaperAnchor}},m=function(e,t){return function(a){return o.a.createElement(i.a,Object(r.a)(Object(r.a)({},c),t),o.a.createElement(e,a))}},u=a(1662),p={anchorOrigin:{vertical:"top",horizontal:"center"},transformOrigin:{vertical:"bottom",horizontal:"center"},disablePortal:!0,PaperProps:{elevation:3,classes:{root:s.a.menuPaperRoot}}},x=function(e,t){return function(a){return o.a.createElement(u.a,Object(r.a)(Object(r.a)({},p),t),o.a.createElement(e,a))}}},1888:function(e,t,a){e.exports={menuPaperRoot:"styles_menuPaperRoot__1g92s",drawerPaperAnchor:"styles_drawerPaperAnchor__2FJo9"}},2004:function(e,t,a){e.exports={notesCardWrapper:"Bookmarkers_notesCardWrapper__1PD67",row2:"Bookmarkers_row2__fotWg",row1:"Bookmarkers_row1__2qeFY",row1TimeInfo:"Bookmarkers_row1TimeInfo__qAm-A",notesContainer:"Bookmarkers_notesContainer__gya-x",noteItemTitle:"Bookmarkers_noteItemTitle__1uizh",markerIcon:"Bookmarkers_markerIcon__2dUrL",markerWrapper:"Bookmarkers_markerWrapper__3DS__",bookmarkersWrapper:"Bookmarkers_bookmarkersWrapper__23cIp"}},2005:function(e,t,a){e.exports={progressBarWrapper:"ProgressBar_progressBarWrapper__vqgqC",thumb:"ProgressBar_thumb__1oknp",positionedContainer:"ProgressBar_positionedContainer__2cN23",videoAdColors:"ProgressBar_videoAdColors__1Gv7o"}},2006:function(e,t,a){e.exports={wrapper:"DurationDisplay_wrapper__3jH_4"}},2007:function(e,t,a){e.exports={button:"SkipAdButton_button__13TNC",text:"SkipAdButton_text__-oj1y"}},2021:function(e,t,a){"use strict";a.d(t,"a",(function(){return V}));var r=a(4),i=a(3045),n=a(1682),o=a(1551),l=a(0),s=a.n(l),c=a(166),m=a(460),u=a(2155),p=a.n(u),x=a(99),d=a.n(x),k=a(791),y=a.n(k),h=a(28),f=a(1808),E=a(2004),b=a.n(E),v=function(e){var t=e.description,a=e.noted_at,r=e.onClick,i=e.onClose;return s.a.createElement(s.a.Fragment,null,s.a.createElement("div",{className:b.a.notesContainer},s.a.createElement("div",{className:b.a.row1},s.a.createElement("div",{role:"button",tabIndex:0,onClick:r,onKeyDown:r,className:b.a.row1TimeInfo},s.a.createElement(y.a,null),s.a.createElement(c.a,{variant:"body2",className:b.a.noteItemTitle},Object(h.e)(a))),s.a.createElement("div",null,s.a.createElement(m.a,{onClick:i},s.a.createElement(d.a,{fontSize:"small"})))),s.a.createElement(c.a,{variant:"body2",className:b.a.row2},t)))},g=function(e){var t=e.player,a=e.note_id,i=e.noted_at,n=e.left,o=e.description,c=Object(l.useState)(null),u=Object(r.a)(c,2),x=u[0],d=u[1],k=Boolean(x),y=k?"".concat(a,"-notes-popover"):void 0,h=function(){d(null)},E=Object(l.useMemo)((function(){return Object(f.b)(v,{id:y,open:k,anchorEl:x,onClose:h,PaperProps:{elevation:1,classes:{root:b.a.notesCardWrapper}}})}),[k]);return s.a.createElement("div",{className:b.a.markerWrapper,style:{left:"calc(".concat(n,"% - 18px)")}},s.a.createElement(m.a,{className:b.a.markerIcon,onClick:function(e){d(e.currentTarget)}},s.a.createElement(p.a,null)),s.a.createElement(E,{noted_at:i,description:o,onClose:h,onClick:function(){return t.currentTime(i)}}))},_=function(e){var t=e.player,a=e.trackDuration,r=e.bookmarks;return s.a.createElement("div",{className:b.a.bookmarkersWrapper},r.map((function(e){return s.a.createElement(g,{key:e.note_id,player:t,noted_at:e.time,note_id:e.note_id,description:e.description,left:100*e.time/a})})))},B=a(2005),A=a.n(B),S=Object(o.a)((function(){return{root:{display:"flex",alignItems:"center",fontSize:"inherit"},rail:{display:"flex",alignItems:"center",height:"1em",opacity:.16,background:"#ffffff",borderRadius:0},track:{display:"flex",alignItems:"center",height:"1em",borderRadius:0},thumb:{marginTop:0},marked:{marginBottom:"unset"},markLabel:{top:"-24px"}}}))(i.a),D=Object(o.a)((function(){return{root:{width:"100%",backgroundColor:"transparent",top:0,borderRadius:0,display:"flex",alignItems:"center",height:"1em",fontSize:"inherit"},barColorPrimary:{backgroundColor:"#fafafa",opacity:.6,borderRadius:0}}}))(n.a),V=function(e){var t=e.player,a=e.dispatch,i=e.state,n=Object(l.useRef)(),o=Object(l.useState)(t.duration()),c=Object(r.a)(o,2),m=c[0],u=c[1],p=Object(l.useState)(t.currentTime()),x=Object(r.a)(p,2),d=x[0],k=x[1],y=Object(l.useState)(t.bufferedEnd()),h=Object(r.a)(y,2),f=h[0],E=h[1],b=function(){var e=t.duration(),a=t.currentTime(),r=t.bufferedEnd();e!==m&&u(e),a!==d&&k(a),r!==f&&E(r)};Object(l.useEffect)((function(){return t.on("timeupdate",b),function(){t.off("timeupdate",b)}}),[]);var v=function(){i.configuration.canSeek&&a({type:"UPDATE_UI",payload:{shortcutHint:null}})};return s.a.createElement("div",{className:A.a.progressBarWrapper},s.a.createElement("div",{id:"progressBar",className:A.a.positionedContainer},s.a.createElement(S,{classes:{root:i.videoAd.hasVideoAd?A.a.videoAdColors:"",thumb:A.a.thumb},value:100*d/m,onChange:function(e,a){i.configuration.canSeek?t&&"number"===typeof a&&(t.currentTime(a*m/100),k(a*m/100)):e.preventDefault()},onMouseEnter:function(){i.configuration.canSeek&&(n.current&&clearTimeout(n.current),a({type:"UPDATE_UI",payload:{shortcutHint:"seek"}}),n.current=setTimeout(v,5e3))}})),!i.videoAd.hasVideoAd&&s.a.createElement("div",{id:"bufferBar",className:A.a.positionedContainer,style:{zIndex:1}},s.a.createElement(D,{value:100*f/m,variant:"determinate"})),i.bookmarks&&i.isFullscreen&&s.a.createElement("div",{className:A.a.positionedContainer,style:{zIndex:3}},s.a.createElement(_,{player:t,bookmarks:i.bookmarks,trackDuration:m})))}},2022:function(e,t,a){"use strict";a.d(t,"a",(function(){return E}));var r=a(0),i=a.n(r),n=a(22),o=a(3045),l=(a(461),a(166)),s=a(1853),c=a.n(s),m=a(261),u=a(1807),p=a.n(u),x=(Object(n.a)((function(){return{root:{display:"flex",alignItems:"center"},rail:{display:"flex",alignItems:"center",height:"4px",borderRadius:"8px",opacity:.2,backgroundColor:"rgba(255, 255, 255, 0.38)"},track:{display:"flex",alignItems:"center",height:"4px",background:"transparent"},thumb:{marginTop:0,color:"white",width:"8px",height:"8px",marginLeft:"0px","&::after":{top:"-4px",bottom:"-4px",right:"-4px",left:"-4px",border:"1px solid white"}},mark:{width:"8px",height:"8px",boxShadow:"0 1px 8px 0 rgba(0, 0, 0, 0.2), 0 3px 3px -2px var(--black-14-12), 0 3px 4px 0 var(--black-14)",backgroundColor:"#888",borderRadius:"16px",fontFamily:"Poppins"},markLabel:{color:"rgba(255, 255, 255, 0.6)"}}}))(o.a),a(1663)),d=a(1559),k=a(1722),y=a(1664),h=a(462),f=a.n(h),E=function(e){var t=e.reverseOrder,a=e.isDark,r=e.player,n=e.playbackRate,o=e.setPlaybackRate,s=r.options().playbackRates.sort(),u=s.map((function(e){return{value:e,label:1===e?"Normal":"".concat(e,"x")}})),h=s.reverse().map((function(e){return{value:e,label:1===e?"Normal":"".concat(e,"x")}})),E=t?h:u;return i.a.createElement(i.a.Fragment,null,i.a.createElement("div",{className:p.a.listHeader},i.a.createElement(c.a,{htmlColor:"currentColor"}),i.a.createElement(l.a,{variant:"subtitle1"},"Playback Speed")),i.a.createElement(x.a,{className:a?p.a.darkScrollableList:p.a.lightScrollableList},E.map((function(e){return i.a.createElement(d.a,{button:!0,classes:e.value===n?{root:p.a.selectedItem}:{root:p.a.listItem},key:e.label,onClick:function(t){return function(e,t){t.preventDefault(),t.stopPropagation(),Object(m.a)(r,e),o(e)}(e.value,t)}},e.value===n&&i.a.createElement(k.a,{classes:{root:p.a.listItemIcon}},i.a.createElement(f.a,{htmlColor:"currentColor"})),i.a.createElement(y.a,{disableTypography:!0,classes:{root:p.a.listItemText,inset:p.a.listItemTextInset},inset:e.value!==n},e.label))}))))}},2028:function(e,t,a){"use strict";a.d(t,"a",(function(){return c}));var r=a(4),i=a(28),n=a(0),o=a.n(n),l=a(2006),s=a.n(l),c=function(e){var t=e.player,a=e.state,l=Object(n.useState)(t.duration()),c=Object(r.a)(l,2),m=c[0],u=c[1],p=Object(n.useState)(t.currentTime()),x=Object(r.a)(p,2),d=x[0],k=x[1],y=function(){var e=t.duration(),a=t.currentTime();e!==m&&u(e),a!==d&&k(a)};Object(n.useEffect)((function(){return t.on("timeupdate",y),function(){t.off("timeupdate",y)}}),[]);var h=Object(i.e)(d),f=Object(i.e)(m),E=a.videoAd.hasVideoAd?"Ad: ".concat(h):"".concat(h).concat(f?" / ".concat(f):"");return o.a.createElement("div",{className:s.a.wrapper},E)}},2029:function(e,t,a){"use strict";a.d(t,"a",(function(){return p}));var r=a(0),i=a.n(r),n=a(1561),o=a(460),l=a(2158),s=a.n(l),c=a(2157),m=a.n(c),u=a(3),p=function(e){var t=e.player,a=e.isFullscreen,r=Object(u.cb)();return i.a.createElement(n.a,{title:"Full Screen (f)",placement:"top",disableFocusListener:r,disableHoverListener:r,disableTouchListener:r},i.a.createElement(o.a,{onClick:function(){if(t.isFullscreen()){if(Object(u.cb)())try{window.screen.orientation.unlock()}catch(e){console.log(e)}t.exitFullscreen()}else t.requestFullscreen().then((function(){try{Object(u.cb)()&&window.screen.orientation.lock("landscape")}catch(e){console.log(e)}}))},type:"submit"},a?i.a.createElement(m.a,null):i.a.createElement(s.a,null)))}},2030:function(e,t,a){"use strict";a.d(t,"a",(function(){return u}));var r=a(0),i=a.n(r),n=a(585),o=a(166),l=a(2159),s=a.n(l),c=a(2007),m=a.n(c),u=function(e){var t=e.onClick,a=e.buttonText,r=void 0===a?"Skip":a;return i.a.createElement(n.a,{variant:"contained",className:m.a.button,onClick:t,endIcon:i.a.createElement(s.a,null)},i.a.createElement(o.a,{variant:"button",className:m.a.text},r))}},2031:function(e,t,a){"use strict";a.d(t,"a",(function(){return h}));var r=a(0),i=a.n(r),n=a(166),o=a(1663),l=a(1559),s=a(1722),c=a(1664),m=a(1932),u=a.n(m),p=a(462),x=a.n(p),d=a(261),k=a(1807),y=a.n(k),h=function(e){var t=e.isDark,a=e.player,r=e.allSubtitles,m=e.selectedSubtitle,p=e.setSelectedSubtitle,k=function(e,t){t.preventDefault(),t.stopPropagation(),Object(d.b)(a,e),p(e)};return i.a.createElement(i.a.Fragment,null,i.a.createElement("div",{className:y.a.listHeader},i.a.createElement(u.a,{htmlColor:"currentColor"}),i.a.createElement(n.a,{variant:"subtitle1"},"Subtitles")),i.a.createElement(o.a,{className:t?y.a.darkScrollableList:y.a.lightScrollableList},r.map((function(e){var t=!!m&&e.BCP47Code===m.BCP47Code;return i.a.createElement(l.a,{classes:t?{root:y.a.selectedItem}:{root:y.a.listItem},button:!0,key:e.BCP47Code,onClick:function(t){return k(e,t)}},t&&i.a.createElement(s.a,{classes:{root:y.a.listItemIcon}},i.a.createElement(x.a,{htmlColor:"currentColor"})),i.a.createElement(c.a,{disableTypography:!0,classes:{root:y.a.listItemText,inset:y.a.listItemTextInset},inset:!t},e.label))})),i.a.createElement(l.a,{button:!0,classes:null===m?{root:y.a.selectedItem}:{root:y.a.listItem},key:"off",onClick:function(e){return k(null,e)}},null===m&&i.a.createElement(s.a,{classes:{root:y.a.listItemIcon}},i.a.createElement(x.a,{htmlColor:"currentColor"})),i.a.createElement(c.a,{disableTypography:!0,classes:{root:y.a.listItemText,inset:y.a.listItemTextInset},inset:null!==m},"Off"))))}},2032:function(e,t,a){"use strict";a.d(t,"a",(function(){return h}));var r=a(0),i=a.n(r),n=a(166),o=a(1663),l=a(1559),s=a(1722),c=a(1664),m=a(462),u=a.n(m),p=a(1933),x=a.n(p),d=a(261),k=a(1807),y=a.n(k),h=function(e){var t=e.isDark,a=e.player,r=e.allVideoQualities,m=e.selectedVideoQuality,p=e.setSelectedVideoQuality,k=r.sort((function(e,t){return e.height>t.height?-1:e.height<t.height?1:e.bandwidth>t.bandwidth?-1:e.bandwidth<t.bandwidth?1:0}));return i.a.createElement(i.a.Fragment,null,i.a.createElement("div",{className:y.a.listHeader},i.a.createElement(x.a,{htmlColor:"currentColor"}),i.a.createElement(n.a,{variant:"subtitle1"},"Video Quality")),i.a.createElement(o.a,{className:t?y.a.darkScrollableList:y.a.lightScrollableList},k.map((function(e){return i.a.createElement(l.a,{classes:e.id===m.id?{root:y.a.selectedItem}:{root:y.a.listItem},button:!0,key:e.id,onClick:function(t){return function(e,t){t.preventDefault(),t.stopPropagation(),Object(d.c)(a,e),p(r.find((function(t){return t.id===e}))||r[0])}(e.id,t)}},e.id===m.id&&i.a.createElement(s.a,{classes:{root:y.a.listItemIcon}},i.a.createElement(u.a,{htmlColor:"currentColor"})),i.a.createElement(c.a,{disableTypography:!0,classes:{root:y.a.listItemText,inset:y.a.listItemTextInset},inset:e.id!==m.id},Object(d.l)(e.height)))}))))}},2806:function(e){e.exports=JSON.parse('{"v":"5.6.5","fr":60,"ip":0,"op":45,"w":24,"h":24,"nm":"Comp 2","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":2,"ty":4,"nm":"fast_forward Outlines","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":180,"ix":10},"p":{"a":0,"k":[12,12,0],"ix":2},"a":{"a":0,"k":[12,12,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0]],"v":[[-3.842,5.424],[3.842,0],[-3.842,-5.424]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[20.123,12.109],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[50]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":15,"s":[50]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":30,"s":[100]},{"t":45,"s":[50]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 1","np":2,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0]],"v":[[-3.842,5.424],[3.842,-0.001],[-3.842,-5.424]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[11.93,12.109],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[50]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":15,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":30,"s":[50]},{"t":45,"s":[50]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 2","np":2,"cix":2,"bm":0,"ix":2,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0]],"v":[[-3.841,5.424],[3.841,0],[-3.841,-5.424]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[3.865,12.109],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":15,"s":[50]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":30,"s":[50]},{"t":45,"s":[100]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 3","np":2,"cix":2,"bm":0,"ix":3,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":46,"st":0,"bm":0}],"markers":[]}')},2807:function(e){e.exports=JSON.parse('{"v":"5.6.5","fr":60,"ip":0,"op":45,"w":24,"h":24,"nm":"Comp 1","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":1,"ty":4,"nm":"fast_forward Outlines 2","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[12,12,0],"ix":2},"a":{"a":0,"k":[12,12,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0]],"v":[[-3.842,5.424],[3.842,0],[-3.842,-5.424]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[20.123,12.109],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[50]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":15,"s":[50]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":30,"s":[100]},{"t":45,"s":[50]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 1","np":2,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0]],"v":[[-3.842,5.424],[3.842,-0.001],[-3.842,-5.424]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[11.93,12.109],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[50]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":15,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":30,"s":[50]},{"t":45,"s":[50]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 2","np":2,"cix":2,"bm":0,"ix":2,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0]],"v":[[-3.841,5.424],[3.841,0],[-3.841,-5.424]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[3.865,12.109],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":15,"s":[50]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":30,"s":[50]},{"t":45,"s":[100]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 3","np":2,"cix":2,"bm":0,"ix":3,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":46,"st":0,"bm":0}],"markers":[]}')},2808:function(e){e.exports=JSON.parse('{"v":"5.7.4","fr":60,"ip":0,"op":61,"w":96,"h":64,"nm":"Comp 2","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":3,"ty":4,"nm":"Backward Outlines","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[48,32,0],"ix":2,"l":2},"a":{"a":0,"k":[48,32,0],"ix":1,"l":2},"s":{"a":0,"k":[100,100,100],"ix":6,"l":2}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"v":[[-2.761,5],[-2.761,-4.799],[-6.5,-4.799],[-6.5,-2.665],[-5.163,-2.665],[-5.163,5]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[-2.807,0],[0,2.9],[2.808,0],[0,-2.872]],"o":[[2.808,0],[0,-2.872],[-2.807,0],[0,2.9]],"v":[[2.707,4.96],[6.5,-0.034],[2.707,-5],[-1.085,-0.034]],"c":true},"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false},{"ind":2,"ty":"sh","ix":3,"ks":{"a":0,"k":{"i":[[1.364,0],[0.013,1.479],[0,0],[-1.363,0],[0,-1.503]],"o":[[-1.312,0],[0,0],[0,-1.503],[1.364,0],[0,1.557]],"v":[[2.707,2.812],[1.183,0.138],[1.182,-0.034],[2.707,-2.853],[4.233,-0.034]],"c":true},"ix":2},"nm":"Path 3","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"Merge Paths 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[1,0.999998863071,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[64,32],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.667,0.667],"y":[1,1]},"o":{"x":[0.333,0.333],"y":[0,0]},"t":0,"s":[100,100]},{"i":{"x":[0.667,0.667],"y":[1,1]},"o":{"x":[0.333,0.333],"y":[0,0]},"t":15,"s":[150,150]},{"t":38,"s":[100,100]}],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[100]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":15,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":50,"s":[0]},{"t":61,"s":[100]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 1","np":5,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[11.046,0],[3.731,-3.802],[0,0],[0,0],[0.115,-0.141],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[-4.187,0.105],[0,0],[0,-8.836],[8.837,0],[0.168,8.691],[0,0],[0,0],[-11.046,0],[0,11.046]],"o":[[-5.444,0],[0,0],[0,0],[-0.123,0.145],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[2.89,-2.926],[0,0],[8.837,0],[0,8.837],[-8.731,0],[0,0],[0,0],[0,11.046],[11.046,0],[0,-11.046]],"v":[[0,-20],[-14.274,-14.009],[-14.573,-13.698],[-14.644,-13.618],[-15.001,-13.189],[-15,-18],[-19,-18],[-19,-6],[-7,-6],[-7,-10],[-12.428,-10],[-12.337,-10.118],[-11.983,-10.561],[-11.622,-10.997],[-11.382,-11.245],[-0.406,-15.995],[0,-16],[16,0],[0,16],[-15.997,0.315],[-16,0],[-20,0],[0,20],[20,0]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,0.999998863071,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[64,32],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":15,"s":[-45]},{"t":30,"s":[0]}],"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 2","np":2,"cix":2,"bm":0,"ix":2,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[-4.442,-0.665],[-10.5,-0.665],[-10.5,1.241],[-4.442,1.241]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[0,0]],"o":[[0,0]],"v":[[-4.442,1.241]],"c":false},"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false},{"ind":2,"ty":"sh","ix":3,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"v":[[1.053,5],[1.053,-4.799],[-2.76,-4.799],[-2.76,-2.665],[-1.397,-2.665],[-1.397,5]],"c":true},"ix":2},"nm":"Path 3","mn":"ADBE Vector Shape - Group","hd":false},{"ind":3,"ty":"sh","ix":4,"ks":{"a":0,"k":{"i":[[-2.863,0],[0,2.9],[2.864,0],[0,-2.872]],"o":[[2.864,0],[0,-2.872],[-2.863,0],[0,2.9]],"v":[[6.631,4.96],[10.5,-0.034],[6.631,-5],[2.762,-0.034]],"c":true},"ix":2},"nm":"Path 4","mn":"ADBE Vector Shape - Group","hd":false},{"ind":4,"ty":"sh","ix":5,"ks":{"a":0,"k":{"i":[[1.391,0],[0.013,1.479],[0,0],[-1.39,0],[0,-1.503]],"o":[[-1.339,0],[0,0],[0,-1.503],[1.391,0],[0,1.557]],"v":[[6.631,2.813],[5.076,0.138],[5.076,-0.034],[6.631,-2.853],[8.187,-0.034]],"c":true},"ix":2},"nm":"Path 5","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"Merge Paths 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[1,0.999998863071,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":23,"s":[25,32],"to":[-0.667,0],"ti":[0.667,0]},{"t":38,"s":[21,32]}],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":23,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":38,"s":[100]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":50,"s":[100]},{"t":61,"s":[0]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 3","np":7,"cix":2,"bm":0,"ix":3,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":1800,"st":0,"bm":0}],"markers":[]}')},2809:function(e){e.exports=JSON.parse('{"v":"5.7.4","fr":60,"ip":0,"op":61,"w":96,"h":64,"nm":"Comp 1","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":1,"ty":4,"nm":"Forward Outlines","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[48,32,0],"ix":2,"l":2},"a":{"a":0,"k":[48,32,0],"ix":1,"l":2},"s":{"a":0,"k":[100,100,100],"ix":6,"l":2}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"v":[[-2.761,5],[-2.761,-4.799],[-6.5,-4.799],[-6.5,-2.665],[-5.163,-2.665],[-5.163,5]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[-2.807,0],[0,2.9],[2.808,0],[0,-2.872]],"o":[[2.808,0],[0,-2.872],[-2.807,0],[0,2.9]],"v":[[2.707,4.96],[6.5,-0.034],[2.707,-5],[-1.085,-0.034]],"c":true},"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false},{"ind":2,"ty":"sh","ix":3,"ks":{"a":0,"k":{"i":[[1.364,0],[0.013,1.479],[0,0],[-1.363,0],[0,-1.503]],"o":[[-1.312,0],[0,0],[0,-1.503],[1.364,0],[0,1.557]],"v":[[2.707,2.812],[1.183,0.138],[1.182,-0.034],[2.707,-2.853],[4.233,-0.034]],"c":true},"ix":2},"nm":"Path 3","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"Merge Paths 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[1,0.999998863071,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[32,32],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.667,0.667],"y":[1,1]},"o":{"x":[0.333,0.333],"y":[0,0]},"t":0,"s":[100,100]},{"i":{"x":[0.667,0.667],"y":[1,1]},"o":{"x":[0.333,0.333],"y":[0,0]},"t":15,"s":[150,150]},{"t":38,"s":[100,100]}],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[100]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":15,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":50,"s":[0]},{"t":61,"s":[100]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 1","np":5,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"v":[[-6.28,3.725],[-6.28,1.336],[-3.837,1.336],[-3.837,-0.718],[-6.28,-0.718],[-6.28,-3.12],[-8.555,-3.12],[-8.555,-0.718],[-11,-0.718],[-11,1.336],[-8.555,1.336],[-8.555,3.725]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"v":[[1.363,5],[1.363,-4.798],[-2.528,-4.798],[-2.528,-2.664],[-1.137,-2.664],[-1.137,5]],"c":true},"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false},{"ind":2,"ty":"sh","ix":3,"ks":{"a":0,"k":{"i":[[-2.921,0],[0,2.9],[2.922,0],[0,-2.873]],"o":[[2.922,0],[0,-2.873],[-2.921,0],[0,2.9]],"v":[[7.053,4.96],[11,-0.033],[7.053,-5],[3.107,-0.033]],"c":true},"ix":2},"nm":"Path 3","mn":"ADBE Vector Shape - Group","hd":false},{"ind":3,"ty":"sh","ix":4,"ks":{"a":0,"k":{"i":[[1.419,0],[0.014,1.479],[0,0],[-1.418,0],[0,-1.504]],"o":[[-1.366,0],[0,0],[0,-1.504],[1.419,0],[0,1.557]],"v":[[7.053,2.813],[5.467,0.139],[5.466,-0.033],[7.053,-2.852],[8.641,-0.033]],"c":true},"ix":2},"nm":"Path 4","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"Merge Paths 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[1,0.999998863071,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":23,"s":[65,32],"to":[1.667,0],"ti":[-1.667,0]},{"t":38,"s":[75,32]}],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":23,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":38,"s":[100]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":50,"s":[100]},{"t":61,"s":[0]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 2","np":6,"cix":2,"bm":0,"ix":2,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-11.046,0],[-3.731,-3.802],[0,0],[0,0],[-0.115,-0.141],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[4.187,0.105],[0,0],[0,-8.836],[-8.836,0],[-0.168,8.691],[0,0],[0,0],[11.046,0],[0,11.046]],"o":[[5.444,0],[0,0],[0,0],[0.123,0.145],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[-2.89,-2.926],[0,0],[-8.836,0],[0,8.837],[8.731,0],[0,0],[0,0],[0,11.046],[-11.046,0],[0,-11.046]],"v":[[0,-20],[14.274,-14.009],[14.573,-13.698],[14.644,-13.618],[15.001,-13.189],[15,-18],[19,-18],[19,-6],[7,-6],[7,-10],[12.428,-10],[12.337,-10.118],[11.983,-10.561],[11.622,-10.997],[11.382,-11.245],[0.406,-15.995],[0,-16],[-16,0],[0,16],[15.997,0.315],[16,0],[20,0],[0,20],[-20,0]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,0.999998863071,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[32,32],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":15,"s":[45]},{"t":30,"s":[0]}],"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 3","np":2,"cix":2,"bm":0,"ix":3,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":1800,"st":0,"bm":0}],"markers":[]}')},2810:function(e){e.exports=JSON.parse('{"v":"5.6.5","fr":60,"ip":0,"op":30,"w":24,"h":24,"nm":"Plau pause","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":1,"ty":4,"nm":"pause Outlines","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[12,12,0],"ix":2},"a":{"a":0,"k":[12,12,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":15,"s":[{"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[14,19],[18,19],[18,5],[14,5]],"c":true}]},{"t":30,"s":[{"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[14,19],[18,19],[18,5],[14,5]],"c":true}]}],"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"tr","p":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":15,"s":[12,12],"to":[0.667,0],"ti":[-0.667,0]},{"t":30,"s":[16,12]}],"ix":2},"a":{"a":0,"k":[16,12],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 2","np":1,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":15,"s":[{"i":[[0,0]],"o":[[0,0]],"v":[[2,-7]],"c":false}]},{"t":30,"s":[{"i":[[0,0]],"o":[[0,0]],"v":[[2,-7]],"c":false}]}],"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[-6,7],[-2,7],[-2,-7],[-6,-7]],"c":true},"ix":2},"nm":"Path 3","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"Merge Paths 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":15,"s":[16,12],"to":[-0.667,0],"ti":[0.667,0]},{"t":30,"s":[12,12]}],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[0]},{"t":15,"s":[100]}],"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 1","np":4,"cix":2,"bm":0,"ix":2,"mn":"ADBE Vector Group","hd":false}],"ip":15,"op":180,"st":0,"bm":0},{"ddd":0,"ind":2,"ty":4,"nm":"play_arrow Outlines","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[16,12,0],"ix":2},"a":{"a":0,"k":[16,12,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":1,"k":[{"i":{"x":0.833,"y":0.833},"o":{"x":0.167,"y":0.167},"t":0,"s":[{"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[-5.5,-7],[-5.5,0],[-5.5,7],[5.5,0]],"c":true}]},{"t":15,"s":[{"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[0.5,7],[4.5,7],[4.5,-7],[0.5,-7]],"c":true}]}],"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[13.5,12],"ix":2},"a":{"a":1,"k":[{"i":{"x":0.833,"y":0.833},"o":{"x":0.167,"y":0.167},"t":0,"s":[0,0],"to":[0.667,0],"ti":[0,0]},{"i":{"x":0.833,"y":0.833},"o":{"x":0.167,"y":0.167},"t":15,"s":[4,0],"to":[0,0],"ti":[0.667,0]},{"t":30,"s":[0,0]}],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 1","np":2,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":180,"st":0,"bm":0}],"markers":[]}')},2811:function(e){e.exports=JSON.parse('{"v":"5.6.5","fr":60,"ip":0,"op":30,"w":24,"h":24,"nm":"Comp 1","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":3,"ty":4,"nm":"volume_up Outlines","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[12,12,0],"ix":2},"a":{"a":0,"k":[12,12,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[4.01,0.91],[0,0],[0,-3.17],[2.89,-0.86],[0,0],[0,4.28]],"o":[[0,0],[2.89,0.86],[0,3.17],[0,0],[4.01,-0.91],[0,-4.28]],"v":[[14,3.23],[14,5.29],[19,12],[14,18.71],[14,20.77],[21,12]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[100]},{"t":30,"s":[0]}],"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[17.5,12],"ix":2},"a":{"a":0,"k":[17.5,12],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 3","np":2,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,1.77],[1.48,0.74],[0,0]],"o":[[0,-1.77],[0,0],[1.48,-0.73]],"v":[[16.5,12],[14,7.97],[14,16.02]],"c":true},"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":30,"s":[100]},{"t":60,"s":[2]}],"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[15.25,11.995],"ix":2},"a":{"a":0,"k":[15.25,11.995],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 2","np":2,"cix":2,"bm":0,"ix":2,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"v":[[-9,-3],[-9,3],[-5,3],[0,8],[0,-8],[-5,-3]],"c":true},"ix":2},"nm":"Path 3","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"Merge Paths 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[12,12],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 1","np":3,"cix":2,"bm":0,"ix":3,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":180,"st":0,"bm":0}],"markers":[]}')},2812:function(e){e.exports=JSON.parse('{"v":"5.6.5","fr":60,"ip":30,"op":60,"w":24,"h":24,"nm":"Comp 1","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":3,"ty":4,"nm":"volume_up Outlines","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[12,12,0],"ix":2},"a":{"a":0,"k":[12,12,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[4.01,0.91],[0,0],[0,-3.17],[2.89,-0.86],[0,0],[0,4.28]],"o":[[0,0],[2.89,0.86],[0,3.17],[0,0],[4.01,-0.91],[0,-4.28]],"v":[[14,3.23],[14,5.29],[19,12],[14,18.71],[14,20.77],[21,12]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[100]},{"t":30,"s":[0]}],"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[17.5,12],"ix":2},"a":{"a":0,"k":[17.5,12],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 3","np":2,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,1.77],[1.48,0.74],[0,0]],"o":[[0,-1.77],[0,0],[1.48,-0.73]],"v":[[16.5,12],[14,7.97],[14,16.02]],"c":true},"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.167],"y":[0]},"t":0,"s":[100]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":30,"s":[100]},{"t":60,"s":[0]}],"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[15.25,11.995],"ix":2},"a":{"a":0,"k":[15.25,11.995],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 2","np":2,"cix":2,"bm":0,"ix":2,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"v":[[-9,-3],[-9,3],[-5,3],[0,8],[0,-8],[-5,-3]],"c":true},"ix":2},"nm":"Path 3","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"Merge Paths 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[12,12],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 1","np":3,"cix":2,"bm":0,"ix":3,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":180,"st":0,"bm":0}],"markers":[]}')},2813:function(e){e.exports=JSON.parse('{"v":"5.6.5","fr":60,"ip":0,"op":15,"w":24,"h":24,"nm":"Comp 3","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":1,"ty":4,"nm":"volume_off Outlines","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[12,12,0],"ix":2},"a":{"a":0,"k":[12,12,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"hasMask":true,"masksProperties":[{"inv":false,"mode":"s","pt":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":0,"s":[{"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[4.537,2.278],[5.179,2.921],[6.455,1.645],[5.813,1.002]],"c":true}]},{"t":15,"s":[{"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[4.537,2.278],[22.084,19.828],[23.36,18.552],[5.813,1.002]],"c":true}]}],"ix":1},"o":{"a":0,"k":100,"ix":3},"x":{"a":0,"k":0,"ix":4},"nm":"Mask 1"}],"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[4.01,0.91],[0,0],[0,-3.17],[2.89,-0.859],[0,0],[0,4.28]],"o":[[0,0],[2.89,0.86],[0,3.17],[0,0],[4.01,-0.911],[0,-4.28]],"v":[[14,3.246],[14,5.306],[19,12.016],[14,18.726],[14,20.787],[21,12.016]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[0,1.771],[1.479,0.74],[0,0]],"o":[[0,-1.77],[0,0],[1.479,-0.731]],"v":[[16.5,12.016],[14,7.986],[14,16.037]],"c":true},"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[0]},{"t":15,"s":[100]}],"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[17.5,12.016],"ix":2},"a":{"a":0,"k":[17.5,12.016],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 4","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[0.904,12],[-0.904,12],[-0.904,-12],[0.904,-12]],"c":true},"ix":2,"x":"var $bm_rt;\\n$bm_rt = content(\'Group 1\').content(\'Path 1\').path;"},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[4.525,3.589],"ix":2},"a":{"a":0,"k":[-0.008,-11.904],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.667,0.667],"y":[1,1]},"o":{"x":[0.333,0.333],"y":[0,0]},"t":0,"s":[100,0]},{"t":15,"s":[100,100]}],"ix":3},"r":{"a":0,"k":-45,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 1","np":2,"cix":2,"bm":0,"ix":2,"mn":"ADBE Vector Group","hd":false},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],"v":[[-9,-3.001],[-9,2.999],[-5,2.999],[0,7.999],[0,-8.001],[-5,-3.001]],"c":true},"ix":2},"nm":"Path 3","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"mm","mm":1,"nm":"Merge Paths 1","mn":"ADBE Vector Filter - Merge","hd":false},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[12,12.016],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Group 3","np":3,"cix":2,"bm":0,"ix":3,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":180,"st":0,"bm":0}],"markers":[]}')},2814:function(e,t,a){e.exports={lottieWrapper:"Animation_lottieWrapper__3ViZE"}},2815:function(e,t,a){e.exports={clickListener:"CenterBar_clickListener__2WiEV",textHintWrapper:"CenterBar_textHintWrapper__1UBHe",textHintContainer:"CenterBar_textHintContainer__3MATg",textHint:"CenterBar_textHint__1DzXb",grow:"CenterBar_grow__Ex6UD"}},2816:function(e,t,a){"use strict";var r=a(48),i=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a(0)),o=(0,r(a(51)).default)(n.createElement("path",{d:"M4.34 2.93L2.93 4.34 7.29 8.7 7 9H3v6h4l5 5v-6.59l4.18 4.18c-.65.49-1.38.88-2.18 1.11v2.06c1.34-.3 2.57-.92 3.61-1.75l2.05 2.05 1.41-1.41L4.34 2.93zM10 15.17L7.83 13H5v-2h2.83l.88-.88L10 11.41v3.76zM19 12c0 .82-.15 1.61-.41 2.34l1.53 1.53c.56-1.17.88-2.48.88-3.87 0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zm-7-8l-1.88 1.88L12 7.76zm4.5 8c0-1.77-1.02-3.29-2.5-4.03v1.79l2.48 2.48c.01-.08.02-.16.02-.24z"}),"VolumeOffOutlined");t.default=o},2817:function(e,t,a){"use strict";var r=a(48),i=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a(0)),o=(0,r(a(51)).default)(n.createElement("path",{d:"M3 9v6h4l5 5V4L7 9H3zm7-.17v6.34L7.83 13H5v-2h2.83L10 8.83zM16.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77 0-4.28-2.99-7.86-7-8.77z"}),"VolumeUpOutlined");t.default=o},2818:function(e,t,a){e.exports={controlsFooter:"ControlsFooter_controlsFooter__Q2Lg-",controlsFooterGradient:"ControlsFooter_controlsFooterGradient__2sQmS",leftSection:"ControlsFooter_leftSection__3GIvE",rightSection:"ControlsFooter_rightSection__1_k1D"}},2819:function(e,t,a){"use strict";var r=a(48),i=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a(0)),o=(0,r(a(51)).default)(n.createElement("path",{d:"M21 11.01L3 11v2h18zM3 16h12v2H3zM21 6H3v2.01L21 8z"}),"NotesOutlined");t.default=o},2820:function(e,t,a){e.exports={fullWidthHeight:"PlayerControls_fullWidthHeight__1dzxx",overlayContainer:"PlayerControls_overlayContainer__1G2rh",footerPositioner:"PlayerControls_footerPositioner__1oAeU"}},2996:function(e,t,a){"use strict";a.r(t),a.d(t,"default",(function(){return ge}));var r=a(0),i=a.n(r),n=a(6),o=a.n(n),l=a(166),s=a(868),c=a(2806),m=a(2807),u=a(1),p=a(4),x=a(2808),d=a(2809),k=a(2810),y=a(2811),h=a(2812),f=a(2813),E=a(2814),b=a.n(E),v={options:{autoplay:!0,loop:!1,rendererSettings:{preserveAspectRatio:"xMidYMid slice"},animationData:null},style:{borderRadius:"999px",height:"auto",width:"64px",padding:"12px",margin:0,backgroundColor:"#0000007b"},speed:1.5},g=function(e){var t=e.action,a=e.handleHide,n=Object(r.useState)(v),o=Object(p.a)(n,2),l=o[0],c=o[1],m=Object(r.useRef)();Object(r.useEffect)((function(){var e=function(e){switch(e){case"pause":return{animationData:k,direction:1};case"play":return{animationData:k,direction:-1};case"mute":return{animationData:f,direction:1};case"unmute":case"volumeUp":return{animationData:y,direction:1};case"volumeDown":return{animationData:h,direction:1};case"seekBackward":return{animationData:x,direction:1};case"seekForward":return{animationData:d,direction:1};default:return{animationData:null}}}(t),r=e.animationData,i=e.direction;c((function(e){return Object(u.a)(Object(u.a)({},e),{},{options:Object(u.a)(Object(u.a)({},e.options),{},{animationData:r}),direction:i})})),t&&m.current&&clearTimeout(m.current),t&&(m.current=setTimeout((function(){a()}),500))}),[t]);var E={};return"seekBackward"===t?E={position:"relative",right:"30%",width:"88px",padding:"4px 12px"}:"seekForward"===t&&(E={position:"relative",left:"30%",width:"88px",padding:"4px 12px"}),i.a.createElement("div",{className:b.a.lottieWrapper},i.a.createElement(s.default,Object(u.a)(Object(u.a)({},l),{},{style:t?Object(u.a)(Object(u.a)({},l.style),E):{opacity:0}})))},_=a(2815),B=a.n(_),A={options:{autoplay:!0,loop:!0,rendererSettings:{preserveAspectRatio:"xMidYMid slice"},animationData:c},style:{height:"1em",width:"1em"},speed:1.5},S={options:{autoplay:!0,loop:!0,rendererSettings:{preserveAspectRatio:"xMidYMid slice"},animationData:m},style:{height:"1em",width:"1em"},speed:1.5},D=function(e){var t=e.player,a=e.state,n=e.dispatch,o=Object(r.useRef)(null),c=function(e){1===e.detail&&(t.paused()?(n({type:"SET_ANIMATION",payload:"play"}),t.play()):(n({type:"SET_ANIMATION",payload:"pause"}),t.pause())),2===e.detail&&(t.isFullscreen()?t.exitFullscreen():t.requestFullscreen())};Object(r.useEffect)((function(){return o.current&&o.current.addEventListener("click",c),function(){o.current&&o.current.removeEventListener("click",c)}}),[]);var m=(100*t.volume()).toFixed(0);return i.a.createElement(i.a.Fragment,null,i.a.createElement("input",{ref:o,autoFocus:!0,readOnly:!0,className:B.a.clickListener}),a.UIElements.shortcutHint&&i.a.createElement("div",{className:B.a.textHintWrapper},i.a.createElement("div",{className:B.a.textHintContainer},"seek"===a.UIElements.shortcutHint&&i.a.createElement(i.a.Fragment,null,i.a.createElement(s.default,A),i.a.createElement(l.a,{variant:"h6",className:B.a.textHint},"Use \u2190 \u2192 keyboard keys to rewind or forward by 10 seconds"),i.a.createElement(s.default,S)),"volume"===a.UIElements.shortcutHint&&i.a.createElement(l.a,{variant:"h6",className:B.a.textHint},"Use \u2191 \u2193 arrow keys to adjust the volume"))),i.a.createElement("div",{className:B.a.textHintWrapper},"volumeDown"===a.animation||"volumeUp"===a.animation||"unmute"===a.animation?i.a.createElement("div",{className:B.a.textHintContainer},i.a.createElement(l.a,{variant:"h6",className:B.a.textHint},m,"%")):null),i.a.createElement(g,{action:a.animation,handleHide:function(){return n({type:"SET_ANIMATION",payload:null})}}))},V=a(461),G=a(2021),O=a(2028),C=a(1561),P=a(460),F=a(1853),T=a.n(F),I=a(2022),w=a(1808),j=function(e){var t=e.player,a=e.playbackRate,n=e.setPlaybackRate,o=Object(r.useState)(null),l=Object(p.a)(o,2),s=l[0],c=l[1],m=function(){c(null)},u=Boolean(s),x=u?"PlaybackRate-popover":void 0,d=Object(r.useMemo)((function(){return Object(w.b)(I.a,{id:x,open:u,onClose:m,anchorEl:s,transformOrigin:{vertical:"bottom",horizontal:163}})}),[u]);return i.a.createElement(i.a.Fragment,null,i.a.createElement(C.a,{title:"Playback Rate",placement:"top"},i.a.createElement(P.a,{onClick:function(e){c(e.currentTarget)}},i.a.createElement(T.a,null))),i.a.createElement(d,{player:t,playbackRate:a,setPlaybackRate:n,reverseOrder:!0,isDark:!0}))},N=a(1932),M=a.n(N),L=a(2156),H=a.n(L),R=a(2031),U=function(e){var t=e.player,a=e.allSubtitles,n=e.selectedSubtitle,o=e.setSelectedSubtitle,l=Object(r.useState)(null),s=Object(p.a)(l,2),c=s[0],m=s[1],u=Boolean(c),x=u?"Subtitles-popover":void 0,d=function(){m(null)},k=Object(r.useMemo)((function(){return Object(w.b)(R.a,{id:x,open:u,onClose:d,anchorEl:c,transformOrigin:{vertical:"bottom",horizontal:10}})}),[u]);return i.a.createElement(i.a.Fragment,null,i.a.createElement(C.a,{title:"Subtitles",placement:"top"},i.a.createElement(P.a,{onClick:function(e){m(e.currentTarget)}},n?i.a.createElement(H.a,null):i.a.createElement(M.a,null))),i.a.createElement(k,{player:t,allSubtitles:a,selectedSubtitle:n,setSelectedSubtitle:o,isDark:!0}))},z=a(866),W=a.n(z),Q=a(2154),J=a.n(Q),Y=function(e){var t=e.player,a=e.isPlaying;return i.a.createElement(C.a,{title:"Play/Pause (k)",placement:"top"},i.a.createElement(P.a,{onClick:function(){return t.paused()?t.play():t.pause()},type:"submit"},a?i.a.createElement(J.a,null):i.a.createElement(W.a,null)))},q=a(3045),K=a(368),$=a(2817),X=a.n($),Z=a(2816),ee=a.n(Z),te=a(1551),ae=Object(te.a)((function(){return{root:{display:"flex",alignItems:"center"},rail:{display:"flex",alignItems:"center",height:"4px",opacity:.16,background:"#ffffff",borderRadius:"8px"},track:{display:"flex",alignItems:"center",height:"4px",color:"white"},thumb:{marginTop:0,color:"white","&:focus(:visible)":{boxShadow:"0px 0px 0px 8px rgb(255 255 255 / 16%)"},"&:hover":{boxShadow:"0px 0px 0px 8px rgb(255 255 255 / 16%)"}}}}))(q.a),re=function(e){var t=e.player,a=e.isMuted,n=e.dispatch,o=Object(r.useState)(100*t.volume()),l=Object(p.a)(o,2),s=l[0],c=l[1],m=Object(r.useState)(!1),u=Object(p.a)(m,2),x=u[0],d=u[1],k=Object(r.useRef)(),y=function(){return c(100*t.volume())};Object(r.useEffect)((function(){return t.on("volumechange",y),function(){t.off("volumeChange",y)}}),[]);var h=function(){n({type:"UPDATE_UI",payload:{shortcutHint:null}})},f=function(){d(!1)};return i.a.createElement(i.a.Fragment,null,i.a.createElement(K.a,{display:"flex",alignItems:"center",onMouseLeave:f},i.a.createElement(C.a,{title:"Toggle Mute (m)",placement:"top"},i.a.createElement(P.a,{onMouseEnter:function(){k.current&&clearTimeout(k.current),n({type:"UPDATE_UI",payload:{shortcutHint:"volume"}}),k.current=setTimeout(h,5e3),d(!0)},onClick:function(){t.muted()?(t.muted(!1),0===t.volume()&&(t.volume(.2),c(20)),n({type:"UNMUTE"})):t.muted()||(t.muted(!0),n({type:"MUTE"}))}},a?i.a.createElement(ee.a,null):i.a.createElement(X.a,null))),!a&&x&&i.a.createElement("div",{onMouseLeave:f,style:{width:"64px"}},i.a.createElement(C.a,{title:"Volume",placement:"top"},i.a.createElement(ae,{value:s,onChange:function(e,a){t&&"number"===typeof a&&a<=100&&(t.volume(a/100),c(a))}})))))},ie=a(1933),ne=a.n(ie),oe=a(2032),le=function(e){var t=e.player,a=e.allVideoQualities,n=e.selectedVideoQuality,o=e.setSelectedVideoQuality,l=Object(r.useState)(null),s=Object(p.a)(l,2),c=s[0],m=s[1],u=Boolean(c),x=u?"VideoQuality-popover":void 0,d=function(){m(null)},k=Object(r.useMemo)((function(){return Object(w.b)(oe.a,{id:x,open:u,onClose:d,anchorEl:c,transformOrigin:{vertical:"bottom",horizontal:96}})}),[u]);return i.a.createElement(i.a.Fragment,null,i.a.createElement(C.a,{title:"Video Quality",placement:"top"},i.a.createElement(P.a,{onClick:function(e){m(e.currentTarget)}},i.a.createElement(ne.a,null))),i.a.createElement(k,{player:t,allVideoQualities:a,selectedVideoQuality:n,setSelectedVideoQuality:o,isDark:!0}))},se=a(2818),ce=a.n(se),me=(a(1145),a(2819)),ue=a.n(me),pe=function(e){var t=e.notesVisible,a=e.dispatch;return i.a.createElement(C.a,{title:"View Notes",placement:"top"},i.a.createElement(P.a,{onClick:function(){a(t?{type:"HIDE_NOTES"}:{type:"SHOW_NOTES"})},type:"submit"},i.a.createElement(ue.a,null)))},xe=a(2029),de=a(2030),ke=a(38),ye=Object(ke.a)(r.createElement(r.Fragment,null,r.createElement("path",{d:"M11.99 5V1l-5 5 5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6h-2c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"}),r.createElement("path",{d:"M10.89 16h-.85v-3.26l-1.01.31v-.69l1.77-.63h.09V16zM15.17 14.24c0 .32-.03.6-.1.82s-.17.42-.29.57-.28.26-.45.33-.37.1-.59.1-.41-.03-.59-.1-.33-.18-.46-.33-.23-.34-.3-.57-.11-.5-.11-.82v-.74c0-.32.03-.6.1-.82s.17-.42.29-.57.28-.26.45-.33.37-.1.59-.1.41.03.59.1.33.18.46.33.23.34.3.57.11.5.11.82v.74zm-.85-.86c0-.19-.01-.35-.04-.48s-.07-.23-.12-.31-.11-.14-.19-.17-.16-.05-.25-.05-.18.02-.25.05-.14.09-.19.17-.09.18-.12.31-.04.29-.04.48v.97c0 .19.01.35.04.48s.07.24.12.32.11.14.19.17.16.05.25.05.18-.02.25-.05.14-.09.19-.17.09-.19.11-.32.04-.29.04-.48v-.97z"})),"Replay10"),he=Object(ke.a)(r.createElement(r.Fragment,null,r.createElement("path",{d:"M18 13c0 3.31-2.69 6-6 6s-6-2.69-6-6 2.69-6 6-6v4l5-5-5-5v4c-4.42 0-8 3.58-8 8s3.58 8 8 8 8-3.58 8-8h-2z"}),r.createElement("path",{d:"M10.86 15.94v-4.27h-.09L9 12.3v.69l1.01-.31v3.26zM12.25 13.44v.74c0 1.9 1.31 1.82 1.44 1.82.14 0 1.44.09 1.44-1.82v-.74c0-1.9-1.31-1.82-1.44-1.82-.14 0-1.44-.09-1.44 1.82zm2.04-.12v.97c0 .77-.21 1.03-.59 1.03s-.6-.26-.6-1.03v-.97c0-.75.22-1.01.59-1.01.38-.01.6.26.6 1.01z"})),"Forward10"),fe=function(e){var t=e.player,a=e.dispatch;return i.a.createElement(K.a,null,i.a.createElement(C.a,{title:"Rewind 10s",placement:"top"},i.a.createElement(P.a,{onClick:function(){t&&(t.currentTime(t.currentTime()-10),a({type:"SET_ANIMATION",payload:"seekBackward"}))}},i.a.createElement(ye,null))),i.a.createElement(C.a,{title:"Forward 10s",placement:"top"},i.a.createElement(P.a,{onClick:function(){t&&(t.currentTime(t.currentTime()+10),a({type:"SET_ANIMATION",payload:"seekForward"}))}},i.a.createElement(he,null))))},Ee=function(e){var t=e.player,a=e.skipAd,r=e.state,n=e.dispatch;return i.a.createElement(i.a.Fragment,null,i.a.createElement("div",{className:ce.a.controlsFooterGradient}),i.a.createElement("div",{className:ce.a.controlsFooter},r.UIElements.playProgressSlider&&i.a.createElement(G.a,{player:t,state:r,dispatch:n}),i.a.createElement(V.a,{container:!0,justify:"space-between"},i.a.createElement("div",{className:ce.a.leftSection},r.UIElements.playPauseButton&&i.a.createElement(Y,{player:t,isPlaying:r.isPlaying}),r.UIElements.seekButtons&&i.a.createElement(fe,{player:t,dispatch:n}),r.UIElements.volumeControlButton&&i.a.createElement(re,{player:t,dispatch:n,isMuted:r.isMuted}),i.a.createElement(O.a,{player:t,state:r})),i.a.createElement("div",{className:ce.a.rightSection},r.configuration.hasNotes&&r.UIElements.notesButton&&i.a.createElement(pe,{notesVisible:r.notesVisible,dispatch:n}),r.UIElements.subtitlesButton&&i.a.createElement(U,{player:t,allSubtitles:r.subtitles.allSubtitles,selectedSubtitle:r.subtitles.current,setSelectedSubtitle:function(e){return n({type:"UPDATE_CURRENT_SUBTITLE",payload:e})}}),r.UIElements.playbackRateButton&&i.a.createElement(j,{player:t,playbackRate:r.playbackRate,setPlaybackRate:function(e){return n({type:"UPDATE_PLAYBACK_RATE",payload:e})}}),r.UIElements.videoQualityButton&&i.a.createElement(le,{player:t,allVideoQualities:r.videoQuality.allQualities,selectedVideoQuality:r.videoQuality.current,setSelectedVideoQuality:function(e){return n({type:"UPDATE_CURRENT_VIDEO_QUALITY",payload:e})}}),r.videoAd.hasVideoAd&&r.videoAd.showSkipButton&&i.a.createElement(de.a,{onClick:a}),r.UIElements.fullscreenButton&&i.a.createElement(xe.a,{player:t,isFullscreen:r.isFullscreen})))))},be=a(2820),ve=a.n(be),ge=function(e){return i.a.createElement("div",{className:ve.a.fullWidthHeight},i.a.createElement("div",{className:ve.a.overlayContainer},i.a.createElement("div",{className:ve.a.fullWidthHeight},i.a.createElement(D,e)),i.a.createElement("div",{className:o()("autoHide",ve.a.footerPositioner)},i.a.createElement(Ee,e))))}}}]);