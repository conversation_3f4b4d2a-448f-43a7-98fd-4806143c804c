<?xml version="1.0" encoding="UTF-8"?><process version="9.10.013">
  <context>
    <input/>
    <output/>
    <macros/>
  </context>
  <operator activated="true" class="process" compatibility="9.10.013" expanded="true" name="Process">
    <parameter key="logverbosity" value="init"/>
    <parameter key="random_seed" value="2001"/>
    <parameter key="send_mail" value="never"/>
    <parameter key="notification_email" value=""/>
    <parameter key="process_duration_for_mail" value="30"/>
    <parameter key="encoding" value="SYSTEM"/>
    <process expanded="true">
      <operator activated="true" class="retrieve" compatibility="9.10.013" expanded="true" height="68" name="Retrieve ratings_Electronics" width="90" x="45" y="85">
        <parameter key="repository_entry" value="//Local Repository/data/ratings_Electronics"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.013" expanded="true" height="103" name="Multiply" width="90" x="179" y="238"/>
      <operator activated="true" class="aggregate" compatibility="9.10.013" expanded="true" height="82" name="Aggregate (2)" width="90" x="380" y="34">
        <parameter key="use_default_aggregation" value="false"/>
        <parameter key="attribute_filter_type" value="subset"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value="stars"/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="default_aggregation_function" value="count"/>
        <list key="aggregation_attributes">
          <parameter key="rating" value="count"/>
          <parameter key="rating" value="average"/>
        </list>
        <parameter key="group_by_attributes" value="user_id"/>
        <parameter key="count_all_combinations" value="false"/>
        <parameter key="only_distinct" value="false"/>
        <parameter key="ignore_missings" value="true"/>
      </operator>
      <operator activated="true" class="filter_examples" compatibility="9.10.013" expanded="true" height="103" name="Filter Examples (2)" width="90" x="514" y="34">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="custom_filters"/>
        <parameter key="invert_filter" value="false"/>
        <list key="filters_list">
          <parameter key="filters_entry_key" value="count(rating).ge.100"/>
        </list>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="concurrency:join" compatibility="9.10.013" expanded="true" height="82" name="Join" width="90" x="581" y="238">
        <parameter key="remove_double_attributes" value="true"/>
        <parameter key="join_type" value="inner"/>
        <parameter key="use_id_attribute_as_key" value="false"/>
        <list key="key_attributes">
          <parameter key="user_id" value="user_id"/>
        </list>
        <parameter key="keep_both_join_attributes" value="false"/>
      </operator>
      <operator activated="true" class="set_role" compatibility="9.10.013" expanded="true" height="82" name="Set Role" width="90" x="648" y="85">
        <parameter key="attribute_name" value="rating"/>
        <parameter key="target_role" value="label"/>
        <list key="set_additional_roles">
          <parameter key="user_id" value="user identification"/>
          <parameter key="prod_id" value="item identification"/>
        </list>
      </operator>
      <operator activated="true" class="split_data" compatibility="9.10.013" expanded="true" height="103" name="Split Data" width="90" x="715" y="238">
        <enumeration key="partitions">
          <parameter key="ratio" value="0.8"/>
          <parameter key="ratio" value="0.2"/>
        </enumeration>
        <parameter key="sampling_type" value="automatic"/>
        <parameter key="use_local_random_seed" value="true"/>
        <parameter key="local_random_seed" value="1992"/>
      </operator>
      <operator activated="true" class="irbrecommender:user_k-NN_rp" compatibility="5.1.002" expanded="true" height="82" name="User k-NN" width="90" x="782" y="34">
        <parameter key="k" value="90"/>
        <parameter key="Min Rating" value="1"/>
        <parameter key="Range" value="4"/>
        <parameter key="Correlation mode" value="pearson"/>
        <parameter key="reg_u" value="5.0"/>
        <parameter key="reg_i" value="3.0"/>
        <parameter key="schrinkage" value="10.0"/>
      </operator>
      <operator activated="true" class="irbrecommender:apply_model_rp" compatibility="5.1.002" expanded="true" height="82" name="Apply Model (2)" width="90" x="916" y="187">
        <parameter key="Online updates" value="false"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.013" expanded="true" height="103" name="Multiply (3)" width="90" x="1050" y="238"/>
      <operator activated="true" class="irbrecommender:performance_rating_prediction" compatibility="5.1.002" expanded="true" height="82" name="Performance (2)" width="90" x="1184" y="85">
        <parameter key="Min Rating" value="1"/>
        <parameter key="Range" value="4"/>
      </operator>
      <operator activated="true" class="select_attributes" compatibility="9.10.013" expanded="true" height="82" name="Select Attributes (2)" width="90" x="1184" y="289">
        <parameter key="attribute_filter_type" value="subset"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value="prediction|user_id"/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
      </operator>
      <connect from_op="Retrieve ratings_Electronics" from_port="output" to_op="Multiply" to_port="input"/>
      <connect from_op="Multiply" from_port="output 1" to_op="Aggregate (2)" to_port="example set input"/>
      <connect from_op="Multiply" from_port="output 2" to_op="Join" to_port="right"/>
      <connect from_op="Aggregate (2)" from_port="example set output" to_op="Filter Examples (2)" to_port="example set input"/>
      <connect from_op="Filter Examples (2)" from_port="example set output" to_op="Join" to_port="left"/>
      <connect from_op="Join" from_port="join" to_op="Set Role" to_port="example set input"/>
      <connect from_op="Set Role" from_port="example set output" to_op="Split Data" to_port="example set"/>
      <connect from_op="Split Data" from_port="partition 1" to_op="User k-NN" to_port="example set"/>
      <connect from_op="Split Data" from_port="partition 2" to_op="Apply Model (2)" to_port="query set"/>
      <connect from_op="User k-NN" from_port="Model" to_op="Apply Model (2)" to_port="Model"/>
      <connect from_op="Apply Model (2)" from_port="result set" to_op="Multiply (3)" to_port="input"/>
      <connect from_op="Multiply (3)" from_port="output 1" to_op="Performance (2)" to_port="predictions"/>
      <connect from_op="Multiply (3)" from_port="output 2" to_op="Select Attributes (2)" to_port="example set input"/>
      <connect from_op="Performance (2)" from_port="performance" to_port="result 1"/>
      <connect from_op="Performance (2)" from_port="evaluation measures" to_port="result 2"/>
      <connect from_op="Select Attributes (2)" from_port="example set output" to_port="result 3"/>
      <portSpacing port="source_input 1" spacing="0"/>
      <portSpacing port="sink_result 1" spacing="0"/>
      <portSpacing port="sink_result 2" spacing="0"/>
      <portSpacing port="sink_result 3" spacing="0"/>
      <portSpacing port="sink_result 4" spacing="0"/>
    </process>
  </operator>
</process>
