<?xml version="1.0" encoding="UTF-8"?><process version="9.10.011">
  <context>
    <input/>
    <output/>
    <macros/>
  </context>
  <operator activated="true" class="process" compatibility="9.10.011" expanded="true" name="Process">
    <parameter key="logverbosity" value="init"/>
    <parameter key="random_seed" value="2001"/>
    <parameter key="send_mail" value="never"/>
    <parameter key="notification_email" value=""/>
    <parameter key="process_duration_for_mail" value="30"/>
    <parameter key="encoding" value="SYSTEM"/>
    <process expanded="true">
      <operator activated="true" class="retrieve" compatibility="9.10.011" expanded="true" height="68" name="Retrieve" width="90" x="45" y="34">
        <parameter key="repository_entry" value="../../Data/Time-Series/amazon_stocks_prices"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.011" expanded="true" height="124" name="Splitting Data" width="90" x="45" y="136"/>
      <operator activated="true" class="filter_examples" compatibility="9.10.011" expanded="true" height="103" name="Testing Set" width="90" x="45" y="493">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="custom_filters"/>
        <parameter key="invert_filter" value="true"/>
        <list key="filters_list">
          <parameter key="filters_entry_key" value="date.lt.01/01/2016"/>
        </list>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="time_series:logarithm" compatibility="9.10.009" expanded="true" height="68" name="Logarithm (2)" width="90" x="447" y="391">
        <parameter key="attribute_filter_type" value="all"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="true"/>
        <parameter key="new_attributes_postfix" value="_logarithm"/>
        <parameter key="logarithm_type" value="ln"/>
      </operator>
      <operator activated="true" class="filter_examples" compatibility="9.10.011" expanded="true" height="103" name="Filter Examples (2)" width="90" x="581" y="391">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="custom_filters"/>
        <parameter key="invert_filter" value="false"/>
        <list key="filters_list">
          <parameter key="filters_entry_key" value="date.gt.12/1/2015"/>
          <parameter key="filters_entry_key" value="date.lt.12/1/2017"/>
        </list>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="time_series:logarithm" compatibility="9.10.009" expanded="true" height="68" name="Logarithm" width="90" x="179" y="34">
        <parameter key="attribute_filter_type" value="all"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="true"/>
        <parameter key="new_attributes_postfix" value="_logarithm"/>
        <parameter key="logarithm_type" value="ln"/>
      </operator>
      <operator activated="true" class="time_series:differentiation" compatibility="9.10.009" expanded="true" height="68" name="Differentiate" width="90" x="179" y="187">
        <parameter key="attribute_filter_type" value="all"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="true"/>
        <parameter key="new_attributes_postfix" value="_differentiated"/>
        <parameter key="lag" value="1"/>
        <parameter key="differentiation_method" value="subtraction"/>
      </operator>
      <operator activated="true" class="filter_examples" compatibility="9.10.011" expanded="true" height="103" name="Filter Examples" width="90" x="313" y="34">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="no_missing_attributes"/>
        <parameter key="invert_filter" value="false"/>
        <list key="filters_list">
          <parameter key="filters_entry_key" value="Timestamp.is_not_missing."/>
        </list>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="time_series:forecast_validation" compatibility="9.10.009" expanded="true" height="145" name="Forecast Validation" width="90" x="313" y="187">
        <parameter key="time_series_attribute" value="close"/>
        <parameter key="has_indices" value="true"/>
        <parameter key="indices_attribute" value="date"/>
        <parameter key="expert_settings" value="true"/>
        <parameter key="unit" value="time based"/>
        <parameter key="windows_defined" value="from start"/>
        <parameter key="custom_start_point" value="5"/>
        <parameter key="custom_end_point" value="100"/>
        <parameter key="window_size" value="100"/>
        <parameter key="custom_start_time" value="2000-01-01 00:00:00"/>
        <parameter key="custom_end_time" value="2030-01-01 00:00:00"/>
        <parameter key="date_format" value="yyyy-MM-dd HH:mm:ss"/>
        <parameter key="window_size_time" value="100.Months"/>
        <parameter key="windows_stop_definition" value="from next window start"/>
        <parameter key="window_start_attribute" value=""/>
        <parameter key="window_stop_attribute" value=""/>
        <parameter key="no_overlapping_windows" value="false"/>
        <parameter key="step_size" value="1"/>
        <parameter key="step_size_time" value="1.Months"/>
        <parameter key="horizon_size" value="11"/>
        <parameter key="horizon_size_time" value="24.Months"/>
        <parameter key="horizon_start_attribute" value=""/>
        <parameter key="horizon_stop_attribute" value=""/>
        <parameter key="empty_window_handling" value="add empty exampleset"/>
        <parameter key="enable_parallel_execution" value="true"/>
        <process expanded="true">
          <operator activated="true" class="concurrency:optimize_parameters_grid" compatibility="9.10.011" expanded="true" height="145" name="Optimize Parameters (Grid)" width="90" x="112" y="34">
            <list key="parameters">
              <parameter key="ARIMA.p:_order_of_the_autoregressive_model" value="[1;10;10;linear]"/>
              <parameter key="ARIMA.q:_order_of_the_moving-average_model" value="[1;10;10;linear]"/>
            </list>
            <parameter key="error_handling" value="fail on error"/>
            <parameter key="log_performance" value="true"/>
            <parameter key="log_all_criteria" value="false"/>
            <parameter key="synchronize" value="false"/>
            <parameter key="enable_parallel_execution" value="true"/>
            <process expanded="true">
              <operator activated="true" class="time_series:arima_trainer" compatibility="9.10.009" expanded="true" height="103" name="ARIMA" width="90" x="112" y="34">
                <parameter key="time_series_attribute" value="close"/>
                <parameter key="has_indices" value="true"/>
                <parameter key="indices_attribute" value="date"/>
                <parameter key="p:_order_of_the_autoregressive_model" value="1"/>
                <parameter key="d:_degree_of_differencing" value="0"/>
                <parameter key="q:_order_of_the_moving-average_model" value="1"/>
                <parameter key="estimate_constant" value="true"/>
                <parameter key="main_criterion" value="aic"/>
                <parameter key="error_handling" value="use default forecast model"/>
              </operator>
              <connect from_port="input 1" to_op="ARIMA" to_port="example set"/>
              <connect from_op="ARIMA" from_port="forecast model" to_port="output 1"/>
              <connect from_op="ARIMA" from_port="performance" to_port="performance"/>
              <portSpacing port="source_input 1" spacing="0"/>
              <portSpacing port="source_input 2" spacing="0"/>
              <portSpacing port="sink_performance" spacing="0"/>
              <portSpacing port="sink_model" spacing="0"/>
              <portSpacing port="sink_output 1" spacing="0"/>
              <portSpacing port="sink_output 2" spacing="0"/>
            </process>
          </operator>
          <connect from_port="training set" to_op="Optimize Parameters (Grid)" to_port="input 1"/>
          <connect from_op="Optimize Parameters (Grid)" from_port="performance" to_port="through 1"/>
          <connect from_op="Optimize Parameters (Grid)" from_port="output 1" to_port="model"/>
          <portSpacing port="source_training set" spacing="0"/>
          <portSpacing port="sink_model" spacing="0"/>
          <portSpacing port="sink_through 1" spacing="0"/>
          <portSpacing port="sink_through 2" spacing="0"/>
        </process>
        <process expanded="true">
          <operator activated="true" class="extract_log_value" compatibility="9.10.011" expanded="true" height="68" name="Extract Log Value" width="90" x="45" y="34">
            <parameter key="attribute_name" value="Last date in window"/>
            <parameter key="example_index" value="1"/>
          </operator>
          <operator activated="true" class="performance_regression" compatibility="9.10.011" expanded="true" height="82" name="Performance" width="90" x="179" y="34">
            <parameter key="main_criterion" value="first"/>
            <parameter key="root_mean_squared_error" value="true"/>
            <parameter key="absolute_error" value="false"/>
            <parameter key="relative_error" value="false"/>
            <parameter key="relative_error_lenient" value="false"/>
            <parameter key="relative_error_strict" value="false"/>
            <parameter key="normalized_absolute_error" value="false"/>
            <parameter key="root_relative_squared_error" value="false"/>
            <parameter key="squared_error" value="false"/>
            <parameter key="correlation" value="false"/>
            <parameter key="squared_correlation" value="false"/>
            <parameter key="prediction_average" value="false"/>
            <parameter key="spearman_rho" value="false"/>
            <parameter key="kendall_tau" value="false"/>
            <parameter key="skip_undefined_labels" value="true"/>
            <parameter key="use_example_weights" value="true"/>
          </operator>
          <operator activated="true" class="log" compatibility="9.10.011" expanded="true" height="103" name="Log" width="90" x="313" y="34">
            <list key="log">
              <parameter key="Error" value="operator.Performance.value.root_mean_squared_error"/>
              <parameter key="training time" value="operator.Extract Log Value.value.data_value"/>
            </list>
            <parameter key="sorting_type" value="none"/>
            <parameter key="sorting_k" value="100"/>
            <parameter key="persistent" value="false"/>
          </operator>
          <connect from_port="test set" to_op="Extract Log Value" to_port="example set"/>
          <connect from_op="Extract Log Value" from_port="example set" to_op="Performance" to_port="labelled data"/>
          <connect from_op="Performance" from_port="performance" to_op="Log" to_port="through 1"/>
          <connect from_op="Performance" from_port="example set" to_op="Log" to_port="through 2"/>
          <connect from_op="Log" from_port="through 1" to_port="performance 1"/>
          <connect from_op="Log" from_port="through 2" to_port="test set results"/>
          <portSpacing port="source_test set" spacing="0"/>
          <portSpacing port="source_through 1" spacing="0"/>
          <portSpacing port="source_through 2" spacing="0"/>
          <portSpacing port="sink_test set results" spacing="0"/>
          <portSpacing port="sink_performance 1" spacing="0"/>
          <portSpacing port="sink_performance 2" spacing="0"/>
        </process>
      </operator>
      <operator activated="true" class="time_series:apply_forecast" compatibility="9.10.009" expanded="true" height="82" name="Apply Forecast (2)" width="90" x="447" y="34">
        <parameter key="forecast_horizon" value="24"/>
        <parameter key="add_original_time_series" value="false"/>
        <parameter key="add_combined_time_series" value="false"/>
      </operator>
      <operator activated="true" class="series:integrate_series" compatibility="7.4.000" expanded="true" height="82" name="Integrate" width="90" x="581" y="34">
        <parameter key="attribute_name" value="forecast of close"/>
        <parameter key="keep_original_attribute" value="true"/>
      </operator>
      <operator activated="true" class="operator_toolbox:merge" compatibility="2.14.000" expanded="true" height="103" name="Merge Attributes" width="90" x="447" y="187">
        <parameter key="handling_of_duplicate_attributes" value="keep_only_first"/>
        <parameter key="handling_of_special_attributes" value="keep_only_first"/>
        <parameter key="handling_of_duplicate_annotations" value="keep_only_first"/>
      </operator>
      <operator activated="true" class="generate_attributes" compatibility="9.10.011" expanded="true" height="82" name="Generate Attributes" width="90" x="581" y="187">
        <list key="function_descriptions">
          <parameter key="Forecasted" value="[cumulative(forecast of close)]+close"/>
        </list>
        <parameter key="keep_all" value="true"/>
      </operator>
      <operator activated="true" class="generate_attributes" compatibility="9.10.011" expanded="true" height="82" name="Generate Attributes (2)" width="90" x="715" y="34">
        <list key="function_descriptions">
          <parameter key="Forecasted" value="exp(Forecasted)"/>
        </list>
        <parameter key="keep_all" value="true"/>
      </operator>
      <operator activated="true" class="operator_toolbox:merge" compatibility="2.14.000" expanded="true" height="103" name="Merge Attributes (2)" width="90" x="782" y="493">
        <parameter key="handling_of_duplicate_attributes" value="keep_only_first"/>
        <parameter key="handling_of_special_attributes" value="keep_only_first"/>
        <parameter key="handling_of_duplicate_annotations" value="keep_only_first"/>
      </operator>
      <connect from_op="Retrieve" from_port="output" to_op="Splitting Data" to_port="input"/>
      <connect from_op="Splitting Data" from_port="output 1" to_op="Logarithm" to_port="example set"/>
      <connect from_op="Splitting Data" from_port="output 2" to_op="Testing Set" to_port="example set input"/>
      <connect from_op="Splitting Data" from_port="output 3" to_op="Logarithm (2)" to_port="example set"/>
      <connect from_op="Testing Set" from_port="example set output" to_op="Merge Attributes (2)" to_port="example set 1"/>
      <connect from_op="Logarithm (2)" from_port="example set" to_op="Filter Examples (2)" to_port="example set input"/>
      <connect from_op="Filter Examples (2)" from_port="example set output" to_op="Merge Attributes" to_port="example set 2"/>
      <connect from_op="Logarithm" from_port="example set" to_op="Differentiate" to_port="example set"/>
      <connect from_op="Differentiate" from_port="example set" to_op="Filter Examples" to_port="example set input"/>
      <connect from_op="Filter Examples" from_port="example set output" to_op="Forecast Validation" to_port="example set"/>
      <connect from_op="Forecast Validation" from_port="model" to_op="Apply Forecast (2)" to_port="forecast model"/>
      <connect from_op="Forecast Validation" from_port="performance 1" to_port="result 2"/>
      <connect from_op="Apply Forecast (2)" from_port="example set" to_op="Integrate" to_port="example set input"/>
      <connect from_op="Integrate" from_port="example set output" to_op="Merge Attributes" to_port="example set 1"/>
      <connect from_op="Merge Attributes" from_port="merged set" to_op="Generate Attributes" to_port="example set input"/>
      <connect from_op="Generate Attributes" from_port="example set output" to_op="Generate Attributes (2)" to_port="example set input"/>
      <connect from_op="Generate Attributes (2)" from_port="example set output" to_op="Merge Attributes (2)" to_port="example set 2"/>
      <connect from_op="Merge Attributes (2)" from_port="merged set" to_port="result 1"/>
      <portSpacing port="source_input 1" spacing="0"/>
      <portSpacing port="sink_result 1" spacing="0"/>
      <portSpacing port="sink_result 2" spacing="0"/>
      <portSpacing port="sink_result 3" spacing="0"/>
    </process>
  </operator>
</process>
