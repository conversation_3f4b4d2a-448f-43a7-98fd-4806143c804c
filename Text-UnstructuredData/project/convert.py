#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Fri Nov 10 20:47:44 2023

@author: jaideepshah
"""

import pandas as pd

# Read the Excel file
df = pd.read_excel('SPAM-text-message.xlsx')

# Filter rows for each category
ham = df[df['Category'] == 'ham']
spam = df[df['Category'] == 'spam']

# Balance the dataset
min_count = min(len(ham), len(spam))
balanced_ham = ham.sample(min_count)
balanced_spam = spam.sample(min_count)

# Combine and shuffle the balanced dataset
balanced_df = pd.concat([balanced_ham, balanced_spam]).sample(frac=1).reset_index(drop=True)

# Write to a new CSV file
balanced_df.to_csv('balanced_output.csv', index=False)
