#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sat Nov 18 15:55:54 2023

@author: jaideepshah
"""

import pandas as pd
from textblob import TextBlob
from wordcloud import WordCloud
import matplotlib.pyplot as plt

# Load data from CSV
df = pd.read_csv('balanced_output.csv')  # Replace 'yourfile.csv' with your CSV file path
text_column = 'Message'  # Replace 'text' with the name of the column containing text

# Perform sentiment analysis and filter top positive/negative words
word_sentiments = {}
for text in df[text_column]:
    blob = TextBlob(text)
    for word, pos in blob.tags:
        if word.lower() not in word_sentiments:
            word_sentiments[word.lower()] = 0
        word_sentiments[word.lower()] += blob.sentiment.polarity

# Sort words by sentiment and pick top 100
top_words = dict(sorted(word_sentiments.items(), key=lambda item: item[1], reverse=True)[:100])

# Generate word cloud
wordcloud = WordCloud(width=800, height=400, background_color ='white').generate_from_frequencies(top_words)

# Plot
plt.figure(figsize=(10, 5))
plt.imshow(wordcloud, interpolation='bilinear')
plt.axis('off')
plt.show()
