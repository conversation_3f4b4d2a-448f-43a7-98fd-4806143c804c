!function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=225)}([,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"f",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"e",(function(){return a})),n.d(t,"i",(function(){return s})),n.d(t,"h",(function(){return c})),n.d(t,"g",(function(){return u})),n.d(t,"j",(function(){return l})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return d}));var r=Object.freeze({LAST_RECORDING_ACTIVITY_STORE_DEBOUNCE:5e3,MAX_TIME_SINCE_LAST_RECORDING_ACTIVITY_IN_SESSION:12e4}),i=window.hjLazyModules,o={SCRIPT:"js",STYLESHEET:"css"},a="https://hotjar.com",s=(Object.freeze({id:null,selector_version:2}),60),c=60*s,u=24*c,l=365*u,h=s/2,d=4},function(e,t,n){"use strict";function r(e){return function(e){if(Array.isArray(e))return i(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,"a",(function(){return h})),n.d(t,"e",(function(){return d})),n.d(t,"d",(function(){return f})),n.d(t,"c",(function(){return g})),n.d(t,"b",(function(){return p})),n.d(t,"f",(function(){return v})),n.d(t,"g",(function(){return m}));var o,a="🐛",s=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return function(){return e+=t}},c=function(e){return function(e){for(var t,n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return(t=console).debug.apply(t,[a,e].concat(r(i||[])))}("[safeNative] ".concat(e))},u=function(){if(document.body){var e=document.createElement("iframe");return e.id="_hjSafeContext_".concat(function(){arguments.length>0&&void 0!==arguments[0]||s(45887);return Math.floor(1e8*Math.random())}()),e.title="_hjSafeContext",e.tabIndex=-1,e.setAttribute("aria-hidden","true"),e.src="about:blank",e.style.setProperty("display","none","important"),e.style.setProperty("width","1px","important"),e.style.setProperty("height","1px","important"),e.style.setProperty("opacity","0","important"),e.style.setProperty("pointer-events","none","important"),document.body.appendChild(e),e}},l=function(e,t){try{if(!o){var n=u();if(!n)return c("Unable to access an IFrame context, using default property."),e;o=n}var r=("function"==typeof e?e.name:t)||t;if(!r)return c("Unable to name property or missing fallbackName"),e;if(!o.contentWindow)return c("Unable to access contentWindow property"),e;var i=o.contentWindow[r];return i||(c("Unable to access property with name [".concat(r,"] from an IFrame context")),e)}catch(t){return c("An unexpected error occurred".concat(t instanceof Error?": ".concat(t.message):"",". Using default constructor")),e}},h=l(Date,"Date"),d=l(encodeURIComponent,"encodeURIComponent"),f=l(decodeURIComponent,"decodeURLComponent"),g=l(btoa,"btoa"),p=l(atob,"atob"),v=l(escape,"escape"),m=l(unescape,"unescape")},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var r=n(19),i=n.n(r),o=n(3),a=n(34),s=n(4),c=function(e,t){if(!e)return null;var n=new s.a((new s.a).getTime()+1e3*e);if(t){var r=new s.a;r.setHours(23),r.setMinutes(59),r.setSeconds(59),r.setMilliseconds(999),n.setTime(Math.min(n,r))}return n};function u(e){var t=this,n=e.key,r=e.supportSubdomains,s=void 0!==r&&r,c=e.ttlSeconds,u=void 0===c?o.j:c,l=e.shouldSync,h=void 0===l||l,d=e.keepAliveSeconds,f=void 0===d?0:d,g=e.shouldExtendExpiryOnActivity,p=void 0!==g&&g,v=e.shouldExpireAtMidnight,m=void 0!==v&&v,y=e.checkExpiry,j=void 0===y?null:y;this.key=n,this.ttlSeconds=u,this.shouldSync=h,this.keepAliveSeconds=f,this.shouldExpireAtMidnight=m,this.hasExceededCookieMaxDuration=!1,this.isSessionOnly=0===this.ttlSeconds,this.supportSubdomains=s,this.ttlSeconds>0&&(this.activeRefreshTimerId=null,this.keepAliveSeconds>0&&setInterval((function(){return t.refreshExpiryWithThrottling()}),1e3*f),p&&(document.addEventListener("click",(function(){return t.refreshExpiryWithThrottling(j)}),!1),document.addEventListener("mousemove",(function(){return t.refreshExpiryWithThrottling(j)}),!1),document.addEventListener("keypress",(function(){return t.refreshExpiryWithThrottling(j)}),!1),document.addEventListener("scroll",(function(){return t.refreshExpiryWithThrottling(j)}),!1),document.addEventListener("visibilitychange",(function(){return t.refreshExpiryWithThrottling(j)}),!1))),this.cookie=i.a.withAttributes(function(e){var t={sameSite:"None",secure:!0};if(e){var n=window.location.hostname;t.domain=Object(a.getMidLevelDomain)(n)}return t}(s))}function l(e){var t=e.key;this.key=t}function h(e){u.call(this,e)}u.prototype.getKey=function(){return this.key},u.prototype.get=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.resetExpiry,n=void 0!==t&&t;try{var r=this.cookie.get(this.key)||null;if(!this.isSessionOnly&&this.shouldSync&&(r=this.sync(r)),n&&this.ttlSeconds&&r){var i=c(this.ttlSeconds,this.shouldExpireAtMidnight);this.cookie.set(this.key,r,{expires:i})}return r}catch(e){return hj.log.debug("Cookie Error: ".concat(e.message),"cookies"),void hj.metrics.count("session-rejection",{tag:{reason:"cookies"},extraTags:{cookie:this.key,message:e.message}})}},u.prototype._setCookie=function(e){var t=c(this.ttlSeconds,this.shouldExpireAtMidnight);this.cookie.set(this.key,e,{expires:t})},u.prototype._setLocalStorage=function(e){m.canUseLocalStorage()&&window.localStorage.setItem(this.key,e)},u.prototype._getLocalStorage=function(){if(m.canUseLocalStorage())return window.localStorage.getItem(this.key)},u.prototype._removeLocalStorage=function(){m.canUseLocalStorage()&&window.localStorage.removeItem(this.key)},u.prototype.set=function(e,t){this._setCookie(e),!t&&this.shouldSync&&(this.isSessionOnly||this._setLocalStorage(e))},u.prototype.setEncoded=function(e,t){e!==decodeURIComponent(e)&&(e=decodeURIComponent(e)),this._setCookie(e);var n=encodeURIComponent(e);!t&&this.shouldSync&&(this.isSessionOnly||this._setLocalStorage(n))},u.prototype.clear=function(){this.cookie.remove(this.key),this.isSessionOnly||this._removeLocalStorage()},u.prototype.sync=function(e){if(!m.canUseLocalStorage()||!this.shouldSync)return e;var t=this._getLocalStorage(),n=e;return e?this._setLocalStorage(e):t&&!e&&(this.set(t,!0),n=t),n},u.prototype.refreshExpiryWithThrottling=function(e){var t=this;this.activeRefreshTimerId||this.hasExceededCookieMaxDuration||(this.activeRefreshTimerId=setTimeout((function(){t.hasExceededCookieMaxDuration=e&&e(t.clear.bind(t)),t.hasExceededCookieMaxDuration||t.get({resetExpiry:!0}),t.activeRefreshTimerId=null}),1e3*o.b))},l.prototype.getKey=function(){return this.key},l.prototype.get=function(){return this._getLocalStorage()},l.prototype.set=function(e){this._setLocalStorage(e)},l.prototype.clear=function(){this._removeLocalStorage()},l.prototype._setLocalStorage=function(e){m.canUseLocalStorage()&&window.localStorage.setItem(this.key,e)},l.prototype._getLocalStorage=function(){if(m.canUseLocalStorage())return window.localStorage.getItem(this.key)},l.prototype._removeLocalStorage=function(){m.canUseLocalStorage()&&window.localStorage.removeItem(this.key)},h.prototype=Object.create(u.prototype),h.prototype.constructor=h,h.prototype.exists=function(e){var t=this.get();t=t?t.split(","):[];for(var n=0;n<t.length;n++)if(e.toString()===t[n])return!0;return!1},h.prototype.add=function(e){var t=this.get();(t=t?t.split(","):[]).push(e),this.setEncoded(t.join(","))},h.prototype.remove=function(e){var t=this.get(),n=(t=t?t.split(","):[]).filter((function(t){return t!==e.toString()}));this.setEncoded(n.join(","))},h.prototype.sync=function(e){if(!m.canUseLocalStorage()||!this.shouldSync)return e;var t=window.localStorage.getItem(this.key)||"";e=e?decodeURIComponent(e).split(","):[],t=t?decodeURIComponent(t).split(","):[];var n=e.concat(t),r=n.filter((function(e,t){return n.indexOf(e)===t})).join();return r&&this.setEncoded(r),r};var d=n(35),f=null,g=null,p=null,v=function(){return window.hjSiteSettings.site_id},m={items:{ABSOLUTE_SESSION_IN_PROGRESS:new u({key:"_hjAbsoluteSessionInProgress",supportSubdomains:!0,ttlSeconds:o.h/2,shouldSync:!1,shouldExtendExpiryOnActivity:!0,shouldExpireAtMidnight:!0}),HAS_CACHED_USER_ATTRIBUTES:new u({key:"_hjHasCachedUserAttributes",ttlSeconds:0}),COOKIE_TEST:new u({key:"_hjCookieTest",ttlSeconds:0}),DEBUG_FLAG:new u({key:"hjDebug",ttlSeconds:0}),FEEDBACK_SHOW_MESSAGE:new u({key:"_hjShownFeedbackMessage",supportSubdomains:!1,ttlSeconds:o.g}),HJ_ID:new u({key:"_hjid",supportSubdomains:!0}),HJ_SESSION_USER:new u({key:"_hjSessionUser_".concat(v()),supportSubdomains:!0,shouldSync:!1}),HJ_SESSION:new u({key:"_hjSession_".concat(v()),supportSubdomains:!0,shouldSync:!1,ttlSeconds:o.h/2,shouldExtendExpiryOnActivity:!0,checkExpiry:d.b}),FIRST_SEEN:new u({key:"_hjFirstSeen",supportSubdomains:!0,shouldSync:!1,ttlSeconds:o.h/2,shouldExtendExpiryOnActivity:!0}),HUBSPOT_UTK:new u({key:"hubspotutk"}),INCLUDE_IN_SESSION_SAMPLE:new u({key:"_hjIncludedInSessionSample_".concat(v()),supportSubdomains:!0,shouldSync:!1,ttlSeconds:2*o.i,keepAliveSeconds:o.i/2}),POLL_DONE:new h({key:"_hjDonePolls",supportSubdomains:!0}),POLL_MINIMIZED:new h({key:"_hjMinimizedPolls",supportSubdomains:!0}),SESSION_RESUMED:new u({key:"_hjSessionResumed",ttlSeconds:0}),SESSION_TOO_LARGE:new u({key:"_hjSessionTooLarge",ttlSeconds:o.h}),SURVEY_INVITES_CLOSED:new h({key:"_hjClosedSurveyInvites"}),USER_ATTRIBUTES_HASH:new u({key:"_hjUserAttributesHash",supportSubdomains:!1,shouldSync:!1,ttlSeconds:2*o.i,keepAliveSeconds:o.i/2})},localStorage:{USER_ATTRIBUTES:new l({key:"_hjUserAttributes"})},areCookiesSupported:function(){return f},setCookiesSupported:function(e){f=e},canUseCookies:function(){return null===this.areCookiesSupported()&&this.setCookiesSupported(function(){try{if(!navigator.cookieEnabled)return!1;if(Object.keys(i.a.get()).length>0)return!0;if(m.items.COOKIE_TEST.set("1"),"1"===m.items.COOKIE_TEST.get())return m.items.COOKIE_TEST.clear(),!0}catch(e){return hj.metrics.count("session-rejection",{tag:{reason:"cookies"}}),!1}}()),this.areCookiesSupported()},canUseLocalStorage:hj.tryCatch((function(){if(null!==g)return g;try{localStorage.setItem("_hjLocalStorageTest",1),localStorage.removeItem("_hjLocalStorageTest"),g=!0}catch(e){g=!1}return g}),"storage.canUseLocalStorage"),canUseSessionStorage:hj.tryCatch((function(){if(null!==p)return p;try{sessionStorage.setItem("_hjSessionStorageTest",1),sessionStorage.removeItem("_hjSessionStorageTest"),p=!0}catch(e){p=!1}return p}),"storage.canUseSessionStorage")};hj.storage=m},,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n(7),i=!1,o=2,a=!1,s={isRecordingEnabled:function(){return i},setRecordingEnabled:function(e){i=e},getSelectorVersion:function(){return o},setSelectorVersion:function(e){o=e},trackSessionResumed:function(){a=!0},isResumedSession:function(){return a},isFirstSeen:function(){return"1"===r.a.items.FIRST_SEEN.get()},setFirstSeen:function(){return r.a.items.FIRST_SEEN.set("1")}}},,,,function(e,t,n){"use strict";n.d(t,"d",(function(){return o})),n.d(t,"e",(function(){return a})),n.d(t,"i",(function(){return s})),n.d(t,"j",(function(){return c})),n.d(t,"a",(function(){return u})),n.d(t,"c",(function(){return l})),n.d(t,"g",(function(){return h})),n.d(t,"h",(function(){return d})),n.d(t,"f",(function(){return f})),n.d(t,"b",(function(){return g}));var r,i=n(56),o=hj.tryCatch((function(e){var t=e||navigator.userAgent;return t.indexOf("MSIE ")>0?document.all&&!document.compatMode?5:document.all&&!window.XMLHttpRequest?6:document.all&&!document.querySelector?7:document.all&&!document.addEventListener?8:document.all&&!window.atob?9:10:-1!==t.indexOf("Trident/")?11:-1!==t.indexOf("Edge/")?12:"notIE"}),"utils"),a=(hj.tryCatch((function(e){return(e=e||navigator.userAgent).indexOf("Firefox")>-1}),"utils"),hj.tryCatch((function(e){return(e=e||navigator.userAgent).indexOf("Safari")>-1&&-1===e.indexOf("Chrome")}),"utils")),s=(hj.tryCatch((function(e){return e=e||navigator.userAgent,/\b(Safari|iPad|iPhone|iPod)\b/.test(e)&&/WebKit/.test(e)&&!/Edge/.test(e)&&void 0===window.MSStream}),"utils"),hj.tryCatch((function(e){var t,n,r;for(t=e.length-1;t>0;t-=1)n=Math.floor(Math.random()*(t+1)),r=e[t],e[t]=e[n],e[n]=r;return e}),"utils")),c=hj.tryCatch((function(e){return/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e)}),"utils"),u=hj.tryCatch((function(){return hj.userDeviceType||(hj.userDeviceType=Object(i.a)(),"mobile"===hj.userDeviceType&&(hj.userDeviceType="phone")),hj.userDeviceType}),"utils"),l=hj.tryCatch((function(){return _hjSettings.wsHost||(_hjSettings.wsHost="ws.hotjar.com"),_hjSettings.wsHost}),"utils.get-ws-server"),h=function(e){var t=!1;return function(){if(!t){t=!0;for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e.apply(null,r)}}},d=function(e){var t={};return function(n){if(!t[n]){t[n]=!0;for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];return e.apply(null,i)}}},f=function(e,t){var n={},r={};return[e,t].forEach((function(e){if(e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&"length"!==t&&(n[t]=e[t])})),Object.keys(n).sort().forEach((function(e){r[e]=n[e]})),r},g=(r=1,function(){return r++})},,,function(e,t,n){var r,i,o;function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}
/*! js-cookie v3.0.1 | MIT */o=function(){"use strict";function e(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}return function t(n,r){function i(t,i,o){if("undefined"!=typeof document){"number"==typeof(o=e({},r,o)).expires&&(o.expires=new Date(Date.now()+864e5*o.expires)),o.expires&&(o.expires=o.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var s in o)o[s]&&(a+="; "+s,!0!==o[s]&&(a+="="+o[s].split(";")[0]));return document.cookie=t+"="+n.write(i,t)+a}}return Object.create({set:i,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var t=document.cookie?document.cookie.split("; "):[],r={},i=0;i<t.length;i++){var o=t[i].split("="),a=o.slice(1).join("=");try{var s=decodeURIComponent(o[0]);if(r[s]=n.read(a,s),e===s)break}catch(e){}}return e?r[e]:r}},remove:function(t,n){i(t,"",e({},n,{expires:-1}))},withAttributes:function(n){return t(this.converter,e({},this.attributes,n))},withConverter:function(n){return t(e({},this.converter,n),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(n)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})},"object"===a(t)&&void 0!==e?e.exports=o():void 0===(i="function"==typeof(r=o)?r.call(t,n,t,e):r)||(e.exports=i)},,function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n(12),i=n(3),o=n(4),a=0,s=function(e,t,n){if(r.a.isRecordingEnabled()){var s=o.a.now();s-a>i.a.LAST_RECORDING_ACTIVITY_STORE_DEBOUNCE&&(sessionStorage.setItem("_hjRecordingLastActivity",s),a=s)}return hj.eventStream.write(e,t,n)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(115),i=Object(r.a)({pageVisitKey:void 0,pageInfo:void 0,pageContent:void 0,tagsToProcess:[],autoTagsToProcess:[],active:!1},"recording")},function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"g",(function(){return a})),n.d(t,"h",(function(){return s})),n.d(t,"c",(function(){return c})),n.d(t,"j",(function(){return u})),n.d(t,"e",(function(){return l})),n.d(t,"f",(function(){return h})),n.d(t,"i",(function(){return d}));var r="_hj_hm-retaker",i=340,o={htmlError:"retaker_html_error",ready:"retaker_ready",sendHTML:"retaker_send_html",start:"retaker_start"},a="k",s=[-1!=navigator.userAgent.indexOf("Mac")?"metaKey":"ctrlKey"],c="_hjRetakerHtmlDebug",u={button:"button",shortcut:"shortcut"},l={basicScreenshot:"basicScreenshot",dynamicElements:"dynamicElements",cookiesBanners:"cookiesBanners"},h="_hjRetakerMode",d="_hjRetakerStrings"},,function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e,t){return e.substring(0,t.length)===t}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(115),i=Object(r.a)({session:null,user:{}},"session")},,function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return j}));var r=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var i=function(e){return"string"==typeof e&&r.test(e)},o=[],a=0;a<256;++a)o.push((a+256).toString(16).substr(1));var s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase();if(!i(n))throw TypeError("Stringified UUID is invalid");return n};var c=function(e){if(!i(e))throw TypeError("Invalid UUID");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};function u(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:return t^n^r;case 2:return t&n^t&r^n&r;case 3:return t^n^r}}function l(e,t){return e<<t|e>>>32-t}var h,d=function(e,t,n){function r(e,r,i,o){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}(e)),"string"==typeof r&&(r=c(r)),16!==r.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var a=new Uint8Array(16+e.length);if(a.set(r),a.set(e,r.length),(a=n(a))[6]=15&a[6]|t,a[8]=63&a[8]|128,i){o=o||0;for(var u=0;u<16;++u)i[o+u]=a[u];return i}return s(a)}try{r.name=e}catch(e){}return r.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",r.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",r}("v5",80,(function(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof e){var r=unescape(encodeURIComponent(e));e=[];for(var i=0;i<r.length;++i)e.push(r.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var o=e.length/4+2,a=Math.ceil(o/16),s=new Array(a),c=0;c<a;++c){for(var h=new Uint32Array(16),d=0;d<16;++d)h[d]=e[64*c+4*d]<<24|e[64*c+4*d+1]<<16|e[64*c+4*d+2]<<8|e[64*c+4*d+3];s[c]=h}s[a-1][14]=8*(e.length-1)/Math.pow(2,32),s[a-1][14]=Math.floor(s[a-1][14]),s[a-1][15]=8*(e.length-1)&4294967295;for(var f=0;f<a;++f){for(var g=new Uint32Array(80),p=0;p<16;++p)g[p]=s[f][p];for(var v=16;v<80;++v)g[v]=l(g[v-3]^g[v-8]^g[v-14]^g[v-16],1);for(var m=n[0],y=n[1],j=n[2],b=n[3],w=n[4],S=0;S<80;++S){var _=Math.floor(S/20),E=l(m,5)+u(_,y,j,b)+w+t[_]+g[S]>>>0;w=b,b=j,j=l(y,30)>>>0,y=m,m=E}n[0]=n[0]+m>>>0,n[1]=n[1]+y>>>0,n[2]=n[2]+j>>>0,n[3]=n[3]+b>>>0,n[4]=n[4]+w>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]})),f=new Uint8Array(16);function g(){if(!h&&!(h="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return h(f)}var p=function(e,t,n){var r=(e=e||{}).random||(e.rng||g)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var i=0;i<16;++i)t[n+i]=r[i];return t}return s(r)},v="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof window.msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto),m=new Array(16),y=v?void 0:function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),m[t]=e>>>((3&t)<<3)&255;return m},j=d,b=function(e,t,n){return y&&((e=e||{}).rng=y),p(e,t,n)}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=Object.freeze({POPOVER:"popover",FULL_SCREEN:"full_screen",EXTERNAL:"external_link",BUTTON:"button",INLINE_EMBEDDED:"inline"})},,,function(e,t,n){"use strict";n.r(t),n.d(t,"getParameter",(function(){return a})),n.d(t,"tryDecodeURIComponent",(function(){return s})),n.d(t,"getUrlFromString",(function(){return c})),n.d(t,"getMidLevelDomain",(function(){return l}));var r=n(19),i=n.n(r),o=n(25);function a(e){var t,n,r=[];for(t=new RegExp("[^?&]?"+e.replace(/\[/,"\\[").replace(/]/,"\\]")+"=([^&]+)","g");n=t.exec(location.search);)r.push(s(n[1]));switch(r.length){case 0:return"";case 1:return r[0];default:return r}}function s(e){try{return decodeURIComponent(e)}catch(t){return e}}function c(e){return Object(o.a)(e,"http")||(Object(o.a)(e,"/")||(e="/"+e),e=location.protocol+"//"+location.hostname+(""!=location.port?":"+location.port:"")+e),e}var u={};function l(e){if(!u[e]){var t,n=e.lastIndexOf(".");t=function e(t,n){n=n?n-1:t.length;var r,i=t.lastIndexOf(".",n-1);i>-1&&(function(e){try{var t={domain:e};h.set("_hjTLDTest",e,t);var n=h.get("_hjTLDTest");return n&&h.remove("_hjTLDTest",t),n}catch(e){return!1}}(r=t.substring(i))||(r=e(t,i)));return r}(e,n),u[e]=t||e}return u[e]}var h=i.a.withAttributes({sameSite:"None",secure:!0})},function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return s}));var r=n(4),i=n(3),o=4*i.h*1e3,a=i.h/2*1e3,s=function(e){try{var t=hj.store.session.get("session.created");if("number"==typeof t&&r.a.now()+a>t+o){var n=o-(r.a.now()-t);n<0&&(n=0),setTimeout((function(){try{hj.eventStream.close(),e(),hj.store.session.set("session",null),hj.metrics.count("session-interruption",{tag:{reason:"too-long"}})}catch(e){hj.exceptions.log(e,"hj.session.clear")}}),n)}return"number"==typeof t&&r.a.now()>=t+o}catch(e){return!1}}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"d",(function(){return s}));var r="_hj-widget-container",i="_hj-widget-iframe";function o(e,t){for(var n=e.querySelectorAll(t),r=0;r<n.length;r++){var i=n[r];i&&i.parentElement&&i.parentElement.removeChild(i)}}function a(e){o(e,".".concat(r))}function s(e){o(e,".".concat(i))}},,,,,,,,function(e,t,n){"use strict";var r,i;function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return m}));var a=Object.freeze({LIVE:"LIVE",REVIEW_WEBAPP:"REVIEW_WEBAPP",REVIEW:"REVIEW",STAGING:"STAGING",DEV:"DEV",DEV_OLD:"DEV_OLD"}),s=function(){var e=document.location.host.match(/^(insights-webapp|surveys-webapp|insights|surveys)-(.*?)((?:\.[^.]+)?(?:\.hotjarians\.net)|(?:\.[^.]+)?(?:\.eks\.hotjar\.com))$/);return e&&{component:e[1],reviewId:e[2],domain:e[3],reviewUrlSuffix:e[2]+e[3]}},c=null===(r=s())||void 0===r?void 0:r.reviewUrlSuffix,u=Object.freeze((o(i={},a.LIVE,{INSIGHTS:"insights.hotjar.com",SURVEYS:"surveys.hotjar.com"}),o(i,a.REVIEW,{INSIGHTS:"insights-".concat(c),SURVEYS:"surveys-".concat(c)}),o(i,a.REVIEW_WEBAPP,{INSIGHTS:"insights-webapp-".concat(c),SURVEYS:"surveys-webapp-".concat(c)}),o(i,a.STAGING,{INSIGHTS:"insights-staging.hotjar.com",SURVEYS:"surveys-staging.hotjar.com"}),o(i,a.DEV,{INSIGHTS:"local.hotjar.com:8443",SURVEYS:"surveys.local.hotjar.com:8443"}),o(i,a.DEV_OLD,{INSIGHTS:"local.hotjar.com",SURVEYS:"surveys.local.hotjar.com"}),i)),l=function(e){return function(t,n){return t===u[e][n]}},h=l(a.DEV),d=l(a.DEV_OLD),f=l(a.LIVE),g=l(a.REVIEW_WEBAPP),p=l(a.REVIEW),v=l(a.STAGING),m=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"INSIGHTS",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.location.host;return f(t,e)?a.LIVE:h(t,e)?a.DEV:d(t,e)?a.DEV_OLD:g(t,e)?a.REVIEW_WEBAPP:p(t,e)?a.REVIEW:v(t,e)?a.STAGING:a.LIVE}},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"d",(function(){return s})),n.d(t,"f",(function(){return c})),n.d(t,"b",(function(){return u})),n.d(t,"e",(function(){return l}));var r=n(92),i=n(29);function o(e,t,n){!function i(o){if(o>0&&e(),o>=n.maxRetries)t&&t();else{var a=0===o&&n.firstAttemptDelay?n.firstAttemptDelay:n.delay*Math.pow(n.baseExponent||3,o);setTimeout((function(){!0!==r.a.get("eventsRetrySuccess")&&i(o+1)}),a)}}(0)}var a=function(){return Object(i.a)()},s=function(e){return Object(i.b)(e,"ded6f544-7265-46bb-ab52-fefac2598466")},c=function(e){return JSON.stringify(e)},u=function(e){return JSON.parse(e)},l=function(){var e;return null===(e=window.navigator)||void 0===e?void 0:e.userAgent}},,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(3),i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s,r=0,i=!1;function c(){0!==--r||i||t.bind(this)()}function u(){i=!0,n.bind(this)()}Object.keys(e).forEach((function(t){var n=e[t];"string"==typeof n&&(n=[n]),r+=n.length,n.forEach((function(e){o(e,t,c,u)}))}))},o=function(e,t,n,i){var o;t===r.d.SCRIPT?(o=document.createElement("script")).src="".concat(hj.scriptDomain).concat(e):t===r.d.STYLESHEET&&((o=document.createElement("link")).href="".concat(hj.scriptDomain).concat(e),o.rel="stylesheet"),o.onload=n,o.onerror=i,document.getElementsByTagName("head")[0].appendChild(o)};function a(){}function s(){var e=this.src||this.href;hj.exceptions.log(new Error("Failed to load module: ".concat(e,".")),"loader")}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r,i,o=((i=function(){return r()}).test=r=function(){var e;if(!navigator)return"No User-Agent Provided";if(null===(e=navigator.userAgentData)||void 0===e?void 0:e.mobile)return"mobile";var t=function(e){return navigator.userAgent.match(e)};return t(/GoogleTV|SmartTV|Internet.TV|NetCast|NETTV|AppleTV|boxee|Kylo|Roku|DLNADOC|CE\-HTML/i)||t(/Xbox|PLAYSTATION.3|Wii/i)?"tv":t(/iPad/i)||t(/tablet/i)&&!t(/RX-34/i)||t(/FOLIO/i)||t(/Linux/i)&&t(/Android/i)&&!t(/Fennec|mobi|HTC.Magic|HTCX06HT|Nexus.One|SC-02B|fone.945|Chromebook/i)||t(/Kindle/i)||t(/Mac.OS/i)&&t(/Silk/i)||t(/GT-P10|SC-01C|SHW-M180S|SGH-T849|SCH-I800|SHW-M180L|SPH-P100|SGH-I987|zt180|HTC(.Flyer|\_Flyer)|Sprint.ATP51|ViewPad7|pandigital(sprnova|nova)|Ideos.S7|Dell.Streak.7|Advent.Vega|A101IT|A70BHT|MID7015|Next2|nook/i)||t(/MB511/i)&&t(/RUTEM/i)?"tablet":t(/BOLT|Fennec|Iris|Maemo|Minimo|Mobi|mowser|NetFront|Novarra|Prism|RX-34|Skyfire|Tear|XV6875|XV6975|Google.Wireless.Transcoder/i)||t(/Opera/i)&&t(/Windows.NT.5/i)&&t(/HTC|Xda|Mini|Vario|SAMSUNG\-GT\-i8000|SAMSUNG\-SGH\-i9/i)?"mobile":t(/Windows.(NT|XP|ME|9)/)&&!t(/Phone/i)||t(/Win(9|.9|NT)/i)||t(/Macintosh|PowerPC/i)&&!t(/Silk/i)||t(/Linux/i)&&t(/X11/i)||t(/Solaris|SunOS|BSD/i)||t(/Bot|Crawler|Spider|Yahoo|ia_archiver|Covario-IDS|findlinks|DataparkSearch|larbin|Mediapartners-Google|NG-Search|Snappy|Teoma|Jeeves|TinEye/i)&&!t(/Mobile/i)||t(/\b(CrOS|Chromebook)\b/i)?"desktop":"mobile"},i)},,function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=Object.freeze({MODAL:"_hj-modal",FOOTER:"_hj-footer",SURVEY_INVITES:"_hj_survey_invite_container",HEATMAP_RETAKER:"_hj-heatmap-retaker",ADMIN_WIDGET:"_hj_admin_widget",INCOMING_FEEDBACK:"_hj_feedback_container",NOTICATION:"_hj-notification"});Object.freeze({RETAKER:"_hjRetakerTrsToken",TARGETING:"_hjRetakerTargeting"})},,function(e,t,n){"use strict";n.r(t),n.d(t,"userAttributes",(function(){return l}));var r=n(67),i=n(21),o=n(12),a=n(4),s=n(71),c=n(7),u=n(16),l={id:void 0,attributes:{},init:hj.tryCatch((function(){if(hj.settings.user_attributes_enabled){var e=g();e&&(l.id=e.userId,l.attributes=e.attributes)}}),"userAttributes.init"),reset:hj.tryCatch((function(){l.id=void 0,l.attributes={},p()}),"userAttributes.reset"),set:hj.tryCatch((function(e,t){hj.settings.user_attributes_enabled?(void 0!==l.id&&null!==l.id&&l.id!==e&&(hj.log.debug("User ID changed, resetting all attributes before continuing.","userAttributes"),l.reset()),l.id=e,l.attributes=Object(u.f)(l.attributes,t),f({attributes:l.attributes,userId:l.id}),hj.event.signal("user-attributes-set"),o.a.isRecordingEnabled()?l.flush():hj.log.debug("No recording in progress. Not sending.","userAttributes")):hj.log.debug("User attributes not enabled. Doing nothing.","userAttributes")}),"userAttributes.set"),flush:hj.tryCatch((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:hj.hq.noop;if(void 0!==l.id||Object.keys(l.attributes).length){var t=c.a.items.USER_ATTRIBUTES_HASH.get({resetExpiry:!0}),n=hj.md5(JSON.stringify({allAttributes:l.attributes,userId:l.id}));if(t===n)return e(null,l.id,l.attributes),void hj.log.debug("No changed user attributes. Not sending.","userAttributes");c.a.items.USER_ATTRIBUTES_HASH.set(n),hj.debug.isOn()&&h(l.id,l.attributes),d(l.id,l.attributes),e(null,l.id,l.attributes)}else e(Error("no_user"))}),"userAttributes.flush"),get:hj.tryCatch((function(e){return"user_id"===e?l.id:l.attributes[e]}),"userAttributes.get")},h=function(e,t){hj.ajax.post("".concat(hj.identifyEndpoint,"/sites/").concat(hj.settings.site_id,"/users/").concat(Object(s.a)(!0),"/validate"),{user_id:e,attributes:t},hj.tryCatch((function(e){e.errors&&Object.keys(e.errors).length>0?hj.log.debug("User validation API call PARTIALLY successful (some errors).","userAttributes",e):hj.log.debug("User validation API call successful.","userAttributes",e)}),"userAttributes"),hj.tryCatch((function(e){hj.log.debug("User Attributes validation API call failed.","userAttributes",e.responseText&&JSON.parse(e.responseText)||"unknown_failure")}),"userAttributes"))},d=function(e,t){if(o.a.isRecordingEnabled()){var n={hj_id:Object(s.a)(!0),user_id:e,attributes:t,time:hj.time.getNow(),timestamp:a.a.now()};Object(i.a)(r.a.IDENTIFY_USER,n,!0).flush(),hj.log.debug("User attributes sent up websocket.","userAttributes",n)}},f=function(e){var t=hj.b64EncodeUnicode(JSON.stringify(e));c.a.localStorage.USER_ATTRIBUTES.set(t),c.a.items.HAS_CACHED_USER_ATTRIBUTES.set("true",!0)},g=function(){var e="true"===c.a.items.HAS_CACHED_USER_ATTRIBUTES.get()?c.a.localStorage.USER_ATTRIBUTES.get():void 0;if(e)try{var t=hj.isParsableJSON(e)?e:hj.b64DecodeUnicode(e);return JSON.parse(t)}catch(e){return}},p=function(){c.a.items.HAS_CACHED_USER_ATTRIBUTES.clear(),c.a.localStorage.USER_ATTRIBUTES.clear()}},,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=Object.freeze({IDENTIFY_USER:"identify_user",AUTOTAG_RECORDING:"autotag_recording",TAG_RECORDING:"tag_recording",HEATMAP_HELO:"heatmap_helo",RECORDING_HELO:"recording_helo",REPORT_USER_ID:"report_user_id",MUTATION:"mutation",MOUSE_CLICK:"mouse_click",INPUT_CHOICE_CHANGE:"input_choice_change",KEY_PRESS:"key_press",MOUSE_MOVE:"mouse_move",RELATIVE_MOUSE_MOVE:"relative_mouse_move",CLIPBOARD:"clipboard",PAGE_VISIBILITY:"page_visibility",SCROLL_REACH:"scroll_reach",SCROLL:"scroll",SELECT_CHANGE:"select_change",VIEWPORT_RESIZE:"viewport_resize",SCRIPT_PERFORMANCE:"script_performance",REPORT_CONTENT:"report_content",INSERTED_RULE:"inserted_rule",DELETED_RULE:"deleted_rule"})},,,,function(e,t,n){"use strict";n.d(t,"b",(function(){return f})),n.d(t,"a",(function(){return g})),n.d(t,"c",(function(){return p}));var r=n(7),i=n(4),o=n(27),a=n(136),s=n(48);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var h=Object(a.a)({storageAccessor:r.a.items.HJ_SESSION_USER,serializer:s.f,deserializer:s.b}),d=function(){return h.get()},f=function(){var e=h.getKey();if(e&&!(e.indexOf("undefined")>-1)){var t,n,o,a,c,l=d(),f=(t=u(u(u({},l),(c=r.a.items.HJ_ID.get())?{id:Object(s.d)(hjSiteSettings.site_id+c),existing:!0}:null),{},{existing:!!l}),{id:null!==(n=null==t?void 0:t.id)&&void 0!==n?n:Object(s.d)(Object(s.c)()),created:null!==(o=null==t?void 0:t.created)&&void 0!==o?o:i.a.now(),existing:null!==(a=null==t?void 0:t.existing)&&void 0!==a&&a});return function(e){h.set(e)}(f),f}},g=function(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=o.a.get("user.id")||(null===(e=d())||void 0===e?void 0:e.id);if(n)return n;if(t){var r=f();return null==r?void 0:r.id}},p=function(){if(Object(s.e)()){var e,t,n,r=o.a.get("user")||d();if((null===(e=r)||void 0===e?void 0:e.id)||(r=f()),null===(t=r)||void 0===t?void 0:t.id)setTimeout((function(){o.a.set("user",r)}));else hj.log.warnIfEmpty(null===(n=r)||void 0===n?void 0:n.id,"user agent check: userId")}}},,,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r,i=n(7),o=Math.random(),a=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";if(void 0!==r)return hj.log.debug("[".concat(e,"] Included in session/CC sample (already set)? ").concat(r),"sampling",{rec_value:hj.settings.rec_value,includedInSessionSample:r,mathRandomResult:o}),r;if(0===hj.settings.rec_value)return i.a.items.INCLUDE_IN_SESSION_SAMPLE.set("0"),hj.log.debug("[".concat(e,"] rec_value has gone to 0, removing from sample."),"sampling",{rec_value:hj.settings.rec_value,includedInSessionSample:r}),r=!1;if(!hj.settings.continuous_capture_enabled)return hj.log.debug("[".concat(e,"] Included in session/CC sample (setting check)? ").concat(r),"sampling",{"hj.settings.continuous_capture_enabled":hj.settings.continuous_capture_enabled}),r=!1;var t=i.a.items.INCLUDE_IN_SESSION_SAMPLE.get({resetExpiry:!0}),n="1"==t;if(hj.log.debug("[".concat(e,"] Included in session/CC sample (from cookie)? ").concat(n),"sampling",{cookie_value:t}),t)return r=n;var a=hj.url.getParameter("hjIncludeInSessionSample");switch(a){case"0":return r=!1,hj.log.debug("You have set includedInSessionSample to false.","sampling"),r;case"1":return r=!0,i.a.items.INCLUDE_IN_SESSION_SAMPLE.set(r?"1":"0"),hj.log.debug("You have set includedInSessionSample to true.","sampling"),r}return(r=1===hj.settings.rec_value||void 0!==hj.settings.rec_value&&null!==hj.settings.rec_value&&(o||1)<=hj.settings.rec_value)?i.a.items.INCLUDE_IN_SESSION_SAMPLE.set("1"):i.a.items.INCLUDE_IN_SESSION_SAMPLE.set("0"),hj.log.debug("[".concat(e,"] Included in session/CC sample? ").concat(r),"sampling",{rec_value:hj.settings.rec_value,mathRandomResult:o}),r},s=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";return a(e)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(55),i=n(3),o=!1,a=function(e){var t=e.title,n=e.message,a=e.status;o?hj.widget.renderNotificationWidget({title:t,message:n,status:a}):Object(r.a)(i.f.NOTIFICATION,(function(){o=!0,hj.widget.renderNotificationWidget({title:t,message:n,status:a})}))}},,,,,,,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(115),i=Object(r.a)({eventsRetrySuccess:!1},"event-stream")},function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return s}));var r=n(4);function i(e){return Object(r.g)(Object(r.e)(e))}function o(e){return Object(r.d)(Object(r.f)(e))}function a(e){return Object(r.c)(i(e))}function s(e){return o(Object(r.b)(e))}},,,,,,,,,,,,,function(e,t,n){"use strict";function r(e,t,n){t=t||hj.hq.noop,n=n||hj.hq.noop,hj.hq.ajax({url:e,success:hj.tryCatch(t,"Data"),error:hj.tryCatch(n,"Data")})}function i(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{contentType:"json"};n=n||hj.hq.noop,r=r||hj.hq.noop;var o=(null==i?void 0:i.query)?"".concat(e,"?").concat(i.query):e;return hj.hq.ajax({url:o,type:"POST",data:"json"===(null==i?void 0:i.contentType)?hj.hq.stringify(t):t,contentType:"text/plain; charset=UTF-8",success:hj.tryCatch(n,"Data"),error:hj.tryCatch(r,"Data")})}function o(e,t,n,r){n=n||hj.hq.noop,r=r||hj.hq.noop,hj.hq.ajax({url:e,type:"PUT",data:hj.hq.stringify(t),contentType:"text/plain; charset=UTF-8",success:hj.tryCatch(n,"Data"),error:hj.tryCatch(r,"Data")})}n.r(t),n.d(t,"get",(function(){return r})),n.d(t,"post",(function(){return i})),n.d(t,"putAsJSON",(function(){return o})),hj.ajax={get:r,post:i,putAsJSON:o}},,,,,,,,,function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}n.d(t,"a",(function(){return i}));var i=function(e,t){var n=Object.assign(Object.create({}),e),i=Object.create({}),o=function(e){return!(null==e||"object"===r(e)&&0===Object.keys(e).length)},a=Object.create({get:function(e){try{if(Object.prototype.hasOwnProperty.call(n,e))return n[e];var i=e.split(".");if(i.length>1){var o=i[1],a=n[i[0]];return o&&a&&"object"===r(a)?a[o]:void 0}return}catch(e){hj.exceptions.log(e,"hj.store.".concat(t))}},set:function(e,s){try{if(Object.prototype.hasOwnProperty.call(n,e))n[e]=s,s&&"object"===r(s)&&Object.keys(s).forEach((function(t){var n="".concat(String(e),".").concat(t),r=i[n];if(r&&s){var c=s[t];o(c)&&r.forEach((function(e){e(c,a.off(n,e))}))}}));else{var c=e.split(".");if(c.length>1){var u=c[0],l=n[u],h=c[1];h&&l&&"object"===r(l)&&(l[h]=s);var d,f=a.get(u);if(o(f))null===(d=i[u])||void 0===d||d.forEach((function(e){e(f,a.off(u,e))}))}}var g;if(o(s))null===(g=i[e])||void 0===g||g.forEach((function(t){t(s,a.off(e,t))}))}catch(e){hj.exceptions.log(e,"hj.store.".concat(t))}},on:function(e,t){var n;(null===(n=i[e])||void 0===n?void 0:n.push(t))||(i[e]=[t]);var r=a.get(e);return o(r)&&t(r,a.off(e,t)),a.off(e,t)},off:function(e,t){return function(){var n;i[e]=null===(n=i[e])||void 0===n?void 0:n.filter((function(e){return e!==t}))}},remove:function(e){delete n[e],delete i[e]},reset:function(e){n=Object.assign(Object.create({}),e),i=Object.create({})}});return a}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return Y}));var r,i,o=n(67),a=n(12),s=n(21),c=n(4),u={setup:!1,listen:hj.tryCatch((function(e){u.setup?e&&u.send(o.a.INSERTED_RULE,hj.insertedRules.getCurrentInsertedRules()):(hj.insertedRules.init(),hj.insertedRules.register(u.send.bind(u,o.a.INSERTED_RULE),!0),hj.cssBlobs.register(u.send.bind(u,o.a.INSERTED_RULE)),hj.deletedRules.init(),hj.deletedRules.register(u.send.bind(u,o.a.DELETED_RULE)),u.setup=!0)}),"behavior-data.cssRules.listen"),send:hj.tryCatch((function(e,t){t.length&&setTimeout(hj.tryCatch((function(){var n={time:hj.time.getNow(),timestamp:c.a.now(),rules:t};hj.debug.emit(e,n),Object(s.a)(e,n,!1)}),"behavior-data.cssRules"))}),"behavior-data.cssRules.send")},l=hj.tryCatch((function(e,t){var n=hj.hq(e),r=hj.selector(a.a.getSelectorVersion()).get(n);if(r&&t){var i={offset_x:t.pageX,offset_y:t.pageY,selector:r};a.a.isRecordingEnabled()&&(i.time=hj.time.getNow(),i.timestamp=c.a.now()),Object(s.a)(o.a.MOUSE_CLICK,i,!1).flush()}}),"behavior-data.frameMouseClicks.frameClick"),h={setup:!1,listen:hj.tryCatch((function(){h.setup||(hj.log.debug("Setting up frame mouse click listeners.","events"),h.send(),h.setup=!0)}),"behavior-data.frameMouseClicks.listen"),send:hj.tryCatch((function(){if(a.a.isRecordingEnabled()){var e=location.origin,t=hj.hq("iframe");t.length&&[].forEach.call(t,(function(t){if(t.src&&-1!==t.src.indexOf(e))try{var n=t.contentWindow;hj.hq(n).on("mousedown",(function(e){l(t,e)}))}catch(n){var r=t.src.split("?")[0];hj.Sentry&&hj.Sentry.captureMessage("SecurityError: Blocked a frame with origin ".concat(e," from accessing\n                                a frame with origin ").concat(r))}}))}}),"behavior-data.frameMouseClicks.send")},d={setup:!1,listen:hj.tryCatch((function(){d.setup||(hj.log.debug("Setting up input choice change listeners.","events"),hj.hq(document).on("change","input[type=checkbox], input[type=radio]",d.send),hj.event.listen("shadow-event:change",d.send),d.setup=!0)}),"behavior-data.inputChoiceChange.listen"),send:hj.tryCatch((function(e){if("INPUT"===e.target.tagName&&("checkbox"===e.target.type||"radio"===e.target.type)&&a.a.isRecordingEnabled()){var t=hj.hq(e.target),n=hj.selector().get(t),r=t.is(":checked");Object(s.a)(o.a.INPUT_CHOICE_CHANGE,{value:r,selector:n,time:hj.time.getNow(),timestamp:c.a.now()},!0).flush()}}),"behavior-data.inputChoiceChange.send")},f=!1,g=!1,p=[],v={setup:!1,listen:hj.tryCatch((function(){v.setup||(hj.log.debug("Setting up key press listeners.","events"),hj.hq(document).on("input","input",v.update),hj.hq(document).on("blur","input",v.send),hj.hq(document).on("input","textarea",v.update),hj.hq(document).on("blur","textarea",v.send),v.setup=!0)}),"behavior-data.keyPress.listen"),update:hj.tryCatch((function(e){var t=hj.hq(e.target),n=t.val();g=g||hj.privacy.isRiskyNotAllowlistedOrSuppressedElement(e.target),p.push({time:hj.time.getNow(),timestamp:c.a.now(),selector:hj.selector().get(t),text:n,type:e.target.type,suppression:g?"full":"none"}),f=!0}),"behavior-data.keyPress.update"),send:hj.tryCatch((function(){if(a.a.isRecordingEnabled()&&f){if(g){var e=p[0],t=p[p.length-1],n=hj.privacy.getSuppressedText(e.type,t.text),r=Math.floor((t.time-e.time)/Math.max(n.length,1));p=[];for(var i=0;i<Math.min(n.length-1,500);i++)p.push({time:e.time+r*i,timestamp:e.timestamp+r*i,selector:e.selector,text:n.substring(0,i+1),type:e.type,suppression:"full"});t.text=n,p.push(t)}hj.hq.each(p,(function(e,t){delete t.type})),Object(s.a)(o.a.KEY_PRESS,p,!0).flush(),f=!1,g=!1,p=[]}}),"behavior-data.keyPress.send")},m=0,y=0,j=!1,b=0,w=0,S=null,_={setup:!1,listen:hj.tryCatch((function(){_.setup||(hj.log.debug("Setting up mouse move listeners.","events"),hj.hq(document).on("mousemove",_.update),setInterval(_.send,100),_.setup=!0)}),"behavior-data.mouseMove.listen"),update:hj.tryCatch((function(e){m=e.clientX,y=e.clientY;var t=hj.hq(document.elementFromPoint(m,y));if(t[0]){var n=t.offset();n&&(b=e.pageX-parseInt(n.left,10),w=e.pageY-parseInt(n.top,10),S=hj.selector(a.a.getSelectorVersion()).get(t),j=!0)}}),"behavior-data.mouseMove.update"),send:hj.tryCatch((function(){a.a.isRecordingEnabled()&&j&&(a.a.isRecordingEnabled()&&(Object(s.a)(o.a.MOUSE_MOVE,{time:hj.time.getNow(),timestamp:c.a.now(),x:m,y:y}),Object(s.a)(o.a.RELATIVE_MOUSE_MOVE,{time:hj.time.getNow(),timestamp:c.a.now(),offset_x:b,offset_y:w,selector:S})),j=!1)}),"behavior-data.mouseMove.send")},E=!1,C={setup:!1,listen:hj.tryCatch((function(){C.setup||(hj.log.debug("Setting up mouse click listeners.","events"),hj.hq(document).on("mousedown",C.send),C.setup=!0)}),"behavior-data.mouseClick.listen"),send:hj.tryCatch((function(e){var t,n;if(a.a.isRecordingEnabled()){E&&(_.update(e),E=!1);var r=(null==e||null===(t=e.composedPath)||void 0===t||null===(n=t.call(e))||void 0===n?void 0:n.length)?e.composedPath()[0]:e.target,i=hj.selector(a.a.getSelectorVersion()).get(hj.hq(r)),u=hj.fullSelector(a.a.getSelectorVersion()).get(hj.hq(r)),l=[];if(!!r&&"pageX"in e&&"pageY"in e&&void 0!==i){var h=hj.hq(r).offset();l.left=e.pageX-h.left,l.top=e.pageY-h.top;var d={offset_x:l.left,offset_y:l.top,selector:i,full_selector:u,page_x:e.pageX,page_y:e.pageY,text:null,time:hj.time.getNow(),timestamp:c.a.now()},f=0,g=!1,p=r;do{if("BUTTON"===p.tagName||"A"===p.tagName){g=!0;break}f++,p=p.parentNode}while(p&&(f<=2||g));if(g){var v=hj.privacy.getSuppressedTextNode(r),m=v.content;!v.shouldSuppressNode&&m&&(d.text=m.trim().substring(0,100))}Object(s.a)(o.a.MOUSE_CLICK,d,!1).flush()}}}),"behavior-data.mouseClick.send"),trackCoordinatesOnNextClick:function(){E=!0}},O=hj.tryCatch((function(e){var t={},n=!1;return t.listen=hj.tryCatch((function(){n||(hj.log.debug("Setting up "+e+" event listener.","events"),hj.hq(document).on(e,t.send),hj.event.listen("shadow-event:".concat(e),t.send),n=!0)}),"behavior-data."+e+".listen"),t.send=hj.tryCatch((function(){a.a.isRecordingEnabled()&&Object(s.a)(o.a.CLIPBOARD,{time:hj.time.getNow(),timestamp:c.a.now(),action:e},!0).flush()}),"behavior-data."+e+".send"),t}),"behavior-data.newClipboardEventListener"),I={listen:hj.tryCatch((function(){document.addEventListener("visibilitychange",(function(){return I.send(!document.hidden)}),!1)}),"behavior-data.pageVisibility.listen"),send:hj.tryCatch((function(e){var t={time:hj.time.getNow(),timestamp:c.a.now(),page_visible:e};hj.debug.emit("page_visibility",t),Object(s.a)(o.a.PAGE_VISIBILITY,t,!1).flush()}),"behavior-data.pageVisibility.send")},N=!1,T={setup:!1,listen:hj.tryCatch((function(){T.setup||(hj.log.debug("Setting up scroll listeners.","events"),window.addEventListener("scroll",T.update,!0),hj.event.listen("shadow-event:scroll",T.update),setInterval(T.send,100),0!==hj.ui.getScrollPosition().top&&T.update(),T.setup=!0)}),"behavior-data.scroll.listen"),update:hj.tryCatch((function(e){r=e?e.target===window.document?window:e.target:window,N=!0}),"behavior-data.scroll.update"),send:hj.tryCatch((function(){if(a.a.isRecordingEnabled()&&N){var e=(r=r||window)===window?"window":hj.selector(a.a.getSelectorVersion()).get(hj.hq(r))||"window",t=hj.ui.getScrollPosition(r);Object(s.a)(o.a.SCROLL,{elem:e,time:hj.time.getNow(),timestamp:c.a.now(),x:t.left,y:t.top}),N=!1}}),"behavior-data.scroll.send")},k=0,R={setup:!1,listen:hj.tryCatch((function(){R.setup||(hj.log.debug("Setting up scroll reach listeners.","events"),hj.hq(window).on("scroll resize",R.send,!0),R.setup=!0)}),"behavior-data.scrollReach.listen"),send:hj.tryCatch((function(){if(a.a.isRecordingEnabled()){var e=hj.ui.getBottomAsPercentage();e>k&&(k=e,Object(s.a)(o.a.SCROLL_REACH,{time:hj.time.getNow(),timestamp:c.a.now(),max_bottom:k},!0))}}),"behavior-data.scrollReach.send")},A={setup:!1,listen:hj.tryCatch((function(){A.setup||(hj.log.debug("Setting up select change listeners.","events"),hj.hq(document).on("change","select",A.send),hj.event.listen("shadow-event:change",A.send),A.setup=!0)}),"behavior-data.selectChange.listen"),send:hj.tryCatch((function(e){if("SELECT"===e.target.tagName&&a.a.isRecordingEnabled()){var t=hj.hq(e.target),n=hj.selector().get(t),r=t.val();Object(s.a)(o.a.SELECT_CHANGE,{value:r,selector:n,time:hj.time.getNow(),timestamp:c.a.now()},!0).flush()}}),"behavior-data.selectChange.send")},x={setup:!1,listen:hj.tryCatch((function(){x.setup||(hj.log.debug("Setting up screen size change listeners.","events"),i=hj.ui.getWindowSize(),setInterval(x.checkResize,1e3),x.checkResize(),x.setup=!0)}),"behavior-data.viewportResize.listen"),checkResize:hj.tryCatch((function(){var e=hj.ui.getWindowSize();e.width===i.width&&e.height===i.height||(i=e,x.send())}),"behavior-data.viewportResize.checkResize"),send:hj.tryCatch((function(){a.a.isRecordingEnabled()&&Object(s.a)(o.a.VIEWPORT_RESIZE,{time:hj.time.getNow(),timestamp:c.a.now(),w:i.width,h:i.height}).flush()}),"behavior-data.viewportResize.send")},P={setup:!1,listen:hj.tryCatch((function(){P.setup||(hj.adoptedStyleSheets.init(),hj.adoptedStyleSheets.register(P.send.bind(P,"adopted_style_sheets"),!0),P.setup=!0)}),"behavior-data.adoptedStyleSheets.listen"),send:hj.tryCatch((function(e,t){t&&setTimeout(hj.tryCatch((function(){var n={time:hj.time.getNow(),timestamp:c.a.now(),parentSelector:t.parentSelector,sheets:t.sheets,nodeId:t.nodeId};hj.debug.emit(e,n),Object(s.a)(e,n,!1)}),"behavior-data.adoptedStyleSheets"))}),"behavior-data.adoptedStyleSheets.send")},D=O("copy"),M=O("cut"),L=O("paste"),U=[P,D,u,M,h,d,v,C,_,I,L,T,R,A,x],H={enableRecording:hj.tryCatch((function(e){a.a.setRecordingEnabled(!0),U.forEach((function(t){t.listen(e)})),hj.autotag.start()}),"behavior-data.events.enableRecording")},q=n(60),V=n(16),W=n(7),z=n(27),B=n(22);var F=!1,G=Object(V.h)((function(e,t,n,r,i){var c,u,l;Object(s.a)((c={},u=o.a.RECORDING_HELO,l=function(){return function(e,t,n){return{playback_version:3,script_context_id:hj.scriptContextId,start_time:t,start_timestamp:n,page_visit_info:e,resumed:a.a.isResumedSession(),first_seen:a.a.isFirstSeen()}}(hj.visitData.getPageVisitInfo(t,hj.settings.site_id),n,r)},u in c?Object.defineProperty(c,u,{value:l,enumerable:!0,configurable:!0,writable:!0}):c[u]=l,c)).flush(),hj.eventStream.reportPageContent(e),Object(s.a)(o.a.SCROLL_REACH,{max_bottom:hj.ui.getBottomAsPercentage()},!0),i?H.enableRecording():B.a.on("pageInfo",(function(e,t){Y.initializeTreeMirror(e.urlMD5),H.enableRecording(W.a.items.SESSION_RESUMED.get()),t()}))})),Y={isTreeMirrorInitialized:function(){return F},start:hj.tryCatch((function(){a.a.setRecordingEnabled(!0),sessionStorage.setItem("_hjRecordingEnabled",!0),sessionStorage.setItem("_hjRecordingLastActivity",c.a.now());var e=hj.ui.getWindowSize(),t=hj.time.getNow(),n=c.a.now(),r=B.a.get("pageVisitKey");G(r,r,e,t,n,this.isTreeMirrorInitialized()),B.a.set("active",!0);var i=B.a.get("tagsToProcess");i.length&&(Object(s.a)(o.a.TAG_RECORDING,i,!0).flush(),B.a.set("tagsToProcess",[]));var u=B.a.get("autoTagsToProcess");u.length&&(Object(s.a)(o.a.AUTOTAG_RECORDING,u,!0).flush(),B.a.set("autoTagsToProcess",[])),hj.settings.user_attributes_enabled&&q.userAttributes.flush()}),"behavior-data.recording.start"),setAndSendPageContent:function(e,t){z.a.on("user.id",(function(){return hj.eventStream.storePageContent(t,e)}))},reset:function(){B.a.reset({pageVisitKey:void 0,pageInfo:void 0,pageContent:void 0,tagsToProcess:[],autoTagsToProcess:[],active:!1}),hj.eventStream.clearPageContent(),hj.treeMirror.resetMutationListeners(),F=!1},initializeTreeMirror:hj.tryCatch((function(e){hj.treeMirror.mutationObserverAvailable&&hj.treeMirror.getTree((function(t,n,r){K(e,t,n,r)}),X)}),"behavior-data.initializeTreeMirror"),resume:function(){T.update(),C.trackCoordinatesOnNextClick()}},K=function(e,t,n,r){Y.setAndSendPageContent(e,JSON.stringify({docType:hj.html.getPageDoctype(),rootId:t,children:n,isIframe:r})),F=!0},X=function(e,t,n,r){var i={};(e.length||t.length||n.length||r.length)&&(i.time=hj.time.getNow(),i.timestamp=c.a.now(),e.length&&(i.a=e),t.length&&(i.b=t),n.length&&(i.c=n),r.length&&(i.d=r),hj.debug.emit("mutation",i),Object(s.a)(o.a.MUTATION,i,!1))}},,,function(e,t,n){"use strict";n.r(t);var r=n(80),i=n(93),o=n(7),a=n(34),s=n(60),c=n(16),u=n(4),l=n(29),h=function(){var e=null,t={},n="hjViewportId";function r(){null===e&&(o.a.canUseSessionStorage()?(function(){if(o.a.canUseLocalStorage()&&o.a.canUseSessionStorage()){var e=i();if(0!==e.length){var t=u.a.now(),r=e.filter((function(e){return e.expireTimestamp>t}));e.length!==r.length&&window.localStorage.setItem("hjActiveViewportIds",JSON.stringify(r));var a=sessionStorage.getItem(n);a&&r.some((function(e){return e.id===a}))&&window.sessionStorage.removeItem(n)}}}(),null===(e=window.sessionStorage.getItem(n))&&(e=Object(l.a)(),window.sessionStorage.setItem(n,e)),function(){if(o.a.canUseLocalStorage()){var t=u.a.now()+216e5,n=i();n.push({id:e,expireTimestamp:t}),window.localStorage.setItem("hjActiveViewportIds",JSON.stringify(n))}}()):e=Object(l.a)())}function i(){var e,t=window.localStorage.getItem("hjActiveViewportIds");if(!t)return[];try{e=JSON.parse(t)}catch(t){window.localStorage.removeItem("hjActiveViewportIds"),e=[]}return Array.isArray(e)?e:(window.localStorage.removeItem("hjActiveViewportIds"),[])}return t.getId=hj.tryCatch((function(){return r(),e}),"hj.viewport.getId"),window.addEventListener("pagehide",(function(){!function(){if(o.a.canUseLocalStorage()){var t=i();if(0!==t.length){var n=t.filter((function(t){return t.id!==e}));0===n.length?window.localStorage.removeItem("hjActiveViewportIds"):window.localStorage.setItem("hjActiveViewportIds",JSON.stringify(n))}}}()})),t};function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}hj.tryCatch((function(){if(void 0!==hj.scriptLoaded)return window.console=window.console||{warn:function(){}},console.warn("Hotjar Tracking Warning: Multiple Hotjar tracking codes were detected on this page. Tracking will not work as expected."),hj.verifyInstall?void Object(r.a)("Hotjar installation invalid.","It appears you have more than one Hotjar tracking code set up on this page. Hotjar cannot work properly if multiple Hotjar scripts are loaded concurrently. Please make sure you only install the one tracking code provided for this site.","bad"):void 0;var e,t,n;function u(e,t){var n=f(n=e[0],o=e[1],i=e[2],r=e[3],t[0],7,-680876936),r=f(r,n,o,i,t[1],12,-389564586),i=f(i,r,n,o,t[2],17,606105819),o=f(o,i,r,n,t[3],22,-1044525330);n=f(n,o,i,r,t[4],7,-176418897),r=f(r,n,o,i,t[5],12,1200080426),i=f(i,r,n,o,t[6],17,-1473231341),o=f(o,i,r,n,t[7],22,-45705983),n=f(n,o,i,r,t[8],7,1770035416),r=f(r,n,o,i,t[9],12,-1958414417),i=f(i,r,n,o,t[10],17,-42063),o=f(o,i,r,n,t[11],22,-1990404162),n=f(n,o,i,r,t[12],7,1804603682),r=f(r,n,o,i,t[13],12,-40341101),i=f(i,r,n,o,t[14],17,-1502002290),n=g(n,o=f(o,i,r,n,t[15],22,1236535329),i,r,t[1],5,-165796510),r=g(r,n,o,i,t[6],9,-1069501632),i=g(i,r,n,o,t[11],14,643717713),o=g(o,i,r,n,t[0],20,-373897302),n=g(n,o,i,r,t[5],5,-701558691),r=g(r,n,o,i,t[10],9,38016083),i=g(i,r,n,o,t[15],14,-660478335),o=g(o,i,r,n,t[4],20,-405537848),n=g(n,o,i,r,t[9],5,568446438),r=g(r,n,o,i,t[14],9,-1019803690),i=g(i,r,n,o,t[3],14,-187363961),o=g(o,i,r,n,t[8],20,1163531501),n=g(n,o,i,r,t[13],5,-1444681467),r=g(r,n,o,i,t[2],9,-51403784),i=g(i,r,n,o,t[7],14,1735328473),n=p(n,o=g(o,i,r,n,t[12],20,-1926607734),i,r,t[5],4,-378558),r=p(r,n,o,i,t[8],11,-2022574463),i=p(i,r,n,o,t[11],16,1839030562),o=p(o,i,r,n,t[14],23,-35309556),n=p(n,o,i,r,t[1],4,-1530992060),r=p(r,n,o,i,t[4],11,1272893353),i=p(i,r,n,o,t[7],16,-155497632),o=p(o,i,r,n,t[10],23,-1094730640),n=p(n,o,i,r,t[13],4,681279174),r=p(r,n,o,i,t[0],11,-358537222),i=p(i,r,n,o,t[3],16,-722521979),o=p(o,i,r,n,t[6],23,76029189),n=p(n,o,i,r,t[9],4,-640364487),r=p(r,n,o,i,t[12],11,-421815835),i=p(i,r,n,o,t[15],16,530742520),n=v(n,o=p(o,i,r,n,t[2],23,-995338651),i,r,t[0],6,-198630844),r=v(r,n,o,i,t[7],10,1126891415),i=v(i,r,n,o,t[14],15,-1416354905),o=v(o,i,r,n,t[5],21,-57434055),n=v(n,o,i,r,t[12],6,1700485571),r=v(r,n,o,i,t[3],10,-1894986606),i=v(i,r,n,o,t[10],15,-1051523),o=v(o,i,r,n,t[1],21,-2054922799),n=v(n,o,i,r,t[8],6,1873313359),r=v(r,n,o,i,t[15],10,-30611744),i=v(i,r,n,o,t[6],15,-1560198380),o=v(o,i,r,n,t[13],21,1309151649),n=v(n,o,i,r,t[4],6,-145523070),r=v(r,n,o,i,t[11],10,-1120210379),i=v(i,r,n,o,t[2],15,718787259),o=v(o,i,r,n,t[9],21,-343485551);e[0]=b(n,e[0]),e[1]=b(o,e[1]),e[2]=b(i,e[2]),e[3]=b(r,e[3])}function l(e,t,n,r,i,o){return t=b(b(t,e),b(r,o)),b(t<<i|t>>>32-i,n)}function f(e,t,n,r,i,o,a){return l(t&n|~t&r,e,t,i,o,a)}function g(e,t,n,r,i,o,a){return l(t&r|n&~r,e,t,i,o,a)}function p(e,t,n,r,i,o,a){return l(t^n^r,e,t,i,o,a)}function v(e,t,n,r,i,o,a){return l(n^(t|~r),e,t,i,o,a)}function m(e){var t,n=[];for(t=0;64>t;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n}window.hj=window.hj||function(){(window.hj.q=window.hj.q||[]).push(arguments)},window.hj.q=window.hj.q||[],hj.hostname=hj.host.split(":")[0],hj.port=443,hj.apiUrlBase="https://"+hj.host+"/api/v1",hj.apiUrlBaseV2="https://"+hj.host+"/api/v2",hj.isPreview=Boolean(_hjSettings.preview),hj.userDeviceType=null,hj.optOut=!1,hj.windowSize=null,hj.maxRecordingTagLength=250,hj.settings=hj.isPreview?{}:window.hjSiteSettings,hj.locationListener=(n="manual",(t={}).setMode=hj.tryCatch((function(t){n=t,e&&clearInterval(e),"automatic_with_fragments"===n?e=setInterval((function(){var e=""===location.hash&&location.href.indexOf("#")>-1?"#":location.hash,t="".concat(location.origin).concat(location.pathname).concat(location.search).concat(e);hj.currentUrl&&hj.currentUrl!=t&&hj._init.reinit(t)}),200):"automatic"===n&&(e=setInterval((function(){var e="".concat(location.origin).concat(location.pathname).concat(location.search);hj.currentUrl&&hj.currentUrl.split("#")[0]!=e&&hj._init.reinit(e)}),200))})),t);var y="0123456789abcdef".split("");function j(e){for(var t="",n=0;4>n;n++)t+=y[e>>8*n+4&15]+y[e>>8*n&15];return t}function b(e,t){return e+t&4294967295}hj.encodeAsUtf8=i.d,hj.decodeAsUtf8=i.c,hj.md5=function(e,t){var n="";try{n=function(e){for(var t=0;t<e.length;t++)e[t]=j(e[t]);return e.join("")}(function(e){var t,n=e.length,r=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=e.length;t+=64)u(r,m(e.substring(t-64,t)));e=e.substring(t-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<e.length;t++)i[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(i[t>>2]|=128<<(t%4<<3),55<t)for(u(r,i),t=0;16>t;t++)i[t]=0;return i[14]=8*n,u(r,i),r}(hj.encodeAsUtf8(e)))}catch(e){t?n="":hj.exceptions.log(e,"md5")}return n},hj.b64EncodeUnicode=i.b,hj.b64DecodeUnicode=i.a,hj.isParsableJSON=function(e){try{var t=JSON.parse(e);if(t&&"object"===d(t))return!0}catch(e){}return!1},"5d41402abc4b2a76b9719d911017c592"!=hj.md5("hello")&&(b=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}),hj.time=function(){var e=(new Date).getTime(),t={reset:function(){e=(new Date).getTime()},getNow:function(){return(new Date).getTime()-e}};return t}(),hj.debug=function(){var e={isOn:function(){return _hjSettings.hjdebug},on:function(e){_hjSettings.hjdebug=!0,e&&o.a.items.DEBUG_FLAG.set(!0)},off:function(){_hjSettings.hjdebug=!1,o.a.items.DEBUG_FLAG.clear()},emit:function(e,t){"undefined"!=typeof _hjEmitters&&_hjEmitters.includes&&_hjEmitters.includes(e)&&window.postMessage({data:t,message:o.a.items.DEBUG_FLAG.getKey(),type:e},"*")}};return e}(),hj.url=a,hj.log=function(){var e=!1,t="",n={},r={autotag:"#ff0099",command:"#0079a4",cookies:"#5a2c22",data:"#009bd2",event:"#ff7000",events:"#ffc000",exception:"#e63946",heatmap:"#700000",init:"#6600cc",integration:"#2a9072",poll:"#00a000",property:"#ff33cc",recording:"#dd0000",rendering:"#bd00ea",sampling:"#efb0a1",survey:"#007000",targeting:"#00ee00",visitdata:"#00668a",websocket:"#0dc0ff"};return n.init=function(){void 0===window.console&&(window.console={debug:function(){},trace:function(){},log:function(){},info:function(){},warn:function(){},error:function(){}})},n.debug=function(i,o,a){var s=o&&r[o.toLowerCase()]||"#2a9d8f";t!=i&&e&&(console.groupEnd(),e=!1),t=i,hj.debug.isOn()&&("object"===d(i)?hj.hq.each(i,(function(e,t){n.debug(e+": "+t,o,null)})):(i=o&&"string"==typeof i?o.toUpperCase()+": "+i:i,i="%c"+(new Date).toTimeString().replace(/.*(\d{2}:\d{2}:\d{2}).*/,"$1")+":%c "+i,a?(e||(console.groupCollapsed(i+":","color: #999;","color: "+s+";"),e=!0),console.log(a)):console.log(i,"color: #999;","color: "+s+";")))},n.info=function(e){console.log("%c"+e,"color: #006EFF")},n.warn=function(e){console.log("%c"+e,"color: #E8910C")},n.error=function(e){console.error("Hotjar error: "+e)},n.warnIfEmpty=function(e,t){if(Array.isArray(e)?0===e.length:hj.hq.isNullOrUndefined(e)){var n=null===e?"null":void 0===e?"undefined":"no value";hj.log.debug("WARNING: [".concat(t,"] a value was expected but ").concat(n," was found!"))}},n}(),hj.loader=function(){var e={},t=[];return e.getModules=hj.tryCatch((function(){return t}),"common"),e.registerModule=hj.tryCatch((function(e,n,r){t.push({name:e,module:n,nonTracking:void 0!==r&&r})}),"common"),e}(),hj.targeting=function(){var e={};function t(t,n){var r;if(!e.matchOperations[t.match_operation])return hj.exceptions.log(new Error('Targeting error - "'.concat(t.match_operation,'" match operation does not exist.')),"common.targeting.matchPatternWithRule"),!1;t.rule_type=t.rule_type||t.component;var i=function(e,t){return!t||"date"!==e.rule_type||"less_than"!==e.match_operation&&"greater_than"!==e.match_operation?t:(new Date(t).valueOf()/1e3).toFixed(0)}(t,n);return(r=e.matchOperations[t.match_operation](t,i)).error?(hj.exceptions.log(new Error("Targeting error - ".concat(t.match_operation," - ").concat(r.error)),"common.targeting.matchPatternWithRule"),!1):("url"!==t.component&&t.negate&&(r=!r),function(e,t,n){var r=n?"Match ":"No Match ";!n||"url"!==e.component&&"device"!==e.component||(r+=e.component+"|"+e.match_operation+"|"+e.pattern+(e.negate?" [NEGATE]":""),hj.log.debug(r,"targeting"));"attribute"!==e.component&&"event"!==e.component||(r+=e.component+"|"+e.name+" ("+e.rule_type+")|"+e.match_operation+"|"+e.pattern+"|compared with: "+t+(e.negate?" [NEGATE]":""),hj.log.debug(r,"targeting"))}(t,n,r),r)}function n(e){var n=!1,r=Object(c.a)();return(n=0===e.length||3===e.length||e.some((function(e){return t(e,r)})))?hj.log.debug("Device match found","targeting"):hj.log.debug("No device match found","targeting"),n}function r(e,n){var r,i=!1,o=!1,a=!1;if(0===e.length)return hj.log.debug("No URL targeting rules set","targeting"),!1;for(var s=0;s<e.length;s+=1)if((i=t(r=e[s],n))&&!r.negate&&(a=!0),i&&r.negate){o=!0;break}return(i=a&&!o)?hj.log.debug("URL match found without any negate rules","targeting"):o?hj.log.debug("Negate URL rule match found","targeting"):hj.log.debug("No URL match found","targeting"),i}e.matchUrl=r;var i=function(e){return e.toLowerCase().split("#")[0].split("?")[0].replace("http://www.","").replace("https://www.","").replace("http://","").replace("https://","").replace(/\/$/,"")};var o={};e.matchRules=hj.tryCatch((function(e,i,a){for(var c={device:{rules:[],isMatch:null,doMatch:n},url:{rules:[],isMatch:null,doMatch:r},attribute:{rules:[]},trigger:{rules:[]}},u=0;u<e.length;u+=1)c[e[u].component]?c[e[u].component].rules.push(e[u]):hj.exceptions.log(new Error("Targeting error - ".concat(c[e[u].component]," targeting component is not supported.")),"common.targeting.matchRules");hj.log.debug("-- Matching rules for new item --");var l=function(e,t){"__proto__"!==i&&"constructor"!==i&&(void 0===o[i]&&(o[i]=[]),o[i].push({eventName:e,callback:t}),hj.event.listen(e,t))};if(Object.keys(o).forEach((function(e){e!==i&&(o[e].forEach((function(e){var t=e.eventName,n=e.callback;hj.event.removeListener(t,n)})),delete o[e])})),!c.device.doMatch(c.device.rules))return!1;var h=function(){return function(e){var n,r=e.every((function(e){return t(e,s.userAttributes.get(e.name))}));return n=r?0===e.length?"No specific user attribute targeting rules set.":"All user attributes matched.":"User attributes set do not match current visitor.",hj.log.debug(n,"targeting"),r}(c.attribute.rules)},d=c.trigger.rules;if(d.length){hj.log.debug("Setting up targeting listeners for trigger rules","targeting");var f=function(){h()&&(null==a||a(!0))};d.forEach((function(e){var t="trigger:"+e.pattern;l(t,f)}))}var g=c.url.doMatch(c.url.rules,i);if(0===c.attribute.rules.length||h())return!!g&&(null==a||a(),!0);hj.log.debug("Setting up targeting listeners for attribute rules","targeting");return l("user-attributes-set",(function(){g&&h()&&(null==a||a(!0))})),!1}),"common.targeting.matchRules");var a=function(e,t,n){return function(){return e.apply(null,arguments)?t.apply(null,arguments):n.apply(null,arguments)}},u=function(e,t){return void 0!==e&&void 0!==t&&null!==e&&null!==t},l=function(e,t){return!isNaN(e.pattern)&&!isNaN(t)&&""!==t&&"boolean"!=typeof t},h=function(e,t){if(!t||t.toString()===parseInt(t,10).toString())return!1;var n=new Date(t);return"Invalid Date"!==n&&!isNaN(n)},d=function(e){return a(u,e,(function(){return!1}))},f=function(e){return d(a(l,e,(function(e,t){return{error:t?"Cannot compare non-numeric values (rule: { name: '".concat(e.name,"' },\n                        pattern: '").concat(t,"')."):void 0}})))},g=function(e){return d(a(h,e,(function(e,t){return{error:"Cannot compare dates. Given pattern is not a date (rule: { name: '".concat(e.name,"' }, pattern: '").concat(t,"').")}})))};return e.matchOperations={exact:function(e,t){return"string"===e.rule_type?d((function(e,t){return t.toLowerCase()===e.pattern.toLowerCase()}))(e,t):"boolean"===e.rule_type?(e.pattern="true"===String(e.pattern),t===e.pattern):"number"===e.rule_type?null!=t&&Number(t)===Number(e.pattern):t===e.pattern},starts_with:d((function(e,t){return 0===(t=t||"").indexOf(e.pattern)})),ends_with:d((function(e,t){var n=t.length-e.pattern.length==-1?0:t.length-e.pattern.length;return t.substring(n,t.length)===e.pattern})),contains:d((function(e,t){return-1!==(t=t||"").indexOf(e.pattern)})),regex:d((function(e,t){try{return new RegExp(e.pattern).test(t)}catch(e){return hj.log.error("The regular expression used for page targeting was invalid. Please provide a valid regular\n                        expression.\n\nRead more here: https://help.hotjar.com/hc/en-us/articles/115012727628"),!1}})),simple:d((function(e,t){return i(t)===i(e.pattern)})),greater_than:f((function(e,t){return Number(t)>Number(e.pattern)})),less_than:f((function(e,t){return Number(t)<Number(e.pattern)})),unknown:function(e,t){return 0!==(t="string"==typeof t?t.trim():t)&&!1!==t&&!t},exact_date:g((function(e,t){var n=new Date(1e3*e.pattern);return(t=new Date(t)).toDateString()===n.toDateString()})),exact_days_ago:g((function(e,t){var n,r=new Date;return n=r.setDate(r.getDate()-Number(e.pattern)),n=new Date(n),(t=new Date(t)).toDateString()===n.toDateString()})),more_than_days_ago:g((function(e,t){var n=Number(e.pattern)+1,r=(new Date).getTime()/1e3;return r-=86400*n,new Date(t).getTime()/1e3<=r})),less_than_days_ago:g((function(t,n){if(0===Number(t.pattern))return e.matchOperations.exact_days_ago(t,n);var r=(new Date).getTime()/1e3;return r-=86400*Number(t.pattern),new Date(n).getTime()/1e3>=r}))},e}(),hj.rendering=function(){var e={},t=[];function n(e,t){hj.tryCatch(t,"Rendering")(e)}function r(e,t){hj.widgetDelay.set((function(){hj.tryCatch(t,"Rendering")(e)}),1e3*e.display_delay)}function i(e,n,r){var i=hj.hq(document),o=hj.hq(window),a=[];function s(){hj.tryCatch(n,"Rendering")(e),i.off("mousemove."+r),i.off("mouseout."+r)}t.push(r),i.off("mousemove."+r),i.off("mouseout."+r),i.on("mousemove."+r,hj.tryCatch((function(e){a.push({x:e.clientX,y:e.clientY}),a.length>2&&(a[1].x===a[2].x&&a[1].y===a[2].y?a.pop():a.shift())}),"Rendering")),i.on("mouseout."+r,hj.tryCatch((function(e){e.relatedTarget&&(e.relatedTarget===this||this.compareDocumentPosition(e.relatedTarget)&Node.DOCUMENT_POSITION_CONTAINED_BY)||function(e){var t=a[1],n=a[0];if(e|=0,void 0!==t&&!(t.y>=n.y||e>0)){n.x===t.x&&s();var r=(n.y-t.y)/(n.x-t.x),i=-(n.y-r*n.x)/r;i>0&&i<o.width()&&s()}}(e.clientY)}),"Rendering"))}function o(e,t,n){var r=hj.hq(document),i=hj.hq(window);i.on("scroll."+n,hj.tryCatch((function(){var o=r.height();(r.scrollTop()+hj.ui.getWindowSize().height)/o>=.5&&(i.off("scroll."+n),t(e))}),"Rendering"))}return e.clearAllAbandonEvents=hj.tryCatch((function(){t.forEach((function(e){hj.log.debug("Removing abandon events for "+e,"rendering"),hj.hq(document).off("mousemove."+e),hj.hq(document).off("mouseout."+e)})),t=[]}),"common"),e.addToDom=hj.tryCatch((function(e,t){if(!e)throw Error("container id not defined");if(!t)throw Error("cannot append html to container #"+e+", html not defined.");return hj.hq("#"+e).remove(),hj.hq("body").append(t),hj.hq("#"+e+">div")}),"common.addToDom"),e.TrustedString=function(e){this.value=e},e.callAccordingToCondition=hj.tryCatch((function(e,t,a){var s={immediate:n,delay:r,abandon:i,scroll:o}["inline"===e.display_type?"immediate":e.display_condition];if(hj.log.debug("Calling active item ("+t+"): "+e.display_condition,"rendering"),!s)throw new Error('Unhandled display condition: "'+e.display_condition+'"');hj.tryCatch(s,"Rendering")(e,a,t)}),"common"),e}(),hj.survey=hj.tryCatch((function(){var e={ctrl:void 0,data:void 0,model:{},activeLanguageDirection:"ltr"};return e}),"common")(),hj.ui=function(){var e={};return e.getWindowSize=hj.tryCatch((function(){var e=function(){try{return window.self!==window.top}catch(e){return!0}}(),t={width:!e&&window.screen?window.screen.width:document.body.clientWidth,height:!e&&window.screen?window.screen.height:document.body.clientHeight};return{width:window.innerWidth||document.documentElement.clientWidth||t.width,height:window.innerHeight||document.documentElement.clientHeight||t.height}}),"common"),e.getDocumentSize=hj.tryCatch((function(){var t,n;if(document&&document.documentElement&&document.documentElement.clientWidth)t=document.documentElement.clientWidth,n=document.documentElement.clientHeight;else{var r=e.getWindowSize();t=r.width,n=r.height}return{width:t,height:n}}),"common"),e.getScrollPosition=hj.tryCatch((function(e){return e=e||window,{left:hj.hq(e).scrollLeft(),top:hj.hq(e).scrollTop()}}),"common"),e.getBottomAsPercentage=hj.tryCatch((function(){var e=parseInt(1e3*(hj.hq(window).scrollTop()+hj.ui.getWindowSize().height)/hj.hq(document).height(),10);return Math.min(1e3,e)}),"common"),e.disableScrolling=hj.tryCatch((function(e){var t=hj.ui.getScrollPosition();hj.hq(document).on("scroll.hotjarDisable resize.hotjarDisable mousewheel.hotjarDisable DOMMouseScroll.hotjarDisable touchmove.hotjarDisable",hj.tryCatch((function(n){n.preventDefault(),window.scrollTo(t.left,t.top),e&&e()}),"common"))}),"common"),e.enableScrolling=hj.tryCatch((function(){hj.hq(document).off("scroll.hotjarDisable"),hj.hq(document).off("resize.hotjarDisable"),hj.hq(document).off("mousewheel.hotjarDisable"),hj.hq(document).off("DOMMouseScroll.hotjarDisable"),hj.hq(document).off("touchmove.hotjarDisable")}),"common"),e}(),hj.dom=function(){var e={getCSSURLs:function(){var e=[],t=document.styleSheets;return hj.log.debug("Getting CSS","dpc","Starting"),t&&t.length>0&&hj.hq.each(t,(function(t,n){n.href&&0===n.href.indexOf("http")&&(hj.log.debug("Getting CSS","dpc",n.href),e.push(n.href))})),e}};return e}(),hj.html=function(){var e={};return e.getPageContent=hj.tryCatch((function(e,t){function n(e,t){t=t||document;var n=parseInt(e.split(":")[0]),r=e.substring(e.indexOf(":")+1);return t.querySelectorAll(r)[n]}t=t||[],hj.treeMirror.getMirroredDocument((function(r){var i=hj.insertedRules.getCurrentInsertedRules(),o={};i.forEach((function(e){var i=n(e.parentSelector,r);if(o[e.parentSelector]||(o[e.parentSelector]=""),"LINK"===i.tagName){var a=i.attributes,s=a.href.value.split(location.hostname).pop().replace("/",""),c=r.querySelector(".blob-hash-"+s);if(!c){c=document.createElement("style"),o[e.parentSelector]=o[e.parentSelector]+"/* Originally: <link ";for(var u=0,l=a.length;u<l;u++)"id"!==a[u].name&&"class"!==a[u].name||c.setAttribute(a[u].name,a[u].value),o[e.parentSelector]+=a[u].name+'="'+a[u].value+'" ';o[e.parentSelector]+="> */",c.classList.add("blob-hash-"+s),i.parentNode.insertBefore(c,i.nextSibling)}i=c}o[e.parentSelector]=o[e.parentSelector]+"\n"+e.rule,-1===t.indexOf('link[href*="blob:"]')&&t.push('link[href*="blob:"]')})),Object.keys(o).forEach((function(e){n(e,r).textContent=o[e]})),function(e,t,n){var r,i=e.querySelectorAll("head iframe"),o=0==document.getElementsByTagName("base").length?location.origin:document.getElementsByTagName("base")[0].href;[].forEach.call(i,(function(e){e.parentNode.removeChild(e)})),n&&n.forEach((function(t){r=e.querySelectorAll(t),hj.hq.each(r,(function(e,t){t.parentNode.removeChild(t)}))})),Array.prototype.slice.call(e.getElementsByTagName("img")).forEach((function(e){if(e.srcset){if(""===e.src){var t=e.srcset.match(/(?:([^"'\s,]+)(\s*(?:\s+\d+[wx]))?(?:,\s*)?)/g)[0];if(t){var n=t.indexOf(" ");n>0&&(t=t.substring(0,n)),0!==(t=t.replace(",","")).indexOf("http")&&(t=o+t),e.src=t}}e.removeAttribute("srcset")}})),Array.prototype.slice.call(e.getElementsByTagName("source")).forEach((function(e){e.attributes.srcset&&e.removeAttribute("srcset")})),[].forEach.call(e.querySelectorAll("script"),(function(e){e.parentNode.removeChild(e)})),t(hj.html.getPageDoctype()+e.documentElement.outerHTML)}(r,e,t)}))}),"common"),e.getPageDoctype=hj.tryCatch((function(){return null===document.doctype?"":"<!DOCTYPE "+(document.doctype.name||"html")+(document.doctype.publicId?' PUBLIC "'+document.doctype.publicId+'"':"")+(!document.doctype.publicId&&document.doctype.systemId?" SYSTEM":"")+(document.doctype.systemId?' "'+document.doctype.systemId+'"':"")+">\n"}),"common"),e}(),hj.features=function(){var e={};return e.getFeatures=hj.tryCatch((function(){return hj.settings.features||[]}),"hj.features.getFeatures"),e.hasFeature=hj.tryCatch((function(t){var n;try{var r=window.localStorage.getItem("HJ_OVERRIDE_FEATURE:".concat(t));n="true"===r||"1"===r}catch(e){n=!1}return e.getFeatures().indexOf(t)>-1||n}),"hj.features.hasFeature"),e}(),hj.cssBlobs=function(){var e={},t=[];return e.register=function(e){t.push(e)},e.handleBlobStyles=hj.tryCatch((function(e){var n,r=[];if("link"===e.tagName.toLowerCase()&&"rel"in e.attributes&&"stylesheet"===e.attributes.rel.value&&"href"in e.attributes&&0===e.attributes.href.value.indexOf("blob:")){n=hj.selector().get(hj.hq(e));for(var i=0,o=e.sheet.cssRules.length;i<o;i++)r.push({parentSelector:n,rule:e.sheet.cssRules[i].cssText,index:i});t.forEach((function(e){e(r)}))}}),"hj.cssBlobs.apply"),e}(),hj.viewport=hj.tryCatch(h,"common")(),hj.resizeListeners=function(){var e={},t={};return e.remove=hj.tryCatch((function(e){var n="survey_".concat(e);"function"==typeof t[n]&&window.removeEventListener("resize",t[n])}),"hj.resizeHandlers.setHandler"),e.add=hj.tryCatch((function(n,r){var i="survey_".concat(r);e.remove(r),t[i]=n,window.addEventListener("resize",n)}),"hj.resizeHandlers.setHandler"),e}(),hj.experimentation=function(){var e={getVariant:function(e,t,n){return hj.tryCatch((function(){var r;if(!(Array.isArray(t)&&Array.isArray(n)&&t.length&&n.length&&t.length===n.length))throw new Error("Options and probability split must be arrays of equal length.");if(n.reduce((function(e,t){return e+t}))<.99)throw new Error("Probability splits should add up to at least 0.99.");(null===(r=window.hjSiteSettings.experimentation)||void 0===r?void 0:r.variants)||(window.hjSiteSettings.experimentation={variants:{}});var i=window.hjSiteSettings.experimentation.variants[e];if(!i){for(var o,a=Math.random(),s=0,c=0;c<=n.length-1;c++){var u=s+n[c];if(a<=u){o=t[c];break}s=u}window.hjSiteSettings.experimentation.variants[e]=i=o}return i}),"hj.experimentation.getVariant")()}};return e}()}),"common")()},,,,,,,,function(e,t,n){"use strict";n.r(t),n.d(t,"sessionEvents",(function(){return r}));var r={storage:{},set:hj.tryCatch((function(e){r.storage.events=e}),"sessionEvents.set"),get:hj.tryCatch((function(){return r.storage.events}),"sessionEvents.get")}},function(e,t,n){"use strict";n.r(t),n.d(t,"Resource",(function(){return a}));var r,i=n(106),o=new RegExp("<.+:(.+)>");function a(e,t,n){var r={},i=function(){for(var t=e.split("/"),n=[],r=0;r<t.length;r+=1){var i=o.exec(t[r]);i?n.push({label:i[1],isDynamic:!0}):n.push({label:t[r],isDynamic:!1})}return n}();return function(){for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e)){var o=t[e][0],a=t[e][1];r[e]=s({urlPathTokens:i,method:o,requiredArgs:a,getHost:n})}}(),r}function s(e){var t=e.urlPathTokens,n=e.method,r=e.requiredArgs,o=e.getHost;function a(e){if(!r)return e;for(var t={},n=0;n<r.length;n+=1)if(t[r[n]]=e[r[n]],void 0===t[r[n]])throw new Error('Argument "'+r[n]+'" is missing.');return t}return function(e,r,s,c){var u,l="https://".concat(null!==(u=null==o?void 0:o())&&void 0!==u?u:hj.host).concat(function(e,t){for(var n=[],r=0;r<e.length;r+=1){var i=e[r];if(i.isDynamic){var o=t[i.label];if(void 0===o)throw new Error('Argument "'+i.label+'" is missing.');n.push(o)}else n.push(i.label)}return n.join("/")}(t,e));switch(n){case"GET":i.get(l,r,s);break;case"POST":i.post(l,a(e),r,s,c);break;case"PUT":i.putAsJSON(l,a(e),r,s);break;default:throw new Error('HTTP method "'+n+'" is not supported.')}}}hj.resource=((r={}).TokenRestrictedStorage=a("/api/v1/trs/<string:token>",{put:["PUT",["token","data"]]}),r)},function(e,t,n){"use strict";n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return f})),n.d(t,"c",(function(){return g}));var r=n(79),i=n(7),o=n(4),a=n(35),s=n(27),c=n(136),u=n(48),l=Object(c.a)({storageAccessor:i.a.items.HJ_SESSION,serializer:u.f,deserializer:u.b}),h=function(){var e=s.a.get("session");return(null==e?void 0:e.id)?e:l.get()},d=function(){var e;return null===(e=h())||void 0===e?void 0:e.id},f=function(){var e;return(null===(e=h())||void 0===e?void 0:e.sessionizerBetaEnabled)?1:0},g=hj.tryCatch((function(){var e,t,n=l.get();n||s.a.set("session",null);var i,c,h,d,f,g=s.a.get("session");((null===(e=g)||void 0===e?void 0:e.created)||(g=n),null===(t=g)||void 0===t?void 0:t.created)&&(g.created+a.a<o.a.now()&&(s.a.set("session",null),g=null));if(null===g){var p=hj.features.hasFeature("sessionizer_beta_enabled");!function(e){l.set(e),s.a.set("session",e)}({id:null!==(c=null==(i={sessionizerBetaEnabled:p})?void 0:i.id)&&void 0!==c?c:Object(u.c)(),created:null!==(h=null==i?void 0:i.created)&&void 0!==h?h:o.a.now(),inSample:null!==(d=null==i?void 0:i.inSample)&&void 0!==d?d:Object(r.a)(),sessionizerBetaEnabled:null!==(f=null==i?void 0:i.sessionizerBetaEnabled)&&void 0!==f&&f})}else s.a.set("session",g||{})}),"session.registerSession")},,,,,function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}try{!function(e,t){var r=function(e){return new i(e)};r.isValidSelector=function(e){try{return hj.hq(e),!0}catch(e){return!1}},r.isEmptyObject=function(e){return!Object.keys(e).length},r.isFunction=function(e){return"function"==typeof e},r.isWindow=function(t){return t===e},r.isDocument=function(e,n){return e===(n||t)},r.noop=function(){},r.stringify=function(e){if(void 0===Array.prototype.toJSON)return JSON.stringify(e);var t=Array.prototype.toJSON;delete Array.prototype.toJSON;try{return JSON.stringify(e)}finally{Array.prototype.toJSON=t}},r.each=function(e,t){var r,i,o;if("object"===n(e)&&"[object Array]"!==Object.prototype.toString.call(e)){if((i=e[Object.keys(e)[0]])&&void 0!==i.nodeName){for(r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&"length"!==r&&!1===t(r,e[r],e))break}else for(r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&!1===t(r,e[r],e))break}else if(void 0!==e)for(o=0;o<e.length&&!1!==t(o,e[o],e);o+=1);},r.trim=function(e){return"string"==typeof e?e.replace(/^\s+|\s+$/gm,""):""},r.inArray=function(e,t){var n=t.indexOf(e);return void 0!==n&&-1!==n},r.values=function(e){return Object.keys(e).map((function(t){return e[t]}))},r.isUndefined=function(e){return void 0===e},r.isNullOrUndefined=function(e){return null===e||r.isUndefined(e)},r.indexOf=function(e,t){if("object"===n(t)){var r=t.indexOf(e);return void 0!==r?r:-1}return-1},r.ajax=function(e){var t=new XMLHttpRequest;e.type=e.type||"GET",e.timeout_ms=e.timeout_ms||15e3,e.withCredentials&&(t.withCredentials=!0),t.open(e.type,e.url,!0),t.timeout=e.timeout_ms,"POST"!==e.type&&"PUT"!==e.type||!e.contentType||t.setRequestHeader("Content-Type",e.contentType),t.onload=function(){t.status>=200&&t.status<400?r.isFunction(e.success)&&e.success(t.responseText&&JSON.parse(t.responseText),t):r.isFunction(e.error)&&e.error(t)},t.onerror=function(){r.isFunction(e.error)&&e.error(t)},t.ontimeout=function(){r.isFunction(e.timeout)&&e.timeout(t)},r.isFunction(e.requestAnnotator)&&e.requestAnnotator(t),"POST"!==e.type&&"PUT"!==e.type||!e.data?t.send():t.send(e.data)},r.get=function(e,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.timeout=15e3,n.onload=function(){n.status>=200&&n.status<400&&t&&t(n.responseText)},n.send()},r.eventHandlers={},r.selector="";var i=function(e){var i,o,a,s=window._hjDocument||t;if(r.selector=e,r.isWindow(e))return this[0]=window,this.length=1,this;if(r.isDocument(e,s))return this[0]=s,this.length=1,this;if("object"===n(e))return this[0]=e,this.length=1,this;if("string"==typeof e&&"<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3)return(i=s.createElement("div")).innerHTML=e,this[0]=i.childNodes[0],this.length=1,this;if("string"==typeof e){isNaN(e.charAt(1))||"."!==e.charAt(0)&&"#"!==e.charAt(0)||(e=e.charAt(0)+"\\3"+e.charAt(1)+" "+e.slice(2));try{o=s.querySelectorAll(e)}catch(e){return this.length=0,this}for(a=0;a<o.length;a+=1)this[a]=o[a];return this.length=o.length,this}return this};i.prototype.val=function(e){return void 0!==e&&this.length>0&&(this[0].value=e),void 0===this[0]?void 0:this[0]?this[0].value:""},i.prototype.text=function(e){return void 0===e?this[0].textContent:this[0].textContent=e},i.prototype.each=function(e,t){Array.prototype.forEach.call(this,(function(e,n,r){t(n,e,r)}))},i.prototype.append=function(e){var i;"object"===n(e)?"body"===r.selector?t.body.appendChild(e.get(0)):this[0].appendChild(e.get(0)):"body"===r.selector?((i=t.createElement("div")).innerHTML=e,t.body.appendChild(i)):((i=t.createElement("div")).innerHTML=e,this[0].appendChild(i))},i.prototype.hasClass=function(e){return this[0].classList?this[0].classList.contains(e):new RegExp("(^| )"+e+"( |$)","gi").test(this[0].className)},i.prototype.addClass=function(e){var t;for(t=0;t<this.length;t+=1)this[t].classList?this[t].classList.add(e):this[t].className+=" "+e;return this},i.prototype.removeClass=function(e){var t;for(t=0;t<this.length;t+=1)this[t].classList?this[t].classList.remove(e):this[t].className=this[t].className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ");return this},i.prototype.toggleClass=function(e){var t;for(t=0;t<this.length;t+=1)this[t].classList?this[t].classList.contains(e)?this[t].classList.remove(e):this[t].classList.add(e):new RegExp("(^| )"+e+"( |$)","gi").test(this[0].className)?this[t].className=this[t].className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," "):this[t].className+=" "+e;return this},i.prototype.is=function(e){return function(e,t){var n=e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector;if(n)return n.call(e,t);for(var r=e.parentNode.querySelectorAll(t),i=r.length;i>=0;i-=1)if(r[i]===e)return!0;return!1}(this[0],e)},i.prototype.remove=function(){var e;for(e=0;e<this.length;e+=1)this[e].parentNode.removeChild(this[e])},i.prototype.click=function(e){var n,r;for(n=0;n<this.length;n+=1)(r=t.createEvent("HTMLEvents")).initEvent("click",!0,!1),this[n].dispatchEvent(r),e&&e()},i.prototype.trigger=function(e){var n,r,i,o=e.split(" ");for(n=0;n<this.length;n+=1)for(r=0;r<o.length;r+=1)(i=t.createEvent("HTMLEvents")).initEvent(o[r],!0,!1),this[n].dispatchEvent(i)},i.prototype.on=function(e,i,o,a){var s,c,u,l,h,d,f,g,p=e.split(" ");if(a=!!a,r.isDocument(this[0])&&"string"==typeof i)for(c=0;c<p.length;c+=1)"string"==typeof i?("boolean"==typeof o&&!1===o&&(o=function(e){return e.preventDefault(),!1}),u=i+"."+p[c],l=function(e){if(h=t.querySelectorAll(i)){for(d=e.target,f=-1;d&&-1===(f=Array.prototype.indexOf.call(h,d));)d=d.parentElement;f>-1&&o.call(d,e)}},Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(l),t.addEventListener(p[c].split(".")[0],l,!0)):("boolean"==typeof i&&!1===i&&(i=function(e){return e.preventDefault(),!1}),Array.isArray(r.eventHandlers.document)||(r.eventHandlers.document=[]),r.eventHandlers.document.push(i),this[0].addEventListener(p[c].split(".")[0],i,a));else if(r.isDocument(this[0]))for(c=0;c<p.length;c+=1)"boolean"==typeof i&&!1===i&&(i=function(e){return e.preventDefault(),!1}),u="document."+p[c],Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(i),t.addEventListener(p[c].split(".")[0],i,a);else if(r.isWindow(this[0]))for(c=0;c<p.length;c+=1)"boolean"==typeof i&&!1===i&&(i=function(e){return e.preventDefault(),!1}),u="window."+p[c],Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(i),window.addEventListener(p[c].split(".")[0],i,a);else for(s=0;s<this.length;s+=1)for(c=0;c<p.length;c+=1)"object"===n(i)?(g=i,i=function(e){e.data=g,o.call(this[s],e)}):"boolean"==typeof i&&!1===i&&(i=function(e){return e.preventDefault(),!1}),u=r.selector+"."+p[c],Array.isArray(r.eventHandlers[u])||(r.eventHandlers[u]=[]),r.eventHandlers[u].push(i),this[s].addEventListener(p[c].split(".")[0],i,a);return this},i.prototype.off=function(e,i,o,a){var s,c,u,l=e.split(" ");for(a=!!a,s=0;s<this.length;s+=1)for(c=0;c<l.length;c+=1)if(r.isDocument(this[s])&&"string"==typeof i)if(void 0===o){if("object"===n(r.eventHandlers[i+"."+l[c]])){for(u=0;u<r.eventHandlers[i+"."+l[c]].length;u+=1)t.removeEventListener(l[c].split(".")[0],r.eventHandlers[i+"."+l[c]][u],!0);delete r.eventHandlers[i+"."+l[c]]}}else t.removeEventListener(l[c].split(".")[0],o,a);else if(r.isDocument(this[s]))if(void 0===i){if("object"===n(r.eventHandlers["document."+l[c]])){for(u=0;u<r.eventHandlers["document."+l[c]].length;u+=1)t.removeEventListener(l[c].split(".")[0],r.eventHandlers["document."+l[c]][u],a);delete r.eventHandlers["document."+l[c]]}}else t.removeEventListener(l[c].split(".")[0],i,a);else if(r.isWindow(this[s]))if(void 0===i){if("object"===n(r.eventHandlers["window."+l[c]])){for(u=0;u<r.eventHandlers["window."+l[c]].length;u+=1)window.removeEventListener(l[c].split(".")[0],r.eventHandlers["window."+l[c]][u],a);delete r.eventHandlers["window."+l[c]]}}else window.removeEventListener(l[c].split(".")[0],i,a);else if(void 0===i){if("object"===n(r.eventHandlers[r.selector+"."+l[c]])){for(u=0;u<r.eventHandlers[r.selector+"."+l[c]].length;u+=1)this[s].removeEventListener(l[c].split(".")[0],r.eventHandlers[r.selector+"."+l[c]][u],a);delete r.eventHandlers[r.selector+"."+l[c]]}}else this[s].removeEventListener(l[c].split(".")[0],i,a);return this},i.prototype.scrollTop=function(){return r.isWindow(this[0])||r.isDocument(this[0])?window.document.body.scrollTop||window.document.documentElement.scrollTop:this[0].scrollTop},i.prototype.scrollLeft=function(){return r.isWindow(this[0])||r.isDocument(this[0])?t.body.scrollLeft||t.documentElement.scrollLeft:this[0].scrollLeft},i.prototype.height=function(){var e;return r.isWindow(this[0])?t.documentElement.clientHeight:9===this[0].nodeType?(e=this[0].documentElement,Math.max(this[0].body.scrollHeight,e.scrollHeight,this[0].body.offsetHeight,e.offsetHeight,e.clientHeight)):Math.max(this[0].scrollHeight,this[0].offsetHeight)},i.prototype.width=function(){var e;return r.isWindow(this[0])?t.documentElement.clientWidth:9===this[0].nodeType?(e=this[0].documentElement,Math.max(this[0].body.scrollWidth,e.scrollWidth,this[0].body.offsetWidth,e.offsetWidth,e.clientWidth)):Math.max(this[0].scrollWidth,this[0].offsetWidth)},i.prototype.outerHeight=function(){return this[0].offsetHeight},i.prototype.offset=function(){var e=(this[0]&&this[0].ownerDocument).documentElement;return{top:this[0].getBoundingClientRect().top+window.pageYOffset-e.clientTop,left:this[0].getBoundingClientRect().left+window.pageXOffset-e.clientLeft}},i.prototype.attr=function(e,t){var r;if(t||""===t){for(r=0;r<this.length;r+=1)this[r].setAttribute(e,t);return this}return this[0]&&"object"===n(this[0])&&null!==this[0].getAttribute(e)?this[0].getAttribute(e):void 0},i.prototype.ready=function(e){r.isDocument(this[0])&&("interactive"===t.readyState||"complete"===t.readyState||"loaded"===t.readyState?e():t.addEventListener("DOMContentLoaded",e,!1))},i.prototype.parent=function(){var e;return"#document-fragment"===(null===(e=this[0].parentNode)||void 0===e?void 0:e.nodeName)?r(this[0].parentNode.host):r(this[0].parentNode)},i.prototype.get=function(e){return this[e]},i.prototype.show=function(){var e;for(e=0;e<this.length;e+=1)this[e].style.display="";return this},i.prototype.hide=function(){var e;for(e=0;e<this.length;e+=1)this[e].style.display="none";return this},i.prototype.focus=function(){var e;for(e=0;e<this.length;e+=1)this[e].focus();return this},i.prototype.blur=function(){var e;for(e=0;e<this.length;e+=1)this[e].blur();return this},i.prototype.clone=function(){return this[0].cloneNode(!0)},i.prototype.removeAttr=function(e){var t;for(t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this},i.prototype.find=function(e){var t,n,i=r();try{t=this[0].querySelectorAll(e)}catch(e){return this.length=0,this}for(n=0;n<t.length;n+=1)i[n]=t[n];return i.length=t.length,i},i.prototype.is=function(e){var t,i=!1;if(!e||"object"!==n(this[0]))return!1;if("object"===n(e))return r(this[0]).get(0)===e.get(0);if("string"==typeof e){if(":visible"===e)return!(this[0].offsetWidth<=0&&this[0].offsetHeight<=0);if(":hidden"===e)return this[0].offsetWidth<=0&&this[0].offsetHeight<=0;if(":checked"===e)return this[0].checked;if(!(e.indexOf("[")>-1))return r(this[0]).get(0).nodeName.toLowerCase()===e;if((t=/([A-Za-z]+)\[([A-Za-z-]+)=([A-Za-z]+)]/g.exec(e)).length)return r.each(r(this[0]).get(0).attributes,(function(e,n){n.name===t[2]&&n.value===t[3]&&(i=!0)})),r(this[0]).get(0).nodeName.toLowerCase()===t[1]&&i}},i.prototype.css=function(e,t){var r,i;for(i=0;i<this.length;i+=1)if("object"===n(e))for(r in e)this[i].style[r]=e[r];else{if("number"!=typeof t&&"string"!=typeof t)return getComputedStyle(this[i])[e];if("__proto__"===e||"constructor"===e)return;this[i].style[e]=t}return this},i.prototype.animate=function(e,t){var n,i=this;for(void 0===t&&(t=400),n=0;n<i.length;n+=1)r.each(e,(function(e,r){r=r.toString();var o,a,s=getComputedStyle(i[n])[e].replace(/[0-9.-]/g,""),c=parseFloat(r),u=r.replace(/[0-9.-]/g,""),l=s||u,h=c-p,d=parseFloat(t/10),f=h/d,g=[],p=parseFloat(getComputedStyle(i[n])[e])||0;for(o=0;o<d;o+=1)p+=f,g.push({attribute:e,value:l?parseInt(p)+l:parseFloat(p).toFixed(1)});g.pop(),g.push({attribute:e,value:c+l}),g.length&&function e(t,n){var r=n[0].attribute;if("__proto__"===r||"constructor"===r)return;t.style[r]=n[0].value,n.shift(),n.length?a=setTimeout((function(){e(t,n)}),10):clearTimeout(a)}(i[n],g)}));return this},i.prototype.filter=function(e){return Array.prototype.filter.call(t.querySelectorAll(r.selector),(function(t,n){e(n,t)}))},e.hj=e.hj||{},e.hj.hq=e.hj.hq||r}(window,document)}catch(e){hj.exceptions.log(e,"hquery")}},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(93),i=function(e){var t=e.storageAccessor,n=e.serializer,i=e.deserializer;return{get:function(){var e=t.get();if(!e)return null===e?null:void 0;try{return i(Object(r.a)(e))}catch(e){hj.metrics.count("session-exception",{tag:{module:"deserialize-cookie"},extraTags:{errorMessage:hj.metrics.getErrorMessage(e),cookieKey:t.getKey()}})}},set:function(e){t.set(Object(r.b)(n(e)))},getKey:function(){return t.getKey()}}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r={shouldSuppressOnPage:!1},i={get:function(){return r.shouldSuppressOnPage},set:function(e){var t;r.shouldSuppressOnPage=!!(null===(t=hj.settings.suppress_all_on_specific_pages)||void 0===t?void 0:t.length)&&hj.targeting.matchUrl(hj.settings.suppress_all_on_specific_pages,e)},reset:function(){r.shouldSuppressOnPage=!1}}},,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var r=n(45),i=function(e,t){if(t)return"https://".concat(t,"/").concat(e);var n=Object(r.c)(),i=r.b[n].SURVEYS;return"https://".concat(i,"/").concat(e)},o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.location.href,t=[r.b[r.a.LIVE].SURVEYS,r.b[r.a.REVIEW_WEBAPP].SURVEYS,r.b[r.a.REVIEW].SURVEYS,r.b[r.a.STAGING].SURVEYS,r.b[r.a.DEV].SURVEYS,r.b[r.a.DEV_OLD].SURVEYS],n=document.createElement("a");return n.href=e,t.indexOf(n.hostname)>=0}},,,,,,,function(e,t,n){"use strict";n.r(t),n.d(t,"create",(function(){return o}));var r=n(29);function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o=hj.tryCatch((function(){var e={key:Object(r.a)()},t={},n={};return e.ready=hj.tryCatch((function(r,i){i=hj.tryCatch(i,"Data"),n[r]?(hj.log.debug("Property "+r+" is ready. Calling callback() now.","property",i),i(e)):(hj.log.debug("Property "+r+" is not ready. Saving callback().","property",i),t[r]?t[r].push({callback:i,executed:!1}):t[r]=[{callback:i,executed:!1}])}),"data"),e.set=hj.tryCatch((function(r,o){hj.log.debug("Setting properties."+r+" to "+o,"property"),n[r]=o,"object"===i(t[r])&&hj.hq.each(t[r],(function(t,n){n.executed||(hj.log.debug("Calling "+r+" callback.","property"),n.callback(e),n.executed=!0)}))}),"data"),e.get=hj.tryCatch((function(e){return n[e]}),"data"),e.remove=hj.tryCatch((function(e){delete n[e]}),"data"),e}),"data");hj.property=hj.tryCatch((function(){var e={};return e.create=o,hj.globals=e.create(),e}),"data")()},,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}e.exports=function(e,t){t||(t={}),"function"==typeof t&&(t={cmp:t});var n,i="boolean"==typeof t.cycles&&t.cycles,o=t.cmp&&(n=t.cmp,function(e){return function(t,r){var i={key:t,value:e[t]},o={key:r,value:e[r]};return n(i,o)}}),a=[];return function e(t){if(t&&t.toJSON&&"function"==typeof t.toJSON&&(t=t.toJSON()),void 0!==t){if("number"==typeof t)return isFinite(t)?""+t:"null";if("object"!==r(t))return JSON.stringify(t);var n,s;if(Array.isArray(t)){for(s="[",n=0;n<t.length;n++)n&&(s+=","),s+=e(t[n])||"null";return s+"]"}if(null===t)return"null";if(-1!==a.indexOf(t)){if(i)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var c=a.push(t)-1,u=Object.keys(t).sort(o&&o(t));for(s="",n=0;n<u.length;n++){var l=u[n],h=e(t[l]);h&&(s&&(s+=","),s+=JSON.stringify(l)+":"+h)}return a.splice(c,1),"{"+s+"}"}}(e)}},function(e,t,n){var r,i,o;!function(a,s){"use strict";i=[n(244)],void 0===(o="function"==typeof(r=function(e){var t=/(^|@)\S+:\d+/,n=/^\s*at .*(\S+:\d+|\(native\))/m,r=/^(eval@)?(\[native code])?$/;return{parse:function(e){if(void 0!==e.stacktrace||void 0!==e["opera#sourceloc"])return this.parseOpera(e);if(e.stack&&e.stack.match(n))return this.parseV8OrIE(e);if(e.stack)return this.parseFFOrSafari(e);throw new Error("Cannot parse given Error object")},extractLocation:function(e){if(-1===e.indexOf(":"))return[e];var t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));return[t[1],t[2]||void 0,t[3]||void 0]},parseV8OrIE:function(t){return t.stack.split("\n").filter((function(e){return!!e.match(n)}),this).map((function(t){t.indexOf("(eval ")>-1&&(t=t.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(,.*$)/g,""));var n=t.replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/^.*?\s+/,""),r=n.match(/ (\(.+\)$)/);n=r?n.replace(r[0],""):n;var i=this.extractLocation(r?r[1]:n),o=r&&n||void 0,a=["eval","<anonymous>"].indexOf(i[0])>-1?void 0:i[0];return new e({functionName:o,fileName:a,lineNumber:i[1],columnNumber:i[2],source:t})}),this)},parseFFOrSafari:function(t){return t.stack.split("\n").filter((function(e){return!e.match(r)}),this).map((function(t){if(t.indexOf(" > eval")>-1&&(t=t.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===t.indexOf("@")&&-1===t.indexOf(":"))return new e({functionName:t});var n=/((.*".+"[^@]*)?[^@]*)(?:@)/,r=t.match(n),i=r&&r[1]?r[1]:void 0,o=this.extractLocation(t.replace(n,""));return new e({functionName:i,fileName:o[0],lineNumber:o[1],columnNumber:o[2],source:t})}),this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf("\n")>-1&&e.message.split("\n").length>e.stacktrace.split("\n").length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)/i,r=t.message.split("\n"),i=[],o=2,a=r.length;o<a;o+=2){var s=n.exec(r[o]);s&&i.push(new e({fileName:s[2],lineNumber:s[1],source:r[o]}))}return i},parseOpera10:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,r=t.stacktrace.split("\n"),i=[],o=0,a=r.length;o<a;o+=2){var s=n.exec(r[o]);s&&i.push(new e({functionName:s[3]||void 0,fileName:s[2],lineNumber:s[1],source:r[o]}))}return i},parseOpera11:function(n){return n.stack.split("\n").filter((function(e){return!!e.match(t)&&!e.match(/^Error created at/)}),this).map((function(t){var n,r=t.split("@"),i=this.extractLocation(r.pop()),o=r.shift()||"",a=o.replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;o.match(/\(([^)]*)\)/)&&(n=o.replace(/^[^(]+\(([^)]*)\)$/,"$1"));var s=void 0===n||"[arguments not available]"===n?void 0:n.split(",");return new e({functionName:a,args:s,fileName:i[0],lineNumber:i[1],columnNumber:i[2],source:t})}),this)}}})?r.apply(t,i):r)||(e.exports=o)}()},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){n.p=hj.scriptDomain;var r=hj.metrics.time();(0,n(226).initErrorLogging)(),n(135),n(227),n(253),n(120),n(228),n(257),n(250),n(60),n(128),n(232),n(233),n(252),n(234),n(235),n(240),n(258),n(241),n(242),n(243),n(254),hj.metrics.timeEnd("resource-blocking-time",{tag:{resource:"modules-js"},start:r,type:"lab"})},function(e,t,n){"use strict";n.r(t),n.d(t,"exceptionLogger",(function(){return u})),n.d(t,"initErrorLogging",(function(){return l}));var r=n(3);function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){if(!window.navigator&&!window.location&&!window.document)return e;var t=e.request&&e.request.url||window.location&&window.location.href,n=(window.document||{}).referrer,r=(window.navigator||{}).userAgent,i=a(a(a({},e.request&&e.request.headers),n&&{Referer:n}),r&&{"User-Agent":r}),o=a(a({},t&&{url:t}),{},{headers:i});return a(a({},e),{},{request:o})}var u=function(e){var t;function n(e,t,n){(void 0!==hj.log?hj.log.debug:function(){})(e,t,n)}var o,a=null,s=0,u=[],l=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*/g,h=/\d{1,3}\.\d{1,3}\.\d{1,3}\.(\d{1,3})/g,d=/\d{4,}([-\s]?\d{4,}){2,}/g,f=/password(.*)/g,g={},p=[],v=null!==hj.errorUrl,m=window.location.search.indexOf("hjErrorLoggerSamplingDisabled=1")>0?1:.002,y=(null==e?void 0:e.throttleDelay)||1e3,j=(null==e?void 0:e.maxErrors)||10,b=["event_id","stacktrace","release","useragent","User-Agent","logger","scriptversion","module","errorgroup","errormessagegroup"],w=/[?&]logErrors/.test(location.search);function S(){return u.every((function(e){return"loaded"===e.state}))}function _(e){e.state="loaded",e.onLoad(),S()&&v&&g.startProcessing&&g.startProcessing()}function E(){u.filter((function(e){return"unloaded"===e.state})).forEach((function(e){e.check()?_(e):(function(e){var t;e.state="loading";var n=document.createElement("script");n.src=e.url,null===(t=document.getElementsByTagName("head")[0])||void 0===t||t.appendChild(n)}(e),function(e){var t=setInterval((function(){e.check()&&(clearInterval(t),_(e))}),10)}(e))}))}return u=[{name:"sentry",check:function(){return void 0!==hj.Sentry},url:"".concat(hj.scriptDomain).concat(null===(t=r.f.SENTRY)||void 0===t?void 0:t.js),state:"unloaded",onLoad:function(){var e=new hj.Sentry.BrowserClient({dsn:hj.errorUrl,environment:hj.environmentID,release:"insights-client-script-"+window.hjBootstrap.revision,sampleRate:m,defaultIntegrations:!1,integrations:[],beforeSend:function(e){if(!/.*Google.*/.test(window.navigator.userAgent))return function e(t){return hj.hq.each(t,(function(n,r){r&&"object"===i(r)?e(r):r&&"string"==typeof r&&-1==b.indexOf(n)&&(r=(r=(r=(r=r.replace(h,"<XXX>")).replace(l,"<<EMAIL>>")).replace(d,"123456789012")).replace(f,"<******>"),t[n]=r)})),t}(e)}});(o=new hj.Sentry.Hub(e)).setUser({id:_hjSettings.hjid}),o.getScope().addEventProcessor(c)}}],g.sendException=function(e){try{o.captureException(e.exception,{tags:{logger:e.module}})}catch(e){n("Failed to log exception: "+e,"Exception")}},g.tryCatch=function(e,t){return function(){try{return e.apply(this,arguments)}catch(e){if(w&&console.error(e),window.__TESTS__)throw e;g.log&&g.log(e,t)}}},g.log=function(e,t){var r;n("Exception occurred in '"+t+"'","Exception",e),r={exception:e,module:t},p.length<j&&(S()||E(),p.push(r))},g.getQueue=function(){return p},g.startProcessing=function(){g.isProcessing&&!g.isProcessing()&&(a=setInterval((function(){var e;(e=p.shift())&&(g.sendException&&g.sendException(e),s++),s>=j&&g.stopProcessing&&g.stopProcessing()}),y))},g.isProcessing=function(){return null!==a},g.stopProcessing=function(){g.isProcessing&&g.isProcessing()&&a&&(clearInterval(a),a=null)},g};function l(){window.hj=window.hj||function(){(window.hj.q=window.hj.q||[]).push(arguments)},window._hjSettings=window._hjSettings||{},hj.defaults={host:"in.hotjar.com",environment:"live",environmentID:"live",insightsHost:"insights.hotjar.com",insightsApiHost:"insights.hotjar.com",staticHost:"static.hotjar.com",varsHost:"vars.hotjar.com",surveysHost:"surveys.hotjar.com",errorUrl:"https://<EMAIL>/1803150",identifyEndpoint:"https://identify.hotjar.com",viewCounterEndpoint:"https://vc.hotjar.io/sessions",viewCounterHitPercentage:.25,surveyImpressionsEndpoint:"https://surveystats.hotjar.io/hit"},hj.host=_hjSettings.host||hj.defaults.host,hj.insightsHost=_hjSettings.insightsHost||hj.defaults.insightsHost,hj.insightsApiHost=_hjSettings.insightsApiHost||hj.defaults.insightsApiHost,hj.staticHost=_hjSettings.staticHost||hj.defaults.staticHost,hj.varsHost=_hjSettings.varsHost||hj.defaults.varsHost,hj.surveysHost=_hjSettings.surveysHost||hj.defaults.surveysHost,hj.errorUrl=void 0!==_hjSettings.errorUrl?_hjSettings.errorUrl:hj.defaults.errorUrl,hj.environment=_hjSettings.environment||hj.defaults.environment,hj.environmentID=_hjSettings.environmentID||hj.defaults.environmentID,hj.identifyEndpoint=_hjSettings.identifyEndpoint||hj.defaults.identifyEndpoint,hj.viewCounterEndpoint=void 0!==_hjSettings.viewCounterEndpoint?_hjSettings.viewCounterEndpoint:hj.defaults.viewCounterEndpoint,hj.viewCounterHitPercentage=void 0!==_hjSettings.viewCounterHitPercentage?_hjSettings.viewCounterHitPercentage:hj.defaults.viewCounterHitPercentage,hj.surveyImpressionsEndpoint=void 0!==_hjSettings.surveyImpressionsEndpoint?_hjSettings.surveyImpressionsEndpoint:hj.defaults.surveyImpressionsEndpoint,hj.exceptions=u(),hj.tryCatch=hj.exceptions.tryCatch}},function(e,t){hj.tryCatch((function(){if(void 0!==window.MutationObserver||void 0!==window.WebKitMutationObserver||void 0!==window.MozMutationObserver){var e,t=(this||{}).__extends||function(e,t){function n(){this.constructor=e}for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);n.prototype=t.prototype,e.prototype=new n};if(void 0===(e="undefined"!=typeof WebKitMutationObserver?WebKitMutationObserver:MutationObserver))throw console.error("DOM Mutation Observers are required."),console.error("https://developer.mozilla.org/en-US/docs/DOM/MutationObserver"),Error("DOM Mutation Observers are required");var n,r=function(){function e(){this.nodes=[],this.values=[]}return e.prototype.isIndex=function(e){return+e==e>>>0},e.prototype.nodeId=function(t){var n=t[e.ID_PROP];return n||(n=t[e.ID_PROP]=e.nextId_++),n},e.prototype.set=function(e,t){var n=this.nodeId(e);this.nodes[n]=e,this.values[n]=t},e.prototype.get=function(e){return e=this.nodeId(e),this.values[e]},e.prototype.has=function(e){return this.nodeId(e)in this.nodes},e.prototype.deleteNode=function(e){e=this.nodeId(e),delete this.nodes[e],this.values[e]=void 0},e.prototype.keys=function(){var e,t=[];for(e in this.nodes)this.isIndex(e)&&t.push(this.nodes[e]);return t},e.prototype.getValues=function(){var e,t=[];for(e in this.values)this.isIndex(e)&&t.push(this.values[e]);return t},e.ID_PROP="__hj_mutation_summary_node_map_id__",e.nextId_=1,e}();(m=n||(n={}))[m.STAYED_OUT=0]="STAYED_OUT",m[m.ENTERED=1]="ENTERED",m[m.STAYED_IN=2]="STAYED_IN",m[m.REPARENTED=3]="REPARENTED",m[m.REORDERED=4]="REORDERED",m[m.EXITED=5]="EXITED";var i=function(){function e(e,t,n,r,i,o,a,s){void 0===t&&(t=!1),void 0===n&&(n=!1),void 0===r&&(r=!1),void 0===i&&(i=null),void 0===o&&(o=!1),void 0===a&&(a=null),void 0===s&&(s=null),this.node=e,this.childList=t,this.attributes=n,this.characterData=r,this.oldParentNode=i,this.added=o,this.attributeOldValues=a,this.characterDataOldValue=s,this.isCaseInsensitive=this.node.nodeType===Node.ELEMENT_NODE&&this.node instanceof HTMLElement&&this.node.ownerDocument instanceof HTMLDocument}return e.prototype.getAttributeOldValue=function(e){if(this.attributeOldValues)return this.isCaseInsensitive&&(e=e.toLowerCase()),this.attributeOldValues[e]},e.prototype.getAttributeNamesMutated=function(){var e=[];if(!this.attributeOldValues)return e;for(var t in this.attributeOldValues)e.push(t);return e},e.prototype.attributeMutated=function(e,t){this.attributes=!0,this.attributeOldValues=this.attributeOldValues||{},e in this.attributeOldValues||(this.attributeOldValues[e]=t)},e.prototype.characterDataMutated=function(e){this.characterData||(this.characterData=!0,this.characterDataOldValue=e)},e.prototype.removedFromParent=function(e){this.childList=!0,this.added||this.oldParentNode?this.added=!1:this.oldParentNode=e},e.prototype.insertedIntoParent=function(){this.added=this.childList=!0},e.prototype.getOldParent=function(){if(this.childList){if(this.oldParentNode)return this.oldParentNode;if(this.added)return null}return this.node.parentNode},e}(),o=function(){this.added=new r,this.removed=new r,this.maybeMoved=new r,this.oldPrevious=new r,this.moved=void 0},a=function(e){function n(t,n){e.call(this),this.rootNode=t,this.wasReachableCache=this.reachableCache=void 0,this.anyCharacterDataChanged=this.anyAttributesChanged=this.anyParentsChanged=!1;for(var r=0;r<n.length;r++){var i=n[r];switch(i.type){case"childList":this.anyParentsChanged=!0;for(var o=0;o<i.removedNodes.length;o++){var a=i.removedNodes[o];this.getChange(a).removedFromParent(i.target)}for(o=0;o<i.addedNodes.length;o++)a=i.addedNodes[o],this.getChange(a).insertedIntoParent();break;case"attributes":this.anyAttributesChanged=!0,(o=this.getChange(i.target)).attributeMutated(i.attributeName,i.oldValue);break;case"characterData":this.anyCharacterDataChanged=!0,(o=this.getChange(i.target)).characterDataMutated(i.oldValue)}}}return t(n,e),n.prototype.getChange=function(e){var t=this.get(e);return t||(t=new i(e),this.set(e,t)),t},n.prototype.getOldParent=function(e){var t=this.get(e);return t?t.getOldParent():e.parentNode},n.prototype.getIsReachable=function(e){if(e===this.rootNode)return!0;if(!e)return!1;this.reachableCache=this.reachableCache||new r;var t=this.reachableCache.get(e);return void 0===t&&(t=this.getIsReachable(e.parentNode),this.reachableCache.set(e,t)),t},n.prototype.getWasReachable=function(e){if(e===this.rootNode)return!0;if(!e)return!1;this.wasReachableCache=this.wasReachableCache||new r;var t=this.wasReachableCache.get(e);if(void 0===t){var n=this.getOldParent(e);if(n===e)return!1;t=this.getWasReachable(n),this.wasReachableCache.set(e,t)}return t},n.prototype.reachabilityChange=function(e){return this.getIsReachable(e)?this.getWasReachable(e)?2:1:this.getWasReachable(e)?5:0},n}(r),s=function(){function e(e,t,n,i,o){this.rootNode=e,this.mutations=t,this.selectors=n,this.calcReordered=i,this.calcOldPreviousSibling=o,this.treeChanges=new a(e,t),this.entered=[],this.exited=[],this.stayedIn=new r,this.visited=new r,this.matchCache=this.characterDataOnly=this.childListChangeMap=void 0,this.processMutations()}return e.prototype.processMutations=function(){if(this.treeChanges.anyParentsChanged||this.treeChanges.anyAttributesChanged)for(var e=this.treeChanges.keys(),t=0;t<e.length;t++)this.visitNode(e[t],void 0)},e.prototype.visitNode=function(e,t){if(!this.visited.has(e)){this.visited.set(e,!0);var n=this.treeChanges.get(e),r=t;if((n&&n.childList||null==r)&&(r=this.treeChanges.reachabilityChange(e)),0!==r){if(this.matchabilityChange(e),1===r)this.entered.push(e);else if(5===r)this.exited.push(e),this.ensureHasOldPreviousSiblingIfNeeded(e);else if(2===r){var i=2;n&&n.childList&&(n.oldParentNode!==e.parentNode?(i=3,this.ensureHasOldPreviousSiblingIfNeeded(e)):this.calcReordered&&this.wasReordered(e)&&(i=4)),this.stayedIn.set(e,i)}if(2!==r)for(n=e.firstChild;n;n=n.nextSibling)this.visitNode(n,r)}}},e.prototype.ensureHasOldPreviousSiblingIfNeeded=function(e){if(this.calcOldPreviousSibling){this.processChildlistChanges();var t=e.parentNode,n=this.treeChanges.get(e);n&&n.oldParentNode&&(t=n.oldParentNode),(n=this.childListChangeMap.get(t))||(n=new o,this.childListChangeMap.set(t,n)),n.oldPrevious.has(e)||n.oldPrevious.set(e,e.previousSibling)}},e.prototype.getChanged=function(e,t,n){for(this.selectors=t,this.characterDataOnly=n,t=0;t<this.entered.length;t++){n=this.entered[t];var r=this.matchabilityChange(n);(1===r||2===r)&&e.added.push(n)}var i=this.stayedIn.keys();for(t=0;t<i.length;t++)n=i[t],1===(r=this.matchabilityChange(n))?e.added.push(n):5===r?e.removed.push(n):2===r&&(e.reparented||e.reordered)&&(r=this.stayedIn.get(n),e.reparented&&3===r?e.reparented.push(n):e.reordered&&4===r&&e.reordered.push(n));for(t=0;t<this.exited.length;t++)n=this.exited[t],(5===(r=this.matchabilityChange(n))||2===r)&&e.removed.push(n)},e.prototype.getOldParentNode=function(e){var t=this.treeChanges.get(e);if(t&&t.childList)return t.oldParentNode?t.oldParentNode:null;if(0===(t=this.treeChanges.reachabilityChange(e))||1===t)throw Error("getOldParentNode requested on invalid node.");return e.parentNode},e.prototype.getOldPreviousSibling=function(e){var t=e.parentNode,n=this.treeChanges.get(e);if(n&&n.oldParentNode&&(t=n.oldParentNode),!(t=this.childListChangeMap.get(t)))throw Error("getOldPreviousSibling requested on invalid node.");return t.oldPrevious.get(e)},e.prototype.getOldAttribute=function(e,t){var n=this.treeChanges.get(e);if(!n||!n.attributes)throw Error("getOldAttribute requested on invalid node.");if(void 0===(n=n.getAttributeOldValue(t)))throw Error("getOldAttribute requested for unchanged attribute name.");return n},e.prototype.attributeChangedNodes=function(e){if(!this.treeChanges.anyAttributesChanged)return{};var t,n;if(e){t={},n={};for(var r=0;r<e.length;r++){t[o=e[r]]=!0,n[o.toLowerCase()]=o}}e={};var i=this.treeChanges.keys();for(r=0;r<i.length;r++){var o=i[r],a=this.treeChanges.get(o);if(a.attributes&&2===this.treeChanges.reachabilityChange(o)&&2===this.matchabilityChange(o))for(var s=o,c=a.getAttributeNamesMutated(),u=0;u<c.length;u++)o=c[u],(!t||t[o]||a.isCaseInsensitive&&n[o])&&a.getAttributeOldValue(o)!==s.getAttribute(o)&&(n&&a.isCaseInsensitive&&(o=n[o]),e[o]=e[o]||[],e[o].push(s))}return e},e.prototype.getOldCharacterData=function(e){if(!(e=this.treeChanges.get(e))||!e.characterData)throw Error("getOldCharacterData requested on invalid node.");return e.characterDataOldValue},e.prototype.getCharacterDataChanged=function(){if(!this.treeChanges.anyCharacterDataChanged)return[];for(var e=this.treeChanges.keys(),t=[],n=0;n<e.length;n++){var r=e[n];if(2===this.treeChanges.reachabilityChange(r)){var i=this.treeChanges.get(r);i.characterData&&r.textContent!=i.characterDataOldValue&&t.push(r)}}return t},e.prototype.computeMatchabilityChange=function(e,t){this.matchCache||(this.matchCache=[]),this.matchCache[e.uid]||(this.matchCache[e.uid]=new r);var n=this.matchCache[e.uid],i=n.get(t);return void 0===i&&(i=e.matchabilityChange(t,this.treeChanges.get(t)),n.set(t,i)),i},e.prototype.matchabilityChange=function(e){var t=this;if(this.characterDataOnly)switch(e.nodeType){case Node.COMMENT_NODE:case Node.TEXT_NODE:return 2;default:return 0}if(!this.selectors)return 2;if(e.nodeType!==Node.ELEMENT_NODE)return 0;for(var n=this.selectors.map((function(n){return t.computeMatchabilityChange(n,e)})),r=0,i=0;2!==r&&i<n.length;){switch(n[i]){case 2:r=2;break;case 1:r=5===r?2:1;break;case 5:r=1===r?2:5}i++}return r},e.prototype.getChildlistChange=function(e){var t=this.childListChangeMap.get(e);return t||(t=new o,this.childListChangeMap.set(e,t)),t},e.prototype.processChildlistChanges=function(){if(!this.childListChangeMap){this.childListChangeMap=new r;for(var e=0;e<this.mutations.length;e++){var t=this.mutations[e];if("childList"==t.type&&(2===this.treeChanges.reachabilityChange(t.target)||this.calcOldPreviousSibling)){for(var n=this.getChildlistChange(t.target),i=t.previousSibling,o=function(e,t){e&&!n.oldPrevious.has(e)&&!n.added.has(e)&&!n.maybeMoved.has(e)&&(!t||!n.added.has(t)&&!n.maybeMoved.has(t))&&n.oldPrevious.set(e,t)},a=0;a<t.removedNodes.length;a++){var s=t.removedNodes[a];o(s,i),n.added.has(s)?n.added.deleteNode(s):(n.removed.set(s,!0),n.maybeMoved.deleteNode(s)),i=s}for(o(t.nextSibling,i),a=0;a<t.addedNodes.length;a++)s=t.addedNodes[a],n.removed.has(s)?(n.removed.deleteNode(s),n.maybeMoved.set(s,!0)):n.added.set(s,!0)}}}},e.prototype.wasReordered=function(e){function t(e){if(!e||!o.maybeMoved.has(e))return!1;var n=o.moved.get(e);if(void 0!==n)return n;if(a.has(e))n=!0;else{if(a.set(e,!0),c.has(e))n=c.get(e);else{for(n=e.previousSibling;n&&(o.added.has(n)||t(n));)n=n.previousSibling;c.set(e,n)}n=n!==function e(n){var r=s.get(n);if(void 0!==r)return r;for(r=o.oldPrevious.get(n);r&&(o.removed.has(r)||t(r));)r=e(r);return void 0===r&&(r=n.previousSibling),s.set(n,r),r}(e)}return a.has(e)?(a.deleteNode(e),o.moved.set(e,n)):n=o.moved.get(e),n}if(!this.treeChanges.anyParentsChanged)return!1;this.processChildlistChanges();var n=e.parentNode,i=this.treeChanges.get(e);i&&i.oldParentNode&&(n=i.oldParentNode);var o=this.childListChangeMap.get(n);if(!o)return!1;if(o.moved)return o.moved.get(e);o.moved=new r;var a=new r,s=new r,c=new r;return o.maybeMoved.keys().forEach(t),o.moved.get(e)},e}(),c=function(){function e(e,t){var n=this;if(this.projection=e,this.added=[],this.removed=[],this.reparented=t.all||t.element||t.characterData?[]:void 0,this.reordered=t.all?[]:void 0,e.getChanged(this,t.elementFilter,t.characterData),t.all||t.attribute||t.attributeList){var r=e.attributeChangedNodes(t.attribute?[t.attribute]:t.attributeList);t.attribute?this.valueChanged=r[t.attribute]||[]:(this.attributeChanged=r,t.attributeList&&t.attributeList.forEach((function(e){n.attributeChanged.hasOwnProperty(e)||(n.attributeChanged[e]=[])})))}(t.all||t.characterData)&&(r=e.getCharacterDataChanged(),t.characterData?this.valueChanged=r:this.characterDataChanged=r),this.reordered&&(this.getOldPreviousSibling=e.getOldPreviousSibling.bind(e))}return e.prototype.getOldParentNode=function(e){return this.projection.getOldParentNode(e)},e.prototype.getOldAttribute=function(e,t){return this.projection.getOldAttribute(e,t)},e.prototype.getOldCharacterData=function(e){return this.projection.getOldCharacterData(e)},e.prototype.getOldPreviousSibling=function(e){return this.projection.getOldPreviousSibling(e)},e}(),u=/[a-zA-Z_]+/,l=/[a-zA-Z0-9_\-]+/;function h(e){return'"'+e.replace(/"/,'\\"')+'"'}var d=function(){function e(){}return e.prototype.matches=function(e){if(null===e)return!1;if(void 0===this.attrValue)return!0;if(!this.contains)return this.attrValue==e;e=e.split(" ");for(var t=0;t<e.length;t++)if(this.attrValue===e[t])return!0;return!1},e.prototype.toString=function(){return"class"===this.attrName&&this.contains?"."+this.attrValue:"id"!==this.attrName||this.contains?this.contains?"["+this.attrName+"~="+h(this.attrValue)+"]":"attrValue"in this?"["+this.attrName+"="+h(this.attrValue)+"]":"["+this.attrName+"]":"#"+this.attrValue},e}(),f=function(){function e(){this.uid=e.nextUid++,this.qualifiers=[]}var t;return Object.defineProperty(e.prototype,"caseInsensitiveTagName",{get:function(){return this.tagName.toUpperCase()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"selectorString",{get:function(){return this.tagName+this.qualifiers.join("")},enumerable:!0,configurable:!0}),e.prototype.isMatching=function(t){return t[e.matchesSelector](this.selectorString)},e.prototype.wasMatching=function(e,t,n){if(!t||!t.attributes)return n;if("*"!==(r=t.isCaseInsensitive?this.caseInsensitiveTagName:this.tagName)&&r!==e.tagName)return!1;for(var r=[],i=!1,o=0;o<this.qualifiers.length;o++){var a=this.qualifiers[o],s=t.getAttributeOldValue(a.attrName);r.push(s),i=i||void 0!==s}if(!i)return n;for(o=0;o<this.qualifiers.length;o++)if(a=this.qualifiers[o],void 0===(s=r[o])&&(s=e.getAttribute(a.attrName)),!a.matches(s))return!1;return!0},e.prototype.matchabilityChange=function(e,t){var n=this.isMatching(e);return n?this.wasMatching(e,t,n)?2:1:this.wasMatching(e,t,n)?5:0},e.parseSelectors=function(t){function n(){i&&(o&&(i.qualifiers.push(o),o=void 0),s.push(i)),i=new e}function r(){o&&i.qualifiers.push(o),o=new d}for(var i,o,a,s=[],c=/\s/,h=1,f=0;f<t.length;){var g=t[f++];switch(h){case 1:if(g.match(u)){n(),i.tagName=g,h=2;break}if("*"==g){n(),i.tagName="*",h=3;break}if("."==g){n(),r(),i.tagName="*",o.attrName="class",o.contains=!0,h=4;break}if("#"==g){n(),r(),i.tagName="*",o.attrName="id",h=4;break}if("["==g){n(),r(),i.tagName="*",o.attrName="",h=6;break}if(g.match(c))break;throw Error("Invalid or unsupported selector syntax.");case 2:if(g.match(l)){i.tagName+=g;break}if("."==g){r(),o.attrName="class",o.contains=!0,h=4;break}if("#"==g){r(),o.attrName="id",h=4;break}if("["==g){r(),o.attrName="",h=6;break}if(g.match(c)){h=14;break}if(","==g){h=1;break}throw Error("Invalid or unsupported selector syntax.");case 3:if("."==g){r(),o.attrName="class",o.contains=!0,h=4;break}if("#"==g){r(),o.attrName="id",h=4;break}if("["==g){r(),o.attrName="",h=6;break}if(g.match(c)){h=14;break}if(","==g){h=1;break}throw Error("Invalid or unsupported selector syntax.");case 4:if(g.match(u)){o.attrValue=g,h=5;break}throw Error("Invalid or unsupported selector syntax.");case 5:if(g.match(l)){o.attrValue+=g;break}if("."==g){r(),o.attrName="class",o.contains=!0,h=4;break}if("#"==g){r(),o.attrName="id",h=4;break}if("["==g){r(),h=6;break}if(g.match(c)){h=14;break}if(","==g){h=1;break}throw Error("Invalid or unsupported selector syntax.");case 6:if(g.match(u)){o.attrName=g,h=7;break}if(g.match(c))break;throw Error("Invalid or unsupported selector syntax.");case 7:if(g.match(l)){o.attrName+=g;break}if(g.match(c)){h=8;break}if("~"==g){o.contains=!0,h=9;break}if("="==g){o.attrValue="",h=11;break}if("]"==g){h=3;break}throw Error("Invalid or unsupported selector syntax.");case 8:if("~"==g){o.contains=!0,h=9;break}if("="==g){o.attrValue="",h=11;break}if("]"==g){h=3;break}if(g.match(c))break;throw Error("Invalid or unsupported selector syntax.");case 9:if("="==g){o.attrValue="",h=11;break}throw Error("Invalid or unsupported selector syntax.");case 10:if("]"==g){h=3;break}if(g.match(c))break;throw Error("Invalid or unsupported selector syntax.");case 11:if(g.match(c))break;if('"'==g||"'"==g){a=g,h=13;break}o.attrValue+=g,h=12;break;case 12:if(g.match(c)){h=10;break}if("]"==g){h=3;break}if("'"==g||'"'==g)throw Error("Invalid or unsupported selector syntax.");o.attrValue+=g;break;case 13:if(g==a){h=10;break}o.attrValue+=g;break;case 14:if(g.match(c))break;if(","==g){h=1;break}throw Error("Invalid or unsupported selector syntax.")}}switch(h){case 1:case 2:case 3:case 5:case 14:n();break;default:throw Error("Invalid or unsupported selector syntax.")}if(!s.length)throw Error("Invalid or unsupported selector syntax.");return s},e.nextUid=1,e.matchesSelector="function"==typeof(t=document.createElement("div")).webkitMatchesSelector?"webkitMatchesSelector":"function"==typeof t.mozMatchesSelector?"mozMatchesSelector":"function"==typeof t.msMatchesSelector?"msMatchesSelector":"matchesSelector",e}(),g=/^([a-zA-Z:_]+[a-zA-Z0-9_\-:\.]*)$/;function p(e){if("string"!=typeof e)throw Error("Invalid request option. attribute must be a non-zero length string.");if(!(e=e.trim()))throw Error("Invalid request option. attribute must be a non-zero length string.");if(!e.match(g))throw Error("Invalid request option. invalid attribute name: "+e);return e}function v(e){if(!e.trim().length)throw Error("Invalid request option: elementAttributes must contain at least one attribute.");var t={},n={};e=e.split(/\s+/);for(var r=0;r<e.length;r++){if(i=e[r]){var i,o=(i=p(i)).toLowerCase();if(t[o])throw Error("Invalid request option: observing multiple case variations of the same attribute is not supported.");n[i]=!0,t[o]=!0}}return Object.keys(n)}hj.MutationSummary=function(){function t(n){var r=this;this.connected=!1,this.options=t.validateOptions(n),this.observerOptions=t.createObserverOptions(this.options.queries),this.root=this.options.rootNode,this.callback=this.options.callback,this.elementFilter=Array.prototype.concat.apply([],this.options.queries.map((function(e){return e.elementFilter?e.elementFilter:[]}))),this.elementFilter.length||(this.elementFilter=void 0),this.calcReordered=this.options.queries.some((function(e){return e.all})),this.queryValidators=[],t.createQueryValidator&&(this.queryValidators=this.options.queries.map((function(e){return t.createQueryValidator(r.root,e)}))),this.observer=new e((function(e){r.observerCallback(e)})),this.reconnect()}return t.createObserverOptions=function(e){function t(e){r.attributes&&!n||(r.attributes=!0,r.attributeOldValue=!0,e?(n=n||{},e.forEach((function(e){n[e]=!0,n[e.toLowerCase()]=!0}))):n=void 0)}var n,r={childList:!0,subtree:!0};return e.forEach((function(e){e.characterData?(r.characterData=!0,r.characterDataOldValue=!0):e.all?(t(),r.characterData=!0,r.characterDataOldValue=!0):e.attribute?t([e.attribute.trim()]):(e=function(e){var t={};return e.forEach((function(e){e.qualifiers.forEach((function(e){t[e.attrName]=!0}))})),Object.keys(t)}(e.elementFilter).concat(e.attributeList||[])).length&&t(e)})),n&&(r.attributeFilter=Object.keys(n)),r},t.validateOptions=function(e){for(var n in e)if(!(n in t.optionKeys))throw Error("Invalid option: "+n);if("function"!=typeof e.callback)throw Error("Invalid options: callback is required and must be a function");if(!e.queries||!e.queries.length)throw Error("Invalid options: queries must contain at least one query request object.");n={callback:e.callback,rootNode:e.rootNode||document,observeOwnChanges:!!e.observeOwnChanges,oldPreviousSibling:!!e.oldPreviousSibling,queries:[]};for(var r=0;r<e.queries.length;r++){var i=e.queries[r];if(i.all){if(1<Object.keys(i).length)throw Error("Invalid request option. all has no options.");n.queries.push({all:!0})}else if("attribute"in i){if((a={attribute:p(i.attribute)}).elementFilter=f.parseSelectors("*["+a.attribute+"]"),1<Object.keys(i).length)throw Error("Invalid request option. attribute has no options.");n.queries.push(a)}else if("element"in i){var o=Object.keys(i).length,a={element:i.element,elementFilter:f.parseSelectors(i.element)};if(i.hasOwnProperty("elementAttributes")&&(a.attributeList=v(i.elementAttributes),o--),1<o)throw Error("Invalid request option. element only allows elementAttributes option.");n.queries.push(a)}else{if(!i.characterData)throw Error("Invalid request option. Unknown query request.");if(1<Object.keys(i).length)throw Error("Invalid request option. characterData has no options.");n.queries.push({characterData:!0})}}return n},t.prototype.createSummaries=function(e){if(!e||!e.length)return[];e=new s(this.root,e,this.elementFilter,this.calcReordered,this.options.oldPreviousSibling);for(var t=[],n=0;n<this.options.queries.length;n++)t.push(new c(e,this.options.queries[n]));return t},t.prototype.checkpointQueryValidators=function(){this.queryValidators.forEach((function(e){e&&e.recordPreviousState()}))},t.prototype.runQueryValidators=function(e){this.queryValidators.forEach((function(t,n){t&&t.validate(e[n])}))},t.prototype.changesToReport=function(e){return e.some((function(e){return!!("added removed reordered reparented valueChanged characterDataChanged".split(" ").some((function(t){return e[t]&&e[t].length}))||e.attributeChanged&&Object.keys(e.attributeChanged).some((function(t){return!!e.attributeChanged[t].length})))}))},t.prototype.observerCallback=function(e){this.options.observeOwnChanges||this.observer.disconnect(),e=this.createSummaries(e),this.runQueryValidators(e),this.options.observeOwnChanges&&this.checkpointQueryValidators(),this.changesToReport(e)&&this.callback(e),!this.options.observeOwnChanges&&this.connected&&(this.checkpointQueryValidators(),this.observer.observe(this.root,this.observerOptions))},t.prototype.reconnect=function(){if(this.connected)throw Error("Already connected");this.observer.observe(this.root,this.observerOptions),this.connected=!0,this.checkpointQueryValidators()},t.prototype.takeSummaries=function(){if(!this.connected)throw Error("Not connected");var e=this.createSummaries(this.observer.takeRecords());return this.changesToReport(e)?e:void 0},t.prototype.disconnect=function(){var e=this.takeSummaries();return this.observer.disconnect(),this.connected=!1,e},t.NodeMap=r,t.parseElementFilter=f.parseSelectors,t.optionKeys={callback:!0,queries:!0,rootNode:!0,oldPreviousSibling:!0,observeOwnChanges:!0},t}()}var m}),"mutation-summary")()},function(e,t){hj.tryCatch((function(){hj.xcom=hj.tryCatch((function(){var e,t={},n=[],r=1,i=2,o=10,a=r,s="https://"+hj.insightsHost+"/x",c=hj.tryCatch((function(){if(a==r){window.addEventListener?window.addEventListener("message",u,!1):window.attachEvent("onmessage",u),a=i;var t=document.createElement("iframe");t.style.position="fixed",t.style.top=-100,t.style.left=-100,t.width=1,t.height=1,t.id="_hjXcomProxyFrame",t.src=s,document.body.appendChild(t),e=document.getElementById("_hjXcomProxyFrame")}}),"data");t.send=hj.tryCatch((function(t,r){a==o?e.contentWindow.postMessage({eventId:t,data:r},"*"):(n.push({eventId:t,data:r}),c())}));var u=function(e){"knockknock"==e.data.eventId&&(a=o,n.forEach((function(e){t.send(e.eventId,e.data)})),n=[])};return t}),"xcom")()}),"xcom")()},function(e,t,n){"use strict";n.r(t);var r=n(22),i=n(27);hj.store={session:i.a,recording:r.a}},function(e,t,n){"use strict";n.r(t);var r=n(22),i=n(27),o=n(71),a=n(7),s=n(29);hj.visitData=hj.tryCatch((function(){var e={};return e.getPageVisitInfo=hj.tryCatch((function(e,t){var n=r.a.get("pageInfo"),i=navigator.userAgent,o=navigator.language,a={script_revision:window.hjBootstrap.revision,user_agent:i||"",accept_language:o||"",page_url:(null==n?void 0:n.url)||"",url_hash:(null==n?void 0:n.urlMD5)||"",window_width:e.width,window_height:e.height,site_id:t},s=document.referrer;return""!==s&&(a.referrer=s),a}),"data"),e.track=hj.tryCatch((function(e){var t=Object(s.a)();hj.eventStream.setCurrentPageVisitKey(t),e=e||document.location.href,hj.log.debug("Tracking: ".concat(e),"visitData"),r.a.set("pageVisitKey",t),r.a.set("pageInfo",{url:e,urlMD5:hj.md5(hj.b64EncodeUnicode(e))}),i.a.get("user.id")||Object(o.c)()}),"data"),e.trackView=hj.tryCatch((function(){if(null===a.a.items.ABSOLUTE_SESSION_IN_PROGRESS.get({resetExpiry:!0})){hj.log.debug("viewcounter: Determining if we should track this session...","visitData");var e=Math.random(),t=hj.viewCounterEndpoint&&hj.settings.site_id&&e<=hj.viewCounterHitPercentage,n="s=".concat(hj.viewCounterHitPercentage,"&r=").concat(e),r="".concat(hj.viewCounterEndpoint,"/").concat(hj.settings.site_id,"?").concat(n);t?(a.a.items.ABSOLUTE_SESSION_IN_PROGRESS.set(1,!0),hj.ajax.get(r),hj.log.debug("viewcounter: This session was tracked.","visitData",{mathRandomResult:e,viewCounterHitPercentage:hj.viewCounterHitPercentage})):(a.a.items.ABSOLUTE_SESSION_IN_PROGRESS.set(0,!0),hj.log.debug("viewcounter: This session will NOT be tracked.","visitData",{mathRandomResult:e,viewCounterHitPercentage:hj.viewCounterHitPercentage}))}else hj.log.debug("viewcounter: Session already checked. Skipping shouldTrackSession check.","visitData")}),"data"),e}),"data")(),hj.pageVisit=hj.visitData},function(e,t){hj.event=function(){var e={},t={},n={};function r(e,t,n){e[t]||(e[t]=[]),e[t].push(n)}return e.listen=hj.tryCatch((function(i,o){r(t,i,o),function(t){n[t]&&(n[t].forEach((function(n){e.signal(t,n)})),delete n[t])}(i)}),"hj.event.listen"),e.removeListener=hj.tryCatch((function(e,n){var r=t[e];if(r){var i=r.indexOf(n);-1!==i&&r.splice(i,1)}}),"hj.event.removeListener"),e.signal=hj.tryCatch((function(e,i,o){t[e]?t[e].forEach((function(e){e(i)})):o?r(n,e,i):n[e]=[i]}),"hj.event.signal"),e.clearAllListeners=hj.tryCatch((function(){t=[],n=[]}),"data"),e}()},function(e,t){function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){return l(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){i=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}(e,t)||c(e,t)||s()}function a(e){return l(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||c(e)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"==typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function l(e){if(Array.isArray(e))return e}hj.tryCatch((function(){var e=[],t=[],n=function(e){return Array.from(e.children).filter((function(e){return e.matches("slot")}))},i=function(e){var t;return"ShadowRoot"===(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)},s=function(e){var t;return i(null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e))},c=function(e,t){if(!e||!t)return[];var r=[],i=[];i.push({host:e,selector:t});for(var o=function(){var e=i.shift(),t=e.host,o=a(e.selector.replace("hj-shadow:","").replace("hj-lwc:","").split(">")),s=o[0],c=o.slice(1).join(">");n(t)[0]&&Array.from(n(t)).forEach((function(e){Array.from(e.assignedElements({flatten:!0})).forEach((function(e){e.matches(s)&&(c?i.push({host:e.shadowRoot||e,selector:c}):r.push(e))}))})),Array.from(t.children).filter((function(e){return e.matches(s)})).forEach((function(e){c?i.push({host:e.shadowRoot||e,selector:c}):r.push(e)}))};i.length>0;)o();return r},u=function(e){var t,n;if(void 0!==e.attr("data-hj-ignore-attributes"))return!0;if(document.body.hasAttribute("data-hj-ignore-attributes"))return!0;for(var r=null!==(t=null===(n=e.get(0))||void 0===n?void 0:n.parentElement)&&void 0!==t?t:null;null!==r&&r!==document.body;){if(r.hasAttribute("data-hj-ignore-attributes"))return!0;r=r.parentElement}return!1};function l(e,t){var n=/(#|@|&|~|=|<|>|`|'|:|"|!|;|,|\?|%|\}|\{|\.|\*|\+|\||\[|\]|\(|\)|\/|\^|\$)/g,r=/(\s|#|@|&|~|=|<|>|`|'|:|"|!|;|,|\?|%|\}|\{|\.|\*|\+|\||\[|\]|\(|\)|\/|\^|\$)/g,a=t.ignoreUUIDClasses?/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/:{test:function(){return!1}},l=hj.tryCatch((function(e){var n,r,i,a,l,g,p,v,m,y=function(e,t){var n,r;if(p=hj.hq(t),(null==t||null===(n=t.includes)||void 0===n?void 0:n.call(t,"hj-shadow:"))||(null==t||null===(r=t.includes)||void 0===r?void 0:r.call(t,"hj-lwc:"))){var i=new RegExp("^(.*?(".concat("hj-shadow:","|").concat("hj-lwc:",").*?)>(.*)$"),"i"),a=o(t.match(i),4),s=a[1],u=a[3];hj.hq.each(hj.hq(s.replace("hj-shadow:","").replace("hj-lwc:","")),(function(e,t){p=Array.from(p).concat(c(t.shadowRoot,u))}))}for(l=0;l<p.length;l++)if(p[l]===e[0])return l;return 0};if(!0!==t.getFullSelector&&!u(e)&&!s(e.get(0))){if(v=f(e.attr("id")),m=d(e.attr("name")),v)return"0:#"+v;if(m)return y(e,g='*[name="'+m+'"]')+":"+g}return y(e,g=h(e))+":"+((null===(n=g)||void 0===n||null===(r=n.includes)||void 0===r?void 0:r.call(n,"hj-lwc:"))?null===(i=g)||void 0===i||null===(a=i.replaceAll)||void 0===a?void 0:a.call(i,"hj-lwc:",""):g)}),"common"),h=hj.tryCatch((function(e,n){var r=e.get(0);if(!r)return n;if(void 0===n&&(n=""),e.is("html"))return"html"+n;var o=y(r.nodeName.toLowerCase());if(r.shadowRoot&&n&&(o=function(e){return!!e.shadowRoot&&!i(e.shadowRoot)}(r)?"".concat("hj-lwc:").concat(o):"".concat("hj-shadow:").concat(o)),!u(e)&&!s(r)){var a=f(e.attr("id"));if(a)return o+"#"+a+n;if("body"!==o||!t.ignoreBodyClasses){var c=m(e.attr("class"));c&&(o+=c)}}if(null!==r.assignedSlot&&void 0!==r.assignedSlot){for(var l=r.assignedSlot;null!==l.assignedSlot&&void 0!==l.assignedSlot;)l=l.assignedSlot;return h(hj.hq(l).parent(),">"+o+n)}return h(e.parent(),">"+o+n)}),"common"),d=function(e){var r=[];return!(void 0===(e=hj.hq.trim((e||"").replace(/\s\s+/g," ")))||""===e||e.indexOf("yui_")>-1)&&((e=e.replace(n,"\\$1")).split(/\s/g).forEach((function(e){!(r.length<t.maxClassesAllowed||0===t.maxClassesAllowed)||hj.hq.inArray(e,t.ignoreClassList)||a.test(e)||""===e||r.push(e)})),r.join(" "))},f=function(e){return!(void 0===(e=hj.hq.trim((e||"").replace(/\s\s+/g," ")))||""===e||e.indexOf("yui_")>-1||v(e))&&(e=e.replace(r,"\\$1"),e=p(e),e=g(e))},g=function(e){if(!e)return e;var t=e.charAt(0);return/\d/.test(t)?"\\3"+t+" "+e.slice(1):e},p=function(e){if(!e||"-"!==e.charAt(0))return e;var t=e.charAt(0),n=e.charAt(1);return/\d/.test(n)?t+"\\3"+n+" "+e.slice(2):e},v=function(e){return 1===e.length&&"-"===e},m=function(e){var r=[];return!(void 0===(e=hj.hq.trim((e||"").replace(/\s\s+/g," ")))||""===e||e.indexOf("yui_")>-1||v(e))&&((e=e.replace(n,"\\$1")).split(/\s/g).forEach((function(e){!(r.length<t.maxClassesAllowed||0===t.maxClassesAllowed)||hj.hq.inArray(e,t.ignoreClassList)||a.test(e)||""===e||r.push(g(p(e)))})),r.length?"."+r.join("."):"")},y=function(e){return e.replace(t.disallowedTagNameCharactersRE,"")};return l(e)}var h={2:{ignoreClassList:["over","hover","active","selected","scrolled"],ignoreBodyClasses:!0,ignoreUUIDClasses:!0,maxClassesAllowed:5,disallowedTagNameCharactersRE:/[^a-zA-Z0-9-_]/g}},d={2:r(r({},h[2]),{},{getFullSelector:!0})};hj.selector=function(t){return e[t=!t||t<2?2:t]||(e[t]={get:function(e){return l(e,h[t])}}),e[t]},hj.fullSelector=function(e){return t[e=!e||e<2?2:e]||(t[e]={get:function(t){return l(t,d[e])}}),t[e]}}))()},function(e,t,n){"use strict";n.r(t);var r=n(60),i=n(128);function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}hj.tryCatch((function(){hj.loader.registerModule("Command",function(){var e={},t={},n=window.hj.q,a=["ready","stateChange","trigger","session_event"],s=!1;function c(){var e=Array.prototype.slice.call(n.shift()),r=t[e[0]],i=e.slice(1);hj.log.debug("Processing command: "+e[0]+" "+i.map((function(e){return"object"!==o(e)&&"function"!=typeof e||null===e?e:JSON.stringify(e)})).join(", "),"command"),hj.hq.isFunction(r)?!function(e){return!hj.optOut||a.indexOf(e)>=0}(e[0])?hj.log.debug('Command "'+e[0]+'" blocked due to opt-out',"command"):hj.tryCatch(r,"command")(i):hj.log.debug('Unknown command: "'+e[0]+'"',"command"),n.length>0&&hj.tryCatch(c)()}function u(e){e[0]&&hj.event.signal("trigger:"+e[0])}function l(e){if(e[0]&&Array.isArray(e[0])){var t=hj.privacy.getTagsWithoutPII(e[0]);hj.behaviorData.tagRecording(t)}else hj.log.error("tagRecording was called with an invalid argument. Please provide an array of tags. For example: hj('tagRecording', ['tag1', 'tag2']).\n\nRead more here: https://help.hotjar.com/hc/en-us/articles/115011819488-How-to-Tag-your-Hotjar-Recordings")}return t.vpv=function(){},t.stateChange=function(e){var t=window.location.href;e&&e.length>=1&&e[0]&&(t=hj.url.getUrlFromString(e[0])),hj.log.debug('Changing state to URL "'+t+'"',"command"),hj.currentUrl&&hj.currentUrl!=t&&hj._init.reinit(t)},t.tagRecording=function(e){l(e)},t.trigger=function(e){u(e)},t.identify=function(e){if(e[1]){var t=e[0];t=!t&&0!==t||"null"===t||"undefined"===t?null:String(t),r.userAttributes.set(t,e[1])}else null!=e[0]&&"object"===o(e[0])&&r.userAttributes.set(null,e[0])},t.event=function(e){u(e),l([[e[0]]])},t.session_event=function(e){i.sessionEvents.set(e[0])},t.gaSetTracker=function(e){e[0]&&hj.integrations.google_analytics.sendHotjarUserId.setTracker(e[0])},t._xhr=function(){},t.ready=function(e){e.forEach((function(e){e()}))},e.run=function(){hj.command=this},e.activate=function(){s||(s=!0,Object.defineProperty(n,"push",{writable:!0,value:function(){for(var e=0;e<arguments.length;e+=1)this[this.length]=arguments[e];return c(),this.length}}),n.length>0&&c())},hj.initialVisitDataSent&&e.activate(),e}(),!0)}),"command")()},function(e,t,n){"use strict";n.r(t);var r=n(67),i=n(142),o=n(3),a=n(12),s=n(21),c=n(79),u=n(117),l=n(7),h=n(4),d=n(22);var f=function(e){if(d.a.set("active",!1),hj.settings.record){var t=void 0===hj.settings.record_targeting_rules||!hj.settings.record_targeting_rules.length;a.a.setRecordingEnabled(!!sessionStorage.getItem("_hjRecordingEnabled"));var n=sessionStorage.getItem("_hjRecordingLastActivity"),r=!n||h.a.now()-n<o.a.MAX_TIME_SINCE_LAST_RECORDING_ACTIVITY_IN_SESSION;hj.log.debug("_hjRecordingEnabled is set to "+a.a.isRecordingEnabled(),"recordings"),hj.log.debug("_hjRecordingLastActivity is set to "+n,"recordings"),hj.log.debug("_hjRecordingLastActivity still valid: "+r,"recordings"),a.a.isRecordingEnabled()&&r||t?u.a.start():hj.targeting.matchRules(hj.settings.record_targeting_rules,e,hj.tryCatch((function(){u.a.start()}),"behavior-data.maybeStartRecordings")),l.a.items.SESSION_RESUMED.get()&&(l.a.items.SESSION_RESUMED.clear(),u.a.resume())}};hj.tryCatch((function(){return hj.loader.registerModule("BehaviorData",(e={},hj.behaviorData={tagRecording:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;e=e||[];for(var i=[],o=d.a.get("autoTagsToProcess"),a=d.a.get("tagsToProcess"),c=0;c<e.length;c+=1){var u=hj.hq.trim(e[c]);u.length&&u.length<=hj.maxRecordingTagLength?function(){var e={name:u,time:hj.time.getNow(),timestamp:h.a.now()};hj.tryCatch((function(){n&&(e.selector=n.selector,e.page_x=n.pageX,e.page_y=n.pageY,e.offset_x=n.offsetX,e.offset_y=n.offsetY)}),"behavior-data.tagRecording")(),i.push(e)}():hj.log.warn('Invalid recording tag: " '.concat(u,' ", should have length 1.. ').concat(hj.maxRecordingTagLength," characters, was ").concat(u.length,"."))}if(i.length)if(d.a.get("active")){var l=t?r.a.AUTOTAG_RECORDING:r.a.TAG_RECORDING;Object(s.a)(l,i,!0).flush()}else t?(o=o.concat(i),d.a.set("autoTagsToProcess",o)):(a=a.concat(i),d.a.set("tagsToProcess",a))}},e.run=hj.tryCatch((function(t){hj.isPreview||Object(i.b)(t)||Object(c.a)("behavior-data.run")&&e.runRecordings(t)}),"behavior-data.run"),e.runRecordings=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:hj.hq.noop;Object(c.a)("behavior-data.runRecordings")&&(l.a.items.SESSION_RESUMED.get()&&a.a.trackSessionResumed(),f(e))},e),!1);var e}),"behavior-data")()},function(e,t,n){n(236),n(237),n(238),n(239)},function(e,t){hj.tryCatch((function(){hj.insertedRules=function(){var e,t={},n=[],r=!1,i=function(e,t,n,r){var i,o={};return o.rule=t,o.index=n,o.parentSelector=(i=e,hj.selector().get(hj.hq(i))),o.nodeId=hj.treeMirror.getNodeId(e),r&&(o.id=r),o};function o(e){n.forEach((function(t){t([e])}))}return t.init=function(){r||(e=CSSStyleSheet.prototype.insertRule,CSSStyleSheet.prototype.insertRule=function(){var t=Array.prototype.slice.call(arguments),n=e.apply(this,arguments),r=this.ownerNode||this.ownerHostNode,a=t[0],s=t[1];if(r instanceof Node&&r.ownerDocument===document){var c=this.sheetId||null,u=i(r,a,s,c);o(u)}else{var l=this;hj.treeMirror.onTreeMirrorUpdate((function(){var e=l.ownerNode||l.ownerHostNode;if(e instanceof Node&&e.ownerDocument===document){var t=l.sheetId||null;o(i(e,a,s,t))}}))}return n},r=!0)},t.register=function(e,r){n.push(e),r&&e(t.getCurrentInsertedRules())},t.getCurrentInsertedRules=hj.tryCatch((function(e){for(var t=Array.prototype.slice,n=t.call(document.styleSheets).filter((function(n){if(void 0!==e&&!0===e&&n.href&&0===n.href.indexOf("blob:"))return!0;var r="";if(n.href&&0!==n.href.indexOf("blob:"))return!1;try{n.cssRules&&n.cssRules.length}catch(e){return!1}if(0===n.cssRules.length)return!1;n.ownerNode&&void 0!==n.ownerNode.innerText?r=n.ownerNode.innerText:n.ownerNode&&void 0!==n.ownerNode.innerHTML&&(r=n.ownerNode.innerHTML);var i=r.match(/{/g);return(i?i.length:0)<function e(n){var r=0;return t.call(n).forEach((function(t){t.cssRules?(r++,r+=e(t.cssRules)):r++})),r}(n.cssRules)})),r=[],i=function(e,i){var o=n[e],a=hj.selector().get(hj.hq(o.ownerNode)),s=t.call(o.cssRules),c=s.length;s.forEach((function(e,t){r.push({parentSelector:a,rule:e.cssText,index:t+c,nodeId:void 0})}))},o=0,a=n.length;o<a;o++)i(o);return r}),"hj.insertedRules.getCurrentInsertedRules"),t.destroy=function(){e&&(CSSStyleSheet.prototype.insertRule=e,e=null,r=!1),n=[]},t}()}),"hj.insertedRules")()},function(e,t){hj.tryCatch((function(){hj.deletedRules=function(){var e,t={},n=!1,r=[],i=function(e,t,n){var r,i={};return i.index=t,i.parentSelector=(r=e,hj.selector().get(hj.hq(r))),i.nodeId=hj.treeMirror.getNodeId(e),n&&(i.id=n),i};function o(e){r.forEach((function(t){t([e])}))}return t.init=function(){n||(e=CSSStyleSheet.prototype.deleteRule,CSSStyleSheet.prototype.deleteRule=function(){var t=Array.prototype.slice.call(arguments),n=e.apply(this,arguments),r=this.ownerNode||this.ownerHostNode,a=t[0];if(r instanceof Node&&r.ownerDocument===document){var s=this.sheetId||null,c=i(r,a,s);o(c)}else{var u=this;hj.treeMirror.onTreeMirrorUpdate((function(){var e=u.ownerNode||u.ownerHostNode;if(e instanceof Node&&e.ownerDocument===document){var t=u.sheetId||null;o(i(e,a,t))}}))}return n},n=!0)},t.register=function(e){r.push(e)},t.destroy=function(){e&&(CSSStyleSheet.prototype.deleteRule=e,e=null,n=!1),r=[]},t}()}),"hj.deletedRules")()},function(e,t){hj.tryCatch((function(){var e,t;hj.cssBlobs=(t=[],(e={}).register=function(e){t.push(e)},e.handleBlobStyles=hj.tryCatch((function(e){var n=[];"link"===e.tagName.toLowerCase()&&"rel"in e.attributes&&"stylesheet"===e.attributes.rel.value&&"href"in e.attributes&&0===e.attributes.href.value.indexOf("blob:")&&setTimeout((function(){for(var r=hj.selector().get(hj.hq(e)),i=hj.treeMirror.getNodeId(e),o=0,a=e.sheet.cssRules.length;o<a;o++)n.push({parentSelector:r,rule:e.sheet.cssRules[o].cssText,nodeId:i,index:a+o});t.forEach((function(e){e(n)}))}),100)}),"hj.cssBlobs.apply"),e)}),"hj.cssBlobs")()},function(e,t,n){"use strict";n.r(t);var r=n(16);hj.tryCatch((function(){hj.adoptedStyleSheets=function(){var e={},t=[],n=null,i=!1;function o(e){t.forEach((function(t){t(e)}))}function a(e){return hj.selector().get(hj.hq(e))}return e.init=function(){i||(void 0!==window.ShadowRoot&&!!(n=Object.getOwnPropertyDescriptor(ShadowRoot.prototype,"adoptedStyleSheets"))&&Object.defineProperty(ShadowRoot.prototype,"adoptedStyleSheets",{set:function(){n.set.apply(this,arguments);var e=arguments[0],t=this.host,i={sheets:e.map((function(e){var n=[],i=e.cssRules.length;e.ownerHostNode||(e.ownerHostNode=t,e.sheetId=Object(r.b)());for(var o=0;o<i;o++)n.push(e.cssRules[o].cssText);return{id:e.sheetId,rules:n}}))};if(t instanceof Node&&document.contains(t))i.parentSelector=a(t),i.nodeId=hj.treeMirror.getNodeId(t),o(i);else{var s=this;hj.treeMirror.onTreeMirrorUpdate((function(){var e=s.host;i.parentSelector=a(e),i.nodeId=hj.treeMirror.getNodeId(e),o(i)}))}}}),i=!0)},e.register=function(e){t.push(e)},e.destroy=function(){n&&(Object.defineProperty(ShadowRoot.prototype,"adoptedStyleSheets",n),n=null,i=!1),t.length=0},e}()}),"hj.adoptedStyleSheets")()},function(e,t,n){"use strict";n.r(t);var r=n(56),i=n(12),o=n(16);hj.tryCatch((function(){var e,t,n,a,s;hj.loader.registerModule("UserBehavior",(e={},t=!1,n=function(){var e,t,n={},o={desktop:{time:600,distance:200,clicks:6},mobile:{time:600,distance:200,clicks:6},tablet:{time:600,distance:200,clicks:6},tv:{time:600,distance:200,clicks:6}},a={x:null,y:null,pageX:null,pageY:null},s=0;function c(){s=0,a.x=null,a.y=null,a.pageX=null,a.pageY=null}function u(n){var r,o,u,l,h;if(!(r=n.clientX,o=n.clientY,u=n.pageX,l=n.pageY,h=a.pageX&&a.pageX!==u||a.pageY&&a.pageY!==l,document.documentElement.clientWidth&&r>document.documentElement.clientWidth||document.documentElement.clientHeight&&o>document.documentElement.clientHeight||h||(s++,t&&clearInterval(t),t=setTimeout(c,e.time),function(t,n){var r=Math.abs(t-a.x),i=Math.abs(n-a.y);Math.sqrt(Math.pow(r,2)+Math.pow(i,2))>e.distance&&c()}(n.clientX,n.clientY),a.x=n.clientX,a.y=n.clientY,a.pageX=n.pageX,a.pageY=n.pageY,s!==e.clicks))){var d=null;hj.tryCatch((function(){var e,t,r=(null==n||null===(e=n.composedPath)||void 0===e||null===(t=e.call(n))||void 0===t?void 0:t.length)?n.composedPath()[0]:n.target,o=hj.selector(i.a.getSelectorVersion()).get(hj.hq(r));if(r&&"pageX"in n&&"pageY"in n&&void 0!==o){var a=hj.hq(r).offset(),s=n.pageX-a.left,c=n.pageY-a.top;d={selector:o,pageX:n.pageX,pageY:n.pageY,offsetX:s,offsetY:c}}}),"user-behaviour.rageClick.onClick")(),hj.autotag.rageClick(d)}}return n.listen=function(){e=o[Object(r.a)()],hj.hq(document).on("mousedown.user_behavior_rageclick",u)},n}(),a=function(){var e,t={},n=[];function r(){var e=[],t=Array.prototype.filter.call(hj.hq("form"),(function(t){for(var r=0;r<n.length;r++)if(n[r]===t)return!0;return e.push(t),!0}));e.forEach((function(e){hj.log.debug("Found new form.","autotag",e),hj.hq(e).on("submit.user_behavior_formsubmit",(function(){var t,n;t=e,n=hj.selector(i.a.getSelectorVersion()).get(hj.hq(t)),hj.autotag.formSubmit({selector:n})})),hj.hq(e).on("change",Object(o.g)((function(){var t,n;t=e,n=hj.selector(i.a.getSelectorVersion()).get(hj.hq(t)),hj.autotag.formInteract({selector:n})})))})),n=t}return t.listen=function(){e=setInterval(r,2e3)},t.stop=function(){clearInterval(e)},t}(),e.listen=function(){hj.url.getParameter("hjAutogeneratedRecording")&&hj.autotag.autogenerated()},s=e,hj.autotag=hj.tryCatch((function(){var e,r={autogenerated:(e="autogenerated",function(){o([e])})};function i(e,t,n){return function(r){r&&o(t.reduce((function(t,i){var o=e;return Object.keys(i).forEach((function(e){var t=i[e],a=r[t];null==a&&(a=""),n&&(a=n(t,a)),o+="."+e+":"+a})),t.push(o),t}),[]))}}function o(e,t){hj.log.debug("Sending autotags","autotag",e),hj.behaviorData.tagRecording(e,!0,null!=t?t:null)}function c(){var e,t,r;n.listen(),a.listen(),s.listen(),e=function(e,t){return t.replace(".e:",".E:").replace(".v:",".V:").replace(".c:",".C:")},t=function(e,t){return"string"==typeof t?t.replace(/\.|:/g,"_"):t},r={"poll.show":i("poll.show",[{},{id:"id"}]),"poll.send":i("poll.send",[{},{id:"id",r_id:"response_id"}]),"poll.question":i("poll.q",[{t:"type"},{t:"type",a:"answer"},{t:"type",id:"id"},{qid:"questionUuid",id:"id"},{a:"answer",qid:"questionUuid",id:"id"},{t:"type",a:"answer",id:"id"}],(function(e,t){return"type"!==e?t:{"rating-scale-5":"rating5","rating-scale-7":"rating7","net-promoter-score":"nps","single-close-ended":"singleClose","multiple-close-ended":"multiClose","single-open-ended-multiple-line":"singleOpenMulti","single-open-ended-single-line":"singleOpenSingle"}[t]||t})),"feedback.show":i("feedback.show",[{},{id:"id"}]),"feedback.send":i("feedback.send",[{},{id:"id"}]),"feedback.sentiment":i("feedback.sentiment",[{e:"emotion"},{e:"emotion",id:"id",r_id:"response_id"}]),"survey.show":i("survey.show",[{},{id:"id"}]),"survey.open":i("survey.open",[{},{id:"id"}]),"exp.go":i("exp.go",[{e:"experimentId",c:"containerId"},{e:"experimentId",v:"variantId",c:"containerId"}],e),"exp.opt":i("exp.opt",[{e:"experimentId"},{e:"experimentId",v:"variantId"}],e),"exp.ub":i("exp.ub",[{e:"experimentId"},{e:"experimentId",v:"variantId"}],e),"exp.abt":i("exp.abt",[{e:"experimentId"},{e:"experimentId",v:"variantId"}],e),"int.ga":i("int.ga",[{a:"action"},{a:"action",c:"category"},{a:"action",c:"category",l:"label"},{a:"action",c:"category",l:"label",v:"value"}],t),"int.mp":i("int.mp",[{event:"event"}],t),"int.hubspot":i("int.hubspot",[{utk:"utk"}])},Object.keys(r).forEach((function(e){hj.event.listen(e,r[e])}))}return r.formSubmit=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;o(["formsubmit"],e)},r.formInteract=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;o(["forminteract"],e)},r.rageClick=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;o(["rageclick"],e)},r.start=hj.tryCatch((function(){hj.features.hasFeature("settings.billing_v2")&&!t&&(c(),t=!0)}),"user-behavior.autotag.start"),r}),"user-behavior.autotag")(),e.run=Function.prototype,e),!1)}),"user-behavior")()},function(e,t,n){"use strict";n.r(t);var r=n(55),i=n(3);hj.tryCatch((function(){hj.feedback=function(){var e={loaded:!1};function t(){Object(r.a)(i.f.PREACT_INCOMING_FEEDBACK)}function n(){Object(r.a)(i.f.INCOMING_FEEDBACK)}function o(){var r=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e.loaded&&!r||(e.loaded=!0,hj.widget.setLanguage(hj.widget.feedbackData.language),hj.features.hasFeature("feedback.widgetV2")?t():n())}function a(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e.loaded&&!t||(e.loaded=!0,hj.widget.setLanguage(hj.widget.embeddedFeedbackWidgets[0].language),Object(r.a)(i.f.PREACT_INCOMING_FEEDBACK))}function s(t){var n=(hj.settings.feedback_widgets||[]).filter((function(e){var t=e.display_type;return"button"===(void 0===t?"button":t)}));hj.hq.each(n,(function(n,r){hj.targeting.matchRules(r.targeting,t,hj.tryCatch((function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];hj.log.debug("Feedback widget #"+r.id+" has matched.","feedback"),!hj.widget.data||hj.widget.data.id===r.id&&"incoming"===hj.widget.data.type?(r.created_epoch_time-=31536e4,hj.widget.addMatchingWidget("incoming",r.id,r.created_epoch_time,r.targeting_percentage,(function(){hj.widget.feedbackData=r,hj.tryCatch(o,"feedback-button-load")(t)}),e.remove)):hj.log.debug("Another feedback widget is already present.","feedback")}),"feedback.run.matchRules-callback"))}))}function c(e){var t=(hj.settings.feedback_widgets||[]).filter((function(e){return"inline"===e.display_type})).filter((function(t){return hj.targeting.matchRules(t.targeting,e,(function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];void 0!==hj.widget.embeddedFeedbackWidgets&&(hj.widget.embeddedFeedbackWidgets.push(t),hj.tryCatch(a,"feedback-embeddable-load")(e))}))}));hj.widget.embeddedFeedbackWidgets=t,t.length>0&&hj.tryCatch(a,"feedback-embeddable-load")()}return e.run=hj.tryCatch((function(t){e.loaded=!1,hj.tryCatch(s,"feedback-button")(t),hj.tryCatch(c,"feedback-embeddable")(t)}),"feedback"),e.remove=hj.tryCatch((function(e){hj.widget.feedbackData?(hj.hq("#_hj_feedback_container").length>0&&hj.hq("#_hj_feedback_container").parent().remove(),delete hj.widget.feedbackData,hj.hq(window).off("resize"),setTimeout((function(){e()}),1)):e()})),hj.isPreview&&(window._hjFeedbackReload=hj.tryCatch((function(e,t){hj.widget.feedbackData=e,hj.settings.legal_name=e.legal_name,hj.settings.privacy_policy_url=e.privacy_policy_url,t&&(hj.settings.features=t),hj.tryCatch(o,"feedback")(!0)}),"feedback")),e},hj.loader.registerModule("Feedback",hj.feedback(),!0)}),"feedback")()},function(e,t,n){"use strict";n.r(t);var r=n(142),i=n(55),o=n(3),a=n(31),s=n(7),c=n(16),u=n(37),l=n(29);hj.tryCatch((function(){hj.polls=function(){var e,t={};var n=hj.tryCatch((function(e,t){e.display_type!==a.a.INLINE_EMBEDDED&&function(e){if(hj.surveyImpressionsEndpoint&&!hj.isPreview){var t="".concat(hj.surveyImpressionsEndpoint,"?id=").concat(e,"&device=").concat(Object(c.a)());hj.log.debug("poll id: ".concat(e," recording impression."),"poll"),hj.ajax.get(t)}else hj.log.debug("poll id: ".concat(e," skipping recording impression."),"poll")}(e.id);var n={scriptSrc:"".concat(hj.scriptDomain).concat(o.f.SURVEY_ISOLATED.js)};hj.widget.renderSurvey(e,t,n)}),"polls");function h(e){return hj.features.hasFeature("survey.iframe.".concat(e.display_type))?o.f.SURVEY_BOOTSTRAPPER:o.f.SURVEY_V2}var d=hj.tryCatch((function(t,r){hj.widget.pollsData=hj.widget.pollsData||{},hj.widget.pollsData[t.id]=t,hj.widget.pollsResponsesUUID=hj.widget.pollsResponsesUUID||{},hj.widget.pollsResponsesUUID[t.id]=Object(l.a)(),hj.widget.setLanguage(t.language),hj.log.debug("Rendering poll widget.","poll");var o=h(t);o!==e?Object(i.a)(h(t),(function(){e=o,n(t,r)})):n(t,r)}),"polls");var f=hj.tryCatch((function(e){var t=JSON.parse(JSON.stringify(e));return function(e){hj.hq.each(e.content.questions,(function(e,t){t.answers&&hj.hq.each(t.answers,(function(e,t){t.index=e}))}))}(t),function(e){hj.hq.each(e.content.questions,(function(e,t){t.randomize_answer_order&&(t.pin_last_to_bottom?t.answers=Object(c.i)(t.answers.slice(0,-1)).concat(t.answers.slice(-1)):Object(c.i)(t.answers))}))}(t),t}),"polls");return t.add=hj.tryCatch((function(e){hj.widget.pollsData=hj.widget.pollsData||{},hj.widget.pollsData[e.id]=f(e),hj.tryCatch(hj.rendering.callAccordingToCondition,"polls")(hj.widget.pollsData[e.id],"poll",d)}),"polls"),t.addEmbedded=hj.tryCatch((function(e,n){hj.widget.emptyMatchingWidgets();var r="external_link"===e.display_type?void 0:e.targeting_percentage;hj.widget.addMatchingWidget("poll",e.id,e.created_epoch_time,r,(function(){var t=f(e);t.skin="light",t.background="#ffffff",s.a.items.POLL_DONE.exists(e.id)&&"always"!==e.persist_condition&&(hj.log.debug("Offsite poll "+e.id+" was already submitted.","poll"),t.is_submitted=!0),hj.widget.pollsData=hj.widget.pollsData||{},hj.widget.pollsData[t.id]=t,d(t,n[0]),"function"==typeof window.hjRenderCallback&&window.hjRenderCallback({background:e.background})}),t.remove)}),"polls"),t.remove=hj.tryCatch((function(e,t){hj.widget.pollsData[t]?(Object(u.c)(document.body),Object(u.d)(document.body),delete hj.widget.pollsData[t],setTimeout((function(){e()}),1)):e()}),"polls"),t.run=hj.tryCatch((function(e){var n=hj.hq("._hj-survey-embed-container"),i=n.attr("data-survey-id"),o=Object(r.b)(e),c=!1;hj.hq.each(hj.settings.polls||[],(function(r,u){var l=u.display_type===a.a.EXTERNAL,h=u.display_type===a.a.POPOVER||u.display_type===a.a.FULL_SCREEN||u.display_type===a.a.BUTTON||u.display_type===a.a.INLINE_EMBEDDED;n.length>0&&l?u.uuid===i&&(hj.log.debug("Offsite poll #"+u.id+" has matched with the embedded UUID "+i,"poll"),c=!0,t.addEmbedded(u,n)):!o&&h&&hj.targeting.matchRules(u.targeting,e,hj.tryCatch((function(){hj.log.debug("Poll #"+u.id+" has matched.","poll"),s.a.items.POLL_DONE.exists(u.id)&&"always"!==u.persist_condition?hj.log.debug("Poll was already submitted.","poll"):hj.widget.addMatchingWidget("poll",u.id,u.created_epoch_time,u.targeting_percentage,(function(){return t.add(u)}),(function(e){return t.remove(e,u.id)}),u.display_type===a.a.INLINE_EMBEDDED)}),"polls.run.matchRules-callback"))})),o&&!c&&(hj.hq(document).trigger("hj-embedded-survey-mismatch"),hj.widgetDelay.clear(),hj.widget.emptyMatchingWidgets(),hj.log.debug("Could not match the embedded UUID.","poll"))}),"polls"),hj.isPreview&&(window._hjPollReload=hj.tryCatch((function(e,t){hj.widget.pollsData=hj.widget.pollsData||{};var n=f(e);hj.widget.pollsData[e.id]=n,hj.settings.legal_name=e.legal_name,hj.settings.privacy_policy_url=e.privacy_policy_url,t&&(hj.settings.features=t),hj.tryCatch((function(){d(n)}),n,"polls")()}),"polls")),t},hj.loader.registerModule("Polls",hj.polls(),!0)}),"polls")()},function(e,t,n){"use strict";n.r(t);var r=n(7),i=n(55),o=n(3),a=n(58),s=n(31),c=n(142);hj.tryCatch((function(){hj.loader.registerModule("Surveys",function(){var e={};function t(e){return e.display_type===s.a.EXTERNAL?Object(c.a)(e.uuid,hj.surveysHost):e.public_url}return e.run=hj.tryCatch((function(n){if(!Object(c.b)(n)){var a=(hj.settings.polls||[]).filter((function(e){return e.invite_enabled&&e.display_type===s.a.EXTERNAL})),u=(hj.settings.surveys||[]).concat(a);hj.hq.each(u||[],(function(a,s){hj.targeting.matchRules(s.targeting,n,hj.tryCatch((function(){hj.log.debug("Survey #"+s.id+" has matched.","survey"),r.a.items.SURVEY_INVITES_CLOSED.exists(s.id)?hj.log.debug("Survey was already viewed.","survey"):hj.widget.addMatchingWidget("survey",s.id,s.created_epoch_time,s.targeting_percentage,(function(){hj.survey.data=s,hj.rendering.callAccordingToCondition(hj.survey.data,"survey",hj.tryCatch((function(){var e;e=hj.survey.data,hj.widget.surveyInvitationData={id:e.id,effectiveShowBranding:e.effective_show_branding,title:e.invite.title,description:e.invite.description,button:e.invite.button,close:e.invite.close,url:t(e)},Object(i.a)(o.f.SURVEY_INVITATION)}),"polls"))}),e.remove)}),"surveys.run.matchRules-callback"))}))}}),"surveys"),e.remove=hj.tryCatch((function(e){hj.survey.data?(hj.hq(".".concat(a.a.SURVEY_INVITES)).length>0&&hj.hq(".".concat(a.a.SURVEY_INVITES)).remove(),delete hj.survey.data,setTimeout((function(){e()}),1)):e()})),e}(),!0)}),"surveys")()},function(e,t,n){var r,i,o;!function(n,a){"use strict";i=[],void 0===(o="function"==typeof(r=function(){function e(e){return e.charAt(0).toUpperCase()+e.substring(1)}function t(e){return function(){return this[e]}}var n=["isConstructor","isEval","isNative","isToplevel"],r=["columnNumber","lineNumber"],i=["fileName","functionName","source"],o=n.concat(r,i,["args"],["evalOrigin"]);function a(t){if(t)for(var n=0;n<o.length;n++)void 0!==t[o[n]]&&this["set"+e(o[n])](t[o[n]])}a.prototype={getArgs:function(){return this.args},setArgs:function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new TypeError("Args must be an Array");this.args=e},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(e){if(e instanceof a)this.evalOrigin=e;else{if(!(e instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new a(e)}},toString:function(){var e=this.getFileName()||"",t=this.getLineNumber()||"",n=this.getColumnNumber()||"",r=this.getFunctionName()||"";return this.getIsEval()?e?"[eval] ("+e+":"+t+":"+n+")":"[eval]:"+t+":"+n:r?r+" ("+e+":"+t+":"+n+")":e+":"+t+":"+n}},a.fromString=function(e){var t=e.indexOf("("),n=e.lastIndexOf(")"),r=e.substring(0,t),i=e.substring(t+1,n).split(","),o=e.substring(n+1);if(0===o.indexOf("@"))var s=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(o,""),c=s[1],u=s[2],l=s[3];return new a({functionName:r,args:i||void 0,fileName:c,lineNumber:u||void 0,columnNumber:l||void 0})};for(var s=0;s<n.length;s++)a.prototype["get"+e(n[s])]=t(n[s]),a.prototype["set"+e(n[s])]=function(e){return function(t){this[e]=Boolean(t)}}(n[s]);for(var c=0;c<r.length;c++)a.prototype["get"+e(r[c])]=t(r[c]),a.prototype["set"+e(r[c])]=function(e){return function(t){if(n=t,isNaN(parseFloat(n))||!isFinite(n))throw new TypeError(e+" must be a Number");var n;this[e]=Number(t)}}(r[c]);for(var u=0;u<i.length;u++)a.prototype["get"+e(i[u])]=t(i[u]),a.prototype["set"+e(i[u])]=function(e){return function(t){this[e]=String(t)}}(i[u]);return a})?r.apply(t,i):r)||(e.exports=o)}()},,,,,,function(e,t,n){"use strict";function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){o(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){i=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function c(){return Boolean(window[window.GoogleAnalyticsObject||"ga"])}function u(){(0,window[window.GoogleAnalyticsObject||"ga"])((function(e){var t=e.get("sendHitTask");e.set("sendHitTask",(function(e){t(e);var n=function(e){var t=decodeURIComponent(e).split("&").map((function(e){return e.split("=")})).filter((function(e){var t=a(e,1)[0];return["t","ec","ea","el","ev"].indexOf(t)>-1})).reduce((function(e,t){return i(i({},e),{},o({},t[0],t[1]))}),{});if(!t.t)return{};return{event:t.t,category:t.ec||"",action:t.ea||"",label:t.el||"",value:t.ev||""}}(e.get("hitPayload"));"event"===n.event&&h("ga",n)}))}))}function l(e,t){!t.length||t.length<2||"event"!==t[0]||h(e,{action:t[1],category:t[2]&&t[2].event_category||"",label:t[2]&&t[2].event_label||"",value:t[2]&&t[2].value||""})}function h(e,t){if(!function(e){return"detect_user"===e.action&&"Hotjar"===e.category}(t)){var n=function(e){return"GA_"+[e.action,e.category,e.label,e.value].filter((function(e){return 0===e||Boolean(e)})).join("-")}(t),r=function(e){return"GA_"+e.action}(t);hj.log.debug("intercepting ".concat(e,": ")+JSON.stringify(t)),hj("event",n),n!==r&&hj("event",r),hj.event.signal("int.ga",t)}}function d(e,t){return function(n,r){hj.log.debug("[".concat(e,"] ").concat(n),t,r)}}function f(e){return Array.from(e).map((function(e){return JSON.stringify(e)}))}function g(e,t,n,r,i){var o=1;if(void 0!==i&&hj.log.debug("Retry iteration ".concat(o," of ").concat(i)),e())return t(!0);var a=setInterval((function(){return o++,e()?(clearInterval(a),t(!0)):o>=r?(clearInterval(a),t(!1)):void 0}),n)}function p(e){return e&&"string"==typeof e?e.replace(/[\W_]+/g,"-"):""}n.r(t);var v,m,y=d("ga.forward_events","integration"),j={setup:hj.tryCatch((function(e){if(e.tag_sessions){var t=c(),n="function"==typeof window.gtag;g((function(){return void 0!==window.dataLayer&&void 0!==window.dataLayer.length}),(function(e){if(e)return y("Intercepting dataLayer"),void function(){var e=window.dataLayer;if(!0!==e._hj){e.forEach((function(e){l("dataLayer",e)}));var t=e.push;e.push=function(){t.apply(e,arguments);var n=arguments[0];l("dataLayer",n)},e._hj=!0}}();var t;n&&(y("Intercepting gtag"),t=window.gtag,window.gtag=function(){var e=Array.prototype.slice.call(arguments);t.apply(null,e),l("gtag",e)})}),3e3,5),t&&(y("Intercepting ga"),u())}}),"ga.forward_events")},b=d("ga.send_hjuid"),w=60,S=!0,_=hj.tryCatch((function(){hj.store.session.on("user.id",(function(e){"string"!=typeof e||e.length<8?b("invalid userid: '".concat(e,"'")):(b("got userId"),m=e.substring(0,8),O())})),E()}),"integrations.googleAnalytics"),E=hj.tryCatch((function(){if(!v){if(c()){var e=window[window.GoogleAnalyticsObject||"ga"];return b("`ga` variable is available, waiting for tracker."),void e((function(e){C(e,!0)}))}w<=0?b("given up looking for GA module"):(w-=1,setTimeout(E,500))}}),"integrations"),C=hj.tryCatch((function(e,t){e&&(v&&t||(v!==e&&(b("got fresh tracker"),S=!0),v=e,O()))}),"integrations"),O=hj.tryCatch((function(){S&&m&&v&&(S=!1,v.send("event","Hotjar","detect_user",m,{nonInteraction:1}),b("successfully sent detect_user event"))}),"integrations"),I={setup:_,setTracker:hj.tryCatch((function(e){C(e,!1)}),"integrations.googleAnalytics")},N=d("ga.send_hjuid_gtag"),T={setup:hj.tryCatch((function(){var e=window.dataLayer;function t(){N("calling gtag(".concat(f(arguments),")")),e.push(arguments)}void 0!==e?hj.store.session.on("user.id",(function(e){if(e&&"string"==typeof e&&!(e.length<8)){var n=e.substring(0,8);t("set","user_properties",{hjuid:n}),t("event","detect_user",{event_category:"Hotjar",event_label:n,non_interaction:!0})}})):N("`dataLayer` is undefined")}),"ga.send_hjuid_gtag")},k={},R=window.optimizely;function A(e){"applied"===e.name&&(hj.log.debug("Optimizely - campaign decided; ready to tag experiments.","integration"),x())}function x(){var e,t,n,r,i;hj.log.debug("Optimizely - attempting to tag active experiments.","integration"),hj.log.debug("Optimizely - refreshing active experiment variation map.","integration"),k={},R&&"get"in R&&(r=R.get("state"),i=R.get("data"),r.getActiveExperimentIds().forEach((function(o){t=i.experiments[o].name||o,e=r.getVariationMap()[o],n=e.name||e.id,k[t]=n}))),function(){hj.log.debug("Optimizely - looking for tags.","integration");var e=[];for(var t in k)hj.event.signal("exp.opt",{experimentId:t,variantId:k[t]}),e.push(t+"/"+k[t]);return e.length>0?hj.log.debug("Optimizely - found "+e.length+" tags.","integration",e):hj.log.debug("Optimizely - no tags found.","integration"),e}().forEach((function(e){hj("event",e)}))}var P={setup:hj.tryCatch((function(){void 0!==R&&"function"==typeof R.push&&"function"==typeof R.get&&void 0!==R.get("state")&&void 0!==R.get("data")?(hj.log.debug("Optimizely - listening for campaignDecided event.","integration"),(R=window.optimizely||[]).push({type:"addListener",filter:{name:"campaignDecided"},handler:A}),x()):hj.log.debug("`window.optimizely` is not ready","integration")}),"optimizely.setup")},D=n(7),M=function(){return null!==D.a.items.HUBSPOT_UTK.get()},L=d("hubspot","integrations"),U={setup:hj.tryCatch((function(){L("HubSpot setup started"),g(M,(function(e){if(e){var t=D.a.items.HUBSPOT_UTK.get();L("HubSpot UTK found: ".concat(t)),hj.event.signal("int.hubspot",{utk:t})}else L("HubSpot UTK cookie not found")}),5e3,5)}),"hubspot.setup")};var H={setup:hj.tryCatch((function(){void 0!==window.ub&&void 0!==window.ub.page&&void 0!==window.ub.page.variantId?(hj.log.debug("Unbounce experiment in page '".concat(window.ub.page.name,"' is on variant '").concat(window.ub.page.variantId,"'"),"integration",window.ub),hj.event.signal("exp.ub",{experimentId:window.ub.page.name,variantId:window.ub.page.variantId}),hj("event","".concat(p(window.ub.page.name),"-variant-").concat(window.ub.page.variantId))):hj.log.debug("Unbounce experiment not found","integration")}),"unbounce.setup")},q=d("mixpanel.send_events"),V=function(){return void 0!==window.mixpanel&&void 0!==window.mixpanel.set_config},W={setup:hj.tryCatch((function(){g(V,(function(e){e?(q("Registering mixpanel hook"),window.mixpanel.set_config({hooks:{before_send_events:function(e){var t=p(e.event);return q("sending mixpanel payload: ".concat(e.event)),hj("event","MP_".concat(t)),hj.event.signal("int.mp",{event:t}),e}}})):q("mixpanel global object not found or set_config not ready")}),3e3,5)}),"mixpanel.setup")},z=d("ab-tasty","integration");function B(){return void 0!==window.ABTasty&&void 0!==window.ABTasty.hitServiceNotifierSubscribe}var F={setup:hj.tryCatch((function(){function e(e){if("CAMPAIGN"===e.type&&e.data&&e.data.caname&&e.data.vaname){var t=e.data,n=t.caname,r=t.vaname;z("AB Tasty campaign '".concat(n,"' is on variant '").concat(r,"'"),e),hj.event.signal("exp.abt",{experimentId:n,variantId:r},!0);var i=p("".concat(n,"-").concat(r));hj("event",i),z("Sending event '".concat(i,"'"))}else z("Invalid AB Tasty event",e)}g(B,(function(t){t?(z("AB Tasty global object found"),window.ABTasty.hitServiceNotifierSubscribe(e,"CAMPAIGN",{withHitHistory:!0})):z("AB Tasty global object not found")}),2e3,10)}),"abTasty.setup")},G=d("kissmetrics","integration"),Y=function(){return void 0!==window.KM&&void 0!==window.KM.i},K={setup:hj.tryCatch((function(){g(Y,(function(e){if(e){var t=window.KM.i();G("Kissmetrics User ID found: ".concat(t)),hj("identify",null,{kissmetrics_id:t})}}),5e3,5)}),"kissmetrics.setup")};hj.tryCatch((function(){hj.loader.registerModule("IntegrationsModule",(hj.integrations=hj.tryCatch((function(){return{optimizely:P,google_analytics:{forwardEvents:j,sendHotjarUserId:I,sendHotjarUserIdGtag:T},hubspot:U,unbounce:H,mixpanel:W,abTasty:F,kissmetrics:K}}),"integrations")(),{run:hj.tryCatch((function(){var e=hj.settings.integrations;e&&(e.optimizely&&e.optimizely.tag_recordings&&hj.integrations.optimizely.setup(),e.google_analytics&&(hj.integrations.google_analytics.forwardEvents.setup(e.google_analytics),e.google_analytics.send_hotjar_id&&(hj.integrations.google_analytics.sendHotjarUserId.setup(e.google_analytics),hj.integrations.google_analytics.sendHotjarUserIdGtag.setup(e.google_analytics))),e.unbounce&&e.unbounce.tag_recordings&&hj.integrations.unbounce.setup(),e.mixpanel&&e.mixpanel.send_events&&hj.integrations.mixpanel.setup(),e.hubspot&&e.hubspot.enabled&&e.hubspot.send_recordings&&hj.integrations.hubspot.setup(),e.abtasty&&e.abtasty.tag_recordings&&hj.integrations.abTasty.setup(),e.kissmetrics&&e.kissmetrics.send_user_id&&hj.integrations.kissmetrics.setup())}))}),!1)}),"integrations")()},function(e,t,n){"use strict";n.r(t),n.d(t,"eventStream",(function(){return T}));n(120),n(135);var r=n(67),i=Object.freeze({MOUSE_CLICK:r.a.MOUSE_CLICK,MOUSE_MOVE:r.a.MOUSE_MOVE,SCROLL:r.a.SCROLL,VIEWPORT_RESIZE:r.a.VIEWPORT_RESIZE,KEY_PRESS:r.a.KEY_PRESS,SELECT_CHANGE:r.a.SELECT_CHANGE,INPUT_CHOICE_CHANGE:r.a.INPUT_CHOICE_CHANGE,CLIPBOARD:r.a.CLIPBOARD}),o=n(4);function a(e){return"".concat(parseFloat(e/1048576).toFixed(2))}var s=n(7),c=n(27),u=n(22);function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var h=hj.tryCatch((function(e){var t,n,h,d,f,g={},p="",v=void 0,m=null,y=!1,j=o.a.now(),b=!1;g.events=[],g.storePageContent=function(t,n){hj.log.warnIfEmpty(t,"tryStorePageContent: content"),hj.log.warnIfEmpty(n,"tryStorePageContent: urlMD5"),e.storePageContent(t,(function(e){var t=e.content_uuid;t?(y=!0,u.a.set("pageContent",{uuid:t,md5:n})):hj.log.warn("Expecting res.content_uuid but it was not found!")}),(function(e){if(413===e.status){var n=a(t.length);g.write("content_size_too_large",{size:n,source:"page_visit",timestamp:o.a.now()},!1)}}))},g.reportPageContent=function(e){f=u.a.on("pageContent",(function(t){var n=t.uuid,i=t.md5,o=hj.dom.getCSSURLs().map((function(e){return{content_type:2,url:e,url_hash:hj.md5(hj.b64EncodeUnicode(e))}}));hj.log.warnIfEmpty(e,"sendReportContent: pageVisitKey"),hj.log.warnIfEmpty(n,"sendReportContent: uuid"),hj.log.warnIfEmpty(i,"sendReportContent: md5"),hj.log.warnIfEmpty(o,"sendReportContent: webResourceInfos");var a,s,c,u={page_content_url_md5:i,page_content_uuid:n,web_resource_infos:o,content_submitted:y};g.writeNewFrame((a={},s=r.a.REPORT_CONTENT,c=u,s in a?Object.defineProperty(a,s,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[s]=c,a),e).flush(),y=!1}))},g.clearPageContent=function(){f(),u.a.set("pageContent",void 0)},g.setCurrentPageVisitKey=hj.tryCatch((function(e){p!==e&&(p=e,t()),g.flush()}),"data"),g.writeNewFrame=hj.tryCatch((function(e,n){return t(n),g.write(e),t(),g})),g.write=hj.tryCatch((function(e,t,n){var r;if(hj.isPreview)return g;if("object"===l(e))return hj.hq.each(e,(function(e,t){g.write(e,t,!0)})),g;var i=e,a=S(i);if(!a&&b)return g;if(!a&&E())return C(),g;a&&(m=o.a.now()),a&&b&&O();var s=null!==(r=g.events.pop())&&void 0!==r?r:w(p);return n?s[i]=t:(s[i]=s[i]||[],s[i].push(t)),g.events.push(s),g}),"data"),g.flush=hj.tryCatch((function(){var n=c.a.get("user.id");if(v&&clearInterval(v),!n)return t();var r=_(),i=r.length;if(i>0)for(var o=0;o<i;o++){var a=r[o];e.send(a,n)}v=setInterval(g.flush,1e3)}),"data");var w=function(e){return{pageVisitKey:e}},S=function(e){return hj.hq.inArray(e,hj.hq.values(i))};t=hj.tryCatch((function(e){var t={pageVisitKey:e||p};g.events.push(t)}),"data");var _=hj.tryCatch((function(){for(var e,r=g.events.length,i=[],o=[],a=[],s=0;s<r;s++)if(e=g.events[s],Object.keys(e).length>1){var c=[];e.autotag_recording&&(c=e.autotag_recording.filter((function(e){return"rageclick"===e.name}))),c.length>0?o.push(e):(e.clipboard&&i.push(e.clipboard),a.push(n(e)))}var u=d(o,i);return g.events=u.leftover,t(),[].concat(a,u.sendable)}),"data");d=hj.tryCatch((function(e,t){var r=[],i=[];return hj.hq.each(t,(function(t,n){e=e.filter((function(e){if("copy"===n.action||"cut"===n.action)return n.time-e.autotag_recording[0].time>5e3}))})),hj.hq.each(e,(function(e,t){hj.time.getNow()-t.autotag_recording[0].time<5e3?r.push(t):i.push(n(t))})),{leftover:r,sendable:i}}),"data.filterRageClicks"),n=hj.tryCatch((function(e){return hj.hq.each(e,(function(t,n){hj.hq.isFunction(n)&&(e[t]=n())})),e.page_visit_key=e.pageVisitKey,delete e.pageVisitKey,e.viewport_id=hj.viewport.getId(),"object"===l(e.mutation)&&(e.mutation=h(e.mutation)),e}),"data"),h=hj.tryCatch((function(e){var t,n="";if("object"===l(e))return hj.hq.each(e,(function(r,i){"object"===l(i.c)&&(hj.hq.each(i.c,(function(i,o){"object"===l(o.attributes)&&"string"==typeof o.attributes.style&&(o.attributes.style===t&&o.id===n&&(e[r].c[i]=null),t=o.attributes.style,n=o.id)})),e[r].c=e[r].c.filter((function(e){return e})),e[r].c.length||delete e[r].c),void 0===e[r].a&&void 0===e[r].b&&void 0===e[r].c&&void 0===e[r].d&&(e[r]=null)})),e.filter((function(e){return e}))}),"data");var E=function(){var e,t=null!==(e=m)&&void 0!==e?e:j;return o.a.now()-t>18e5},C=function(){sessionStorage.removeItem("_hjRecordingEnabled"),sessionStorage.removeItem("_hjRecordingLastActivity"),b=!0},O=function(){b=!1,s.a.items.SESSION_RESUMED.set(!0),hj._init.reinit(window.location.href,!0)};return g})),d=n(130),f=(n(149),n(16)),g=n(48),p=n(3),v=n(92),m={maxRetries:5,delay:3e3,firstAttemptDelay:0,baseExponent:2};function y(e){var t=this;if(!e)throw new Error("HotjarWebSocket requires a flush callback");this._connected=!1,this._connecting=!1,this._isReconnecting=!1,this._closedPermanently=!1,this.sessionTimedOutDueToInactivity=!1,this._finished=!1,this._pingInterval=3e4,this._pingIntervalId=void 0,this._sessionInactiveInterval=18e5,this._lastUserActivityTime=(new Date).getTime(),this._unloadTimeoutStarted=!1,this._ws=null,this._wsUrl="",this._flush=e,this._msgPrefix="{}\n",this._clearPings=function(){clearInterval(t._pingIntervalId)},this._closeConnection=function(){t._connected=!1,t._connecting=!1,t._isReconnecting=!1,t._closedPermanently=!0}}y.prototype.connect=hj.tryCatch((function(){if(this._sessionIsDisabled())return!1;if(!this._connected&&!this._connecting&&!this._isReconnecting){this._connecting=!0;var e=this;c.a.on("user.id",(function(){var t=_hjSettings.wsMockUrl||"wss://".concat(Object(f.c)(),"/api/v2/client/ws");e._wsUrl="".concat(t,"?v=").concat(p.c);try{e._createAndConnect()}catch(e){hj.log.debug("The Web Socket connection was refused. \n ".concat(e))}}))}return!0}),"data.HotjarWebSocket.connect"),y.prototype.updateLastUserActivityTime=hj.tryCatch((function(){this._lastUserActivityTime=(new Date).getTime()}),"data.HotjarWebSocket.updateLastUserActivityTime"),y.prototype.disconnect=hj.tryCatch((function(){this._connected&&(hj.log.debug("Disconnecting Web Socket.","websocket"),this._flush(),this.close())}),"data.HotjarWebSocket.disconnect"),y.prototype.isConnected=hj.tryCatch((function(){return!!this._connected&&((new Date).getTime()-this._lastUserActivityTime<=this._sessionInactiveInterval||(sessionStorage.removeItem("_hjRecordingEnabled"),sessionStorage.removeItem("_hjRecordingLastActivity"),this.close(),this.sessionTimedOutDueToInactivity=!0,!1))}),"data.HotjarWebSocket.isConnected"),y.prototype.send=hj.tryCatch((function(e){this._sessionIsDisabled()||(hj.log.debug("Sending data to Web Socket","websocket",e),this._ws.send(e))}),"data.HotjarWebSocket.send"),y.prototype.close=hj.tryCatch((function(){hj.log.debug("Closing Web Socket.","websocket"),this._connected=!1,this._connecting=!1,this._finished=!0,this._ws.close()}),"data.HotjarWebSocket.close"),y.prototype.getBufferedAmount=hj.tryCatch((function(){return this._ws.bufferedAmount}),"data.HotjarWebSocket.getBufferedAmount"),y.prototype._sessionIsDisabled=hj.tryCatch((function(){return this._closedPermanently||this.sessionTimedOutDueToInactivity||"1"===s.a.items.SESSION_TOO_LARGE.get()}),"data.HotjarWebSocket._sessionIsDisabled"),y.prototype._ping=hj.tryCatch((function(){this._connected&&(hj.log.debug("Pinging Web Socket","websocket"),this._ws.send(this._msgPrefix+"ping"))}),"data.HotjarWebSocket._ping"),y.prototype._createAndConnect=hj.tryCatch((function(){var e=this,t=this;if(t._finished)hj.log.debug("Unload event triggered, don't reconnect"),!1===t._unloadTimeoutStarted&&(t._unloadTimeoutStarted=!0,setTimeout((function(){t._finished=!1,t._unloadTimeoutStarted=!1}),1e3));else{hj.log.debug("Connecting to Web Socket ["+this._wsUrl+"]","websocket");var n=function(t,n){var i=hj.metrics.time();Object(g.a)((function(){return r(t,i,n)}),(function(){return e._closeConnection()}),m)},r=function(r,i){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{o=new WebSocket(t._wsUrl)}catch(e){return void hj.log.debug("Web socket connection ".concat(a?"retry":""," error. \n ").concat(e))}return o.onopen=function(e){if(r(o),t._onOpen.call(t,e),a){v.a.set("eventsRetrySuccess",!0),0===o.bufferedAmount&&t._ping.call(t);var n=a.reason,s=a.extraTags;hj.metrics.timeEnd("time-to-reconnect",{tag:{reason:n},start:i,extraTags:s})}},o.onmessage=function(e){t._onMessage.call(t,e)},o.onclose=function(t){e._clearPings(),e._connected=!1,hj.log.debug("Web Socket closed.","websocket");var i={message:t.reason,code:t.code,wasClean:t.wasClean};if(hj.metrics.count("session-interruption",{tag:{reason:"websocket-close"},extraTags:i}),t.wasClean&&1012!=t.code)4e3==t.code&&console.warn("Hotjar Tracking Code is out of date and this session will not be captured anymore. Please reload the page to update your Tracking Code."),e._closeConnection();else if(hj.log.warn("Websocket close was unclean: "+t.code),!e._isReconnecting){var o={reason:t.wasClean?"service-restart":"unclean-close",extraTags:i};e._isReconnecting=!0,e._connecting=!0,n(r,o)}},o.onerror=function(e){hj.log.debug("onError was called.","websocket",e),v.a.set("eventsRetrySuccess",!1),this._connected&&(this._clearPings(),this._connected=!1)},o};t._ws=r((function(e){t._ws=e})),window.addEventListener("beforeunload",this.disconnect,!1)}}),"data.HotjarWebSocket._createAndConnect"),y.prototype._onOpen=hj.tryCatch((function(){hj.log.debug("Web Socket opened.","websocket"),this._pingIntervalId=setInterval(this._ping.bind(this),this._pingInterval),this._connected=!0,this._connecting=!1,this._isReconnecting=!1,this._flush()}),"data.HotjarWebSocket._onOpen"),y.prototype._onMessage=hj.tryCatch((function(e){var t;try{t=JSON.parse(e.data)}catch(t){throw hj.log.warn("Could not parse websocket message: "+e.data),t}switch(t.type){case"SESSION_TOO_LARGE":hj.log.warn("Session became too large. Will stop sending websocket data."),s.a.items.SESSION_TOO_LARGE.set("1"),this.disconnect();break;default:hj.log.warn("Received unknown websocket message: "+e.data)}}),"data.HotjarWebSocket._onMessage");var j=function(e,t){var n=function(e){try{e&&hj.metrics.count("session-exception",{tag:{module:"compression"},extraTags:{message:e.message,name:e.name}}),t({success:!1})}catch(e){}};try{if(!function(){try{var e=new Set(hj.settings.features);return"CompressionStream"in window&&e.has("client_script.compression.pc")}catch(e){return!1}}())return n();(function(e){var t=e.getReader(),n=[],r=hj.metrics.timeWatcher();return t.read().then((function e(i){var o=i.done,a=i.value;if(r.start(),o){var s=n.reduce((function(e,t){return e+t.length}),0),c=new Uint8Array(s),u=0;return n.forEach((function(e){c.set(e,u),u+=e.length})),{time:r.end(),compressed:c}}return n.push(a),r.incr(),t.read().then(e)}))})(new ReadableStream({start:function(t){t.enqueue(e),t.close()}}).pipeThrough(new TextEncoderStream).pipeThrough(new CompressionStream("gzip"))).then((function(e){var n=e.time,r=e.compressed;t({success:!0,compressed:r,time:n})})).catch(n)}catch(e){n(e)}};function b(e){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var w=hj.tryCatch((function(e){var t,n,l,h,f,g,p="",v=void 0,m=!1,w=Object.create({events:[],storePageContent:function(t,n,r){hj.log.warnIfEmpty(t,"tryStorePageContent: content"),hj.log.warnIfEmpty(n,"tryStorePageContent: urlMD5");var i=t.length,s=function(r){var s=r.success,c=r.compressed,l=r.time;s&&l&&hj.metrics.timeEnd("pc-compression-time",{tag:{task:"process-chunks"},total:l}),e.storePageContent(s?c:t,(function(e,r){if(207===(null==r?void 0:r.status)&&"gzip error"===e[1])return hj.metrics.distr("compression-malformed",{task:"gzip",value:i,extraTags:{compressedLength:c.length}}),w.storePageContent(t,n,!0);var o=e.content_uuid;o?(m=!0,u.a.set("pageContent",{uuid:o,md5:n}),s&&i&&hj.metrics.distr("compression-ratio",{task:"page-content",value:parseFloat(Number(i/c.length).toFixed(1))})):hj.log.warn("Expecting res.content_uuid but it was not found!")}),(function(e){if(413===e.status){var t=a(i);w.write("content_size_too_large",{size:t,source:"page_visit",timestamp:o.a.now()},!1)}}),{query:s?"gzip=1":void 0})};r?s({success:!1}):j(t,s)},reportPageContent:function(e){m=!1,g=u.a.on("pageContent",(function(t){var n=t.uuid,i=t.md5,o=hj.dom.getCSSURLs().map((function(e){return{content_type:2,url:e,url_hash:hj.md5(hj.b64EncodeUnicode(e))}}));hj.log.warnIfEmpty(e,"sendReportContent: pageVisitKey"),hj.log.warnIfEmpty(n,"sendReportContent: uuid"),hj.log.warnIfEmpty(i,"sendReportContent: md5"),hj.log.warnIfEmpty(o,"sendReportContent: webResourceInfos");var a,s,c,u={page_content_url_md5:i,page_content_uuid:n,web_resource_infos:o,content_submitted:m};w.writeNewFrame((a={},s=r.a.REPORT_CONTENT,c=u,s in a?Object.defineProperty(a,s,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[s]=c,a),e).flush()}))},clearPageContent:function(){g(),u.a.set("pageContent",void 0)},setCurrentPageVisitKey:hj.tryCatch((function(e){p!==e&&(p=e,t()),w.flush()}),"data"),writeNewFrame:hj.tryCatch((function(e,n){return t(n),w.write(e),t(),w})),write:hj.tryCatch((function(e,t,n,r){var i;if(hj.isPreview)return w;if("object"===b(e))return hj.hq.each(e,(function(e,t){w.write(e,t,!0,r)})),w;var o=e;if(!r&&!w._ws.connect()){if(!w._ws.sessionTimedOutDueToInactivity||!_(o))return w;s.a.items.SESSION_RESUMED.set(!0),w._ws=new y(w.flush),hj._init.reinit(window.location.href,!0),w._ws.connect()}var a=null!==(i=w.events.pop())&&void 0!==i?i:S(p);return n?a[o]=t:(a[o]=a[o]||[],a[o].push(t)),w.events.push(a),w}),"data"),queueEndSignal:hj.tryCatch((function(){hj.log.debug("Should send end signal to Web Socket with the next flush","websocket"),w.shouldSendEndSignal=!0}),"data"),sendEndSignal:hj.tryCatch((function(){hj.log.debug("Sending end signal to Web Socket","websocket"),w._ws.send(w._ws._msgPrefix+"end_signal"),w._ws.close(),s.a.items.HJ_SESSION.clear(),w.shouldSendEndSignal=!1}),"data"),flush:hj.tryCatch((function(){var e,r,i=c.a.get("user.id");if(v&&clearInterval(v),i||(c.a.on("user.id",w.flush),t()),w._ws.isConnected()&&i){if((r=(e=n()).length)>0){var s,u={sid:Object(d.a)(),uuid:i,viewportid:null===(s=hj.viewport)||void 0===s?void 0:s.getId(),site_id:hjSiteSettings.site_id,timestamp:o.a.now(),beta:Object(d.b)()};w._ws._msgPrefix=hj.hq.stringify(u)+"\n",E(e)&&w._ws.updateLastUserActivityTime();for(var l=0;l<r;l++){var h=e[l],f=hj.hq.stringify(h);if(f.length<10485760)w._ws.send(w._ws._msgPrefix+f);else{var g=hj.hq.stringify({content_size_too_large:{size:a(f.length),source:"flush",timestamp:o.a.now()},page_visit_key:h.page_visit_key,viewport_id:h.viewport_id});w._ws.send(w._ws._msgPrefix+g)}}}if(w.shouldSendEndSignal)return w.sendEndSignal();v=setInterval(w.flush,1e3)}}),"data"),close:hj.tryCatch((function(){w._ws.disconnect()}),"data"),_ws:void 0});w._ws=new y(w.flush);var S=function(e){return{pageVisitKey:e}},_=function(e){return hj.hq.inArray(e,hj.hq.values(i))},E=function(e){return e.some((function(e){return Object.keys(e).some(_)}))};return t=hj.tryCatch((function(e){var t={pageVisitKey:e||p};w.events.push(t)}),"data"),n=hj.tryCatch((function(){for(var e,n=w.events.length,r=[],i=[],o=[],a=0;a<n;a++)if(e=w.events[a],Object.keys(e).length>1){var s=[];e.autotag_recording&&(s=e.autotag_recording.filter((function(e){return"rageclick"===e.name}))),s.length>0?i.push(e):(e.clipboard&&r.push(e.clipboard),o.push(l(e)))}var c=f(i,r);return w.events=c.leftover,t(),[].concat(o,c.sendable)}),"data"),f=hj.tryCatch((function(e,t){var n=[],r=[];return hj.hq.each(t,(function(t,n){e=e.filter((function(e){if("copy"===n.action||"cut"===n.action)return n.time-e.autotag_recording[0].time>5e3}))})),hj.hq.each(e,(function(e,t){hj.time.getNow()-t.autotag_recording[0].time<5e3?n.push(t):r.push(l(t))})),{leftover:n,sendable:r}}),"data.filterRageClicks"),l=hj.tryCatch((function(e){var t;return hj.hq.each(e,(function(t,n){hj.hq.isFunction(n)&&(e[t]=n())})),e.page_visit_key=e.pageVisitKey,delete e.pageVisitKey,e.viewport_id=null===(t=hj.viewport)||void 0===t?void 0:t.getId(),"object"===b(e.mutation)&&(e.mutation=h(e.mutation)),e}),"data"),h=hj.tryCatch((function(e){var t,n="";if("object"===b(e))return hj.hq.each(e,(function(r,i){"object"===b(i.c)&&(hj.hq.each(i.c,(function(i,o){"object"===b(o.attributes)&&"string"==typeof o.attributes.style&&(o.attributes.style===t&&o.id===n&&(e[r].c[i]=null),t=o.attributes.style,n=o.id)})),e[r].c=e[r].c.filter((function(e){return e})),e[r].c.length||delete e[r].c),void 0===e[r].a&&void 0===e[r].b&&void 0===e[r].c&&void 0===e[r].d&&(e[r]=null)})),e.filter((function(e){return e}))}),"data"),w})),S=n(129);function _(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var E=Object(S.Resource)("/",{post:["POST"]},(function(){return _hjSettings.contentHost||(_hjSettings.contentHost="content.hotjar.io"),_hjSettings.contentHost})),C=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.pageContentResource=E}var t,n,r;return t=e,(n=[{key:"storePageContent",value:function(e,t,n,r){return this.pageContentResource.post(e,t,n,{contentType:"text",query:null==r?void 0:r.query})}},{key:"send",value:function(){}}])&&_(t.prototype,n),r&&_(t,r),e}(),O=n(106);function I(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function N(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var T,k={maxRetries:5,delay:4e3},R=Object(S.Resource)("/",{post:["POST"]},(function(){return _hjSettings.contentHost||(_hjSettings.contentHost="content.hotjar.io"),_hjSettings.contentHost})),A=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;I(this,e),this.viewport=t,this.siteId=n,this.maxRetries=r}var t,n,r;return t=e,(n=[{key:"storePageContent",value:function(e,t,n){return R.post(e,t,n,{contentType:"text"})}},{key:"send",value:function(e,t){var n=(_hjSettings.eventsHost||(_hjSettings.eventsHost="https://events.hotjar.io"),_hjSettings.eventsHost+"/"),r={uuid:t,viewportid:this.viewport.getId(),site_id:this.siteId},i=hj.hq.stringify(r)+"\n"+hj.hq.stringify(e),o=!1,a=function e(){return Object(O.post)(n,i,(function(e){hj.log.debug("Events sent successfully to host","events",e),o=!1,v.a.set("eventsRetrySuccess",!0)}),(function(){hj.log.debug("Events failed to send to host, retrying","events"),v.a.set("eventsRetrySuccess",!1),o||(o=!0,Object(g.a)(e,null,k))}),{contentType:"text"})};a()}}])&&N(t.prototype,n),r&&N(t,r),e}();T=hj.features.hasFeature("ingestion.http")?h(new A(hj.viewport,hj.settings.site_id)):w(new C),hj.eventStream=T},function(e,t,n){"use strict";n.r(t);var r=["button","reset","submit"],i=["password","email","hidden"],o=["default-style","content-type","refresh"],a=["style","script"],s=["address","address1","address2","addressline1","addressline2","cell","cellphone","dateofbirth","dob","email","familyname","firstname","fullname","lastname","mobile","name","phone","postalcode","postcode","surname","tel","telephone","username","zip","zipcode","nationalinsurancenumber","nin","ppsn","security","securitynum","socialsec","socialsecuritynumber","socsec","ssn","adgangskode","authpw","contrasena","contrasenya","contraseña","contrasinal","cyfrinair","fjalëkalim","focalfaire","geslo","hasło","heslo","jelszó","kennwort","kωδικός","kωδικόςπρόσβασης","lozinka","lykilorð","lösenord","motdepasse","parakalw","parola","paroladordine","parole","parool","pasahitza","pass","passord","password","passwort","pw","pwd","pword","pwrd","salasana","sapwd","senha","sifre","slaptažodis","userpw","userpwd","wachtwoord","лозинка","парола","пароль","פּאַראָל","كلمهالسر","पासवर्ड","パスワード","密码","密碼","암호","cc","cccsc","cccvc","cccvv","ccexp","ccexpiry","ccexpmonth","ccexpyear","ccname","ccnum","ccnumber","cctype","creditcard","csc","cvc","cvv","exp","accountnum","accountnumber","bic","iban","ibanaccountnum","ibanaccountnumber","pin","pinno","pinnum","secq","secret","secretq","secretquestion","securityq","securityquestion","sortcode","swift"],c=n(137),u=/\d+/,l=new RegExp(u.source,"g"),h=/[^@\s]+@[^@\s]+\.[^@\s]+/,d=new RegExp(h.source,"g"),f=/([+(]{0,2}\d[-_ ()/]{0,4}){9,}/,g=new RegExp(f.source,"g"),p=function(){return Math.random()<.5?-1:1},v=hj.tryCatch((function(e){return e&&e.parentNode?"#document-fragment"===e.parentNode.nodeName?e.parentNode.host:e.parentNode:null}),"hj.privacy.getParentNode"),m=hj.tryCatch((function(e){var t=Object.prototype.toString.call(e);return-1!==["[object HTMLDocument]","[object Document]"].indexOf(t)}),"hj.privacy._isDocument"),y=hj.tryCatch((function(e){return f.test(e)}),"hj.privacy._hasCCNumOrSSN"),j=hj.tryCatch((function(e){return e.indexOf("@")>-1&&h.test(e)}),"hj.privacy._hasEmail"),b=hj.tryCatch((function(e){return u.test(e)}),"hj.privacy._hasDigitSequence"),w=/(?:\d{1,3}\.){3}\d{1,3}/,S=/(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}/,_=hj.tryCatch((function(e){return w.test(e)||S.test(e)}),"hj.privacy._hasIPAddress"),E=hj.tryCatch((function(e){return e.value||e.textContent}),"hj.privacy._getTextFromElement"),C=hj.tryCatch((function(e){if(e&&"string"==typeof e)return Boolean(e.match(/^\s*data:(image\/[a-z]+|application\/octet-stream);base64,([A-Za-z0-9+/=])+\s*$/))}),"hj.privacy._isBase64Value"),O=/\S+/g,I=/\s?background[^;]+;?\s?/g,N=hj.tryCatch((function(e,t){return new Array((e||16)+1).join(t||"*")}),"Suppresser.createReplacementStr"),T=hj.tryCatch((function(e){var t={};return e.style&&e.style.width||(t.width=e.offsetWidth+"px"),e.style&&e.style.height||(t.height=e.offsetHeight+"px"),t}),"Suppresser.getSuppressedImageSize"),k=hj.tryCatch((function(e){return e&&e.length?e.replace(O,(function(e){return N(Math.max(1,e.length+p()))})):N(16+p())}),"Suppresser.textContentHandler"),R=hj.tryCatch((function(e){var t=e;return t&&t.length&&(hj.settings.anonymize_emails&&(t=t.replace(d,k)),hj.settings.anonymize_digits&&t&&t.length&&(t=t.replace(l,k)),t=t.replace(g,(function(e){return e.replace(l,k)}))),t}),"Suppresser.textContentHandler"),A=hj.tryCatch((function(e){var t=e?16:10;return new Date(2839968e5).toISOString().substring(0,t)}),"Suppresser.getLocalDateStr"),x=hj.tryCatch((function(e){if(e)return e.replace(I,"")}),"Suppresser.imageStyleHandler"),P={text:k,full:k,partial:R,textContent:R,imgStyle:x,password:function(){return N()},number:function(){return N(16,"1")},date:function(){return A(!1)},datetime:function(){return A(!0)},"datetime-local":function(){return A(!0)},time:function(){return"00:00"},month:function(){return"1979-01"},week:function(){return"1979-W1"}},D={getSuppressedText:function(e,t){var n=P[e];return n?n(t):k(t)},getSuppressedImageNode:function(e){var t={src:"",meta:{style:T(e)}},n=x(e.getAttribute("style"));return n&&(t.style=n),t},textHandler:k};function M(e){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var L=hj.tryCatch((function(e){var t=e.tagName.toLowerCase(),n=e.type.toLowerCase();return"input"===t&&i.indexOf(n)>-1}),"hj.privacy._isSupressedInputType"),U=hj.tryCatch((function(e){var t=e.tagName.toLowerCase(),n=e.type.toLowerCase();return"input"===t&&r.indexOf(n)>-1}),"hj.privacy._isAllowlistedInputType"),H=hj.tryCatch((function(e){if(e&&e.tagName){var t=e.tagName.toLowerCase();return a.indexOf(t)>-1}}),"hj.privacy._isAllowlistedElement"),q=hj.tryCatch((function(e){var t=e&&e["http-equiv"]&&e["http-equiv"].value;return!t||o.some((function(e){return!!t.match(e)}))}),"hj.privacy._isValidHttpEquiv"),V=hj.tryCatch((function(e){return[e.name,e.id].map((function(e){return e.replace(/[\s_-]+/g,"").toLocaleLowerCase()})).some((function(e){return s.indexOf(e)>-1}))}),"hj.privacy._hasSuppressedNameOrId"),W=hj.tryCatch((function(e){return"object"===M(e.attributes)&&(void 0!==e.attributes["data-hj-suppress"]||void 0!==e.attributes["data-hj-masked"])||"string"==typeof e.className&&e.className&&e.className.indexOf("data-hj-suppress")>-1}),"hj.privacy._isExplicitlySuppressed"),z=hj.tryCatch((function(e){for(;e&&!m(e);){if(W(e))return!0;e=v(e)}return!1}),"hj.privacy._isSelfOrAncestorSuppressed"),B=hj.tryCatch((function(e){return hj.settings.recording_capture_keystrokes&&(e.attributes&&void 0!==e.attributes["data-hj-whitelist"]||e.className&&e.className.indexOf("data-hj-whitelist")>-1||e.attributes&&void 0!==e.attributes["data-hj-allow"]||e.className&&e.className.indexOf("data-hj-allow")>-1)}),"hj.privacy._isUserAllowlisted"),F=[L,V,function(e){return y(E(e))},function(e){return j(E(e))}],G=hj.tryCatch((function(e){return F.some((function(t){return t(e)}))}),"hj.privacy._shouldSuppressInputOrTextarea"),Y=[function(e){return!(!e||!hj.settings.anonymize_digits)&&b(e)},function(e){return!(!e||hj.settings.anonymize_digits)&&y(e)},function(e){return!(!e||!hj.settings.anonymize_emails)&&("string"==typeof e&&j(e))}],K=hj.tryCatch((function(e,t){return(!t||!H(t))&&Y.some((function(t){return t(e)}))}),"hj.privacy._shouldSuppressTextContent"),X={isRiskyNotAllowlistedOrSuppressedElement:hj.tryCatch((function(e,t){if(void 0===e||!e||void 0===e.tagName)return!1;if(c.a.get()||hj.settings.suppress_all)return!0;if(hj.settings.suppress_text)return"IMG"!==e.tagName||!t||"src"!==t.name&&"style"!==t.name&&"srcset"!==t.name||z(e);var n="TEXTAREA"===e.tagName||"INPUT"===e.tagName&&!U(e);return n&&B(e)?G(e):n||z(e)}),"hj.privacy.isRiskyNotAllowlistedOrSuppressedElement"),isAttributeSuppressable:hj.tryCatch((function(e,t,n,r){var i={INPUT:{attrs:["value","placeholder"]},TEXTAREA:{attrs:["value","placeholder"]},A:{attrs:["href"],shouldSuppressAttrValueCheck:function(e){return!(e&&!!e.match(/^data:/))}},OPTION:{attrs:["label","value"]},PROGRESS:{attrs:["value"]},OPTGROUP:{attrs:["label"]},IMG:{attrs:["alt"]},DIV:{attrs:["title"]},SPAN:{attrs:["title"]},P:{attrs:["title"]},META:function(e,t,n){switch(e){case"content":return(!n||!n.name||"viewport"!==n.name.value)&&!(n&&n["http-equiv"]&&q(n));case"name":return"viewport"!==t;case"http-equiv":return!q(n);case"charset":return!1;default:return!0}}}[e];return void 0!==i&&("function"==typeof i?i(t,n,r):!(i.attrs.indexOf(t)<0)&&(void 0===i.shouldSuppressAttrValueCheck||i.shouldSuppressAttrValueCheck(n)))}),"hj.privacy.isAttributeSuppressable"),hasPotentialPIIData:hj.tryCatch((function(e,t){var n=!t||m(t)?null:t;return K(e,n)}),"hj.privacy.hasPotentialPIIData"),getSuppressedText:hj.tryCatch((function(e,t){return D.getSuppressedText(e,t)}),"hj.privacy.getSuppressedText"),getSuppressedTextNode:hj.tryCatch((function(e,t){var n=e.parentNode||null,r=t||X.isRiskyNotAllowlistedOrSuppressedElement(n),i=r||n&&"textarea"===n.type?"full":"partial";return{content:(r=!H(n)&&!!e.textContent&&!C(e.textContent)&&(r||X.hasPotentialPIIData(e.textContent,n)))?D.getSuppressedText(i,e.textContent):e.textContent,shouldSuppressNode:Boolean(r)}}),"hj.privacy.getSuppressedTextNode"),getSuppressedNodeAttribute:hj.tryCatch((function(e,t,n){var r=t.value,i=t.name,o=n;if("data-hj-suppressed"!==i)return"IMG"!==e.tagName||"src"!==i&&"srcset"!==i&&"style"!==i||(o=X.isRiskyNotAllowlistedOrSuppressedElement(e,t),X.isRiskyNotAllowlistedOrSuppressedElement(e,t)&&(i="data-hj-suppressed",r=D.getSuppressedImageNode(e))),!C(r)&&X.isAttributeSuppressable(e.tagName,i,r,e.attributes)&&((o=o||X.isRiskyNotAllowlistedOrSuppressedElement(e))||"META"===e.tagName?r=D.getSuppressedText("full",r):X.hasPotentialPIIData(r)&&(r=D.getSuppressedText("partial",r))),{name:i,value:r,shouldSuppressNode:o}}),"hj.privacy.getSuppressedTextNode"),getSuppressedNode:hj.tryCatch((function(e,t){for(var n={},r=t,i=0;i<e.attributes.length;i++){var o=e.attributes[i],a=X.getSuppressedNodeAttribute(e,o,t);a&&(n[a.name]=a.value,r=r||a.shouldSuppressNode)}return{node:{tagName:e.tagName,attributes:n||{}},shouldSuppressNode:Boolean(r)}}),"hj.privacy.getSuppressedNode"),getTagsWithoutPII:hj.tryCatch((function(e){return e?e.filter((function(e){return!!e&&(!function(e){var t="string"==typeof e;return!!(y(e)||t&&j(e)||_(e))}(e)||(hj.log.debug("Tag "+e+" has been removed due to possible PII information included"),!1))})):[]}),"hj.privacy.getTagsWithoutPII"),suppressErrorMessage:hj.tryCatch((function(e){var t=e;return hj.settings.anonymize_emails&&(t=t.replace(d,D.textHandler)),t=t.replace(g,D.textHandler)}),"hj.privacy.suppressErrorMessage")};hj.privacy=X},function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}n.r(t);var i=function(e,t,n){[["src","https://"+(n||hj.insightsHost)+"/static/app/img/transparent.png"],["style",function(e,t){var n="https://"+(t||hj.insightsHost)+"/static/app/img/suppressed.png",r=(e.meta||{}).style||{},i=r.width,o=r.height;return[e.style,'background: url("'+n+'") repeat !important',i?"width: "+i:"",o?"height: "+o:""].filter((function(e){return e}))}(t,n).join(";")]].forEach((function(t){e.setAttribute(t[0],t[1])}))},o=n(16),a=n(4);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}hj.tryCatch((function(){var e=hj.tryCatch((function(){function e(e,t){this.root=e,this.delegate=t,this.idMap={}}return e.prototype.initialize=function(e,t){this.idMap[e]=this.root;for(var n=0;n<t.length;n++)this.deserializeNode(t[n],this.root)},e.prototype.deserializeDocument=function(e,t,n){this.root=document.cloneNode(),n&&(this.idMap={}),this.idMap[e]=this.root;for(var r=0;r<t.length;r++)this.deserializeNode(t[r],this.root,n);return this.root},e.prototype.deserializeNode=function(e,t,n){var r=this,i=!1;if(null===e)return null;var o=this.idMap[e.id];if(o&&!n)return o;var a=this.root.ownerDocument;switch(null===a&&(a=this.root),e.nodeType){case Node.COMMENT_NODE:o=a.createComment(e.textContent);break;case Node.TEXT_NODE:o=a.createTextNode(e.textContent);break;case Node.DOCUMENT_TYPE_NODE:o=a.implementation.createDocumentType(e.name,e.publicId,e.systemId);break;case Node.ELEMENT_NODE:try{this.delegate&&this.delegate.createElement&&(o=this.delegate.createElement(e.tagName)),o||(o=a.createElement(e.tagName))}catch(e){o=a.createComment('hj.treeMirror.deserializeNode.error: "'+e.message+'"'),i=!0;break}Object.keys(e.attributes).forEach((function(t){try{r.delegate&&r.delegate.setAttribute&&r.delegate.setAttribute(o,t,e.attributes[t])||o.setAttribute(t,e.attributes[t])}catch(e){}}))}if(!o)throw"Could not create node of type: "+e.nodeType;if(this.idMap[e.id]=o,t&&t.appendChild(o),e.childNodes&&!i)for(var s=0;s<e.childNodes.length;s++)this.deserializeNode(e.childNodes[s],o,n);return o},e}),"TreeMirrorMirror")(),t=hj.tryCatch((function(){function e(e,t,n){var r=this;this.target=e,this.mirror=t,this.nextId=1,this.redactedContentId=-100,this.mutationSummaries=[],this.pendingMutations=[],hj.treeMirror.mutationObserverAvailable?(this.knownNodes=new hj.MutationSummary.NodeMap,this.shadowRoots=new hj.MutationSummary.NodeMap):(this.knownNodes={get:function(){},set:function(){},deleteNode:function(){}},this.shadowRoots={get:function(){},set:function(){},deleteNode:function(){},getValues:function(){}});var i,o=hj.metrics.time();this.serializeTarget({target:e,onSerialization:function(e,t,i){n&&n(e,t,i),r.hookAttachShadow(),hj.metrics.timeEnd("task-execution-time",{tag:{task:"dom-serialization"},start:o}),hj.metrics.timeIncr("task-execution-time",{tag:{task:"node-suppression"},flush:!0})}});try{void 0!==window.MutationObserver?i=window.MutationObserver:void 0!==window.WebKitMutationObserver?i=window.WebKitMutationObserver:void 0!==window.MozMutationObserver&&(i=window.MozMutationObserver)}catch(e){i=void 0}if(void 0!==i){this.listenToMutations(e);var a=this;this.shadowRoots.getValues().forEach((function(e){a.listenToMutations(e),a.propagateNonComposedEvents(e)}))}}return e.prototype.hookAttachShadow=function(){var e=this,t=Element.prototype.attachShadow;Element.prototype.attachShadow=function(){var n=t.apply(this,arguments);return e.shadowRoots.set(this,n),e.listenToMutations(n),e.propagateNonComposedEvents(n),n}},e.prototype.propagateNonComposedEvents=function(e){["change","copy","cut","paste","scroll"].forEach((function(t){e.addEventListener(t,(function(e){e.composed||hj.event.signal("shadow-event:".concat(e.type),e)}),!0)}))},e.prototype.listenToMutations=function(e){var t=this;this.mutationSummaries.push(new hj.MutationSummary({rootNode:e,callback:hj.tryCatch((function(e){t.applyChanged(e)}),"hj.treeMirrorClient"),queries:[{all:!0}],observeOwnChanges:!0}))},e.prototype.serializeTarget=function(e){var t=this,n=e.target,r=e.onDemand,i=e.onSerialization,o=this.serializeNode(n).id,s=function(){try{return window.self!==window.top}catch(e){return!0}}();if(hj.features.hasFeature("client_script.serialization.async")){return function(e){var t=e.target,n=e.task,r=e.onFinish,i=e.config,o=void 0===i?{maxIterations:5,maxTaskTime:250}:i,s=[],c=0,u=a.a.now(),l=t.firstChild,h=[];!function e(){if(l){s.push(n(l)),l=l.nextSibling;var t=a.a.now()-u;t>=o.maxTaskTime&&c<o.maxIterations?(h.push(t),c++,setTimeout((function(){return u=a.a.now(),e()}),0)):e()}else h.push(a.a.now()-u),r(s,{maxTimes:h,iterations:c}),hj.metrics.timeEnd("task-execution-time",{tag:{task:"dom-serialization-async"},total:(i=h,Math.max.apply(null,i.filter(Boolean))),extraTags:{iterations:c}});var i}()}({target:n,task:function(e){return t.serializeNode(e,!0,r)},onFinish:function(e){return i(o,e,s)}})}for(var c=[],u=n.firstChild;u;u=u.nextSibling)c.push(this.serializeNode(u,!0,r));i(o,c,s)},e.prototype.disconnect=function(){this.mutationSummaries&&this.mutationSummaries.length&&(this.mutationSummaries.forEach((function(e){e.disconnect()})),this.mutationSummaries=[])},e.prototype.rememberNode=function(e){var t=this.nextId++;return this.knownNodes.set(e,t),(null==e?void 0:e.shadowRoot)&&!this.shadowRoots.get(e)&&this.shadowRoots.set(e,e.shadowRoot),t},e.prototype.forgetNode=function(e){this.knownNodes.deleteNode(e),this.shadowRoots.get(e)&&this.shadowRoots.deleteNode(e)},e.prototype.serializeNode=function(e,t,n,r){var i=this;if(null===e)return null;var a=this.knownNodes.get(e);if(void 0!==a&&!n)return{id:a};void 0===a&&(a=this.rememberNode(e));var s=e.shadowRoot||this.shadowRoots.get(e),u=c(c({nodeType:e.nodeType,id:a},function(e){var t=e.shadowRoot,n=e.getNextId,r={};if(!t)return r;if(r.hasShadowRoot=!0,r.isSyntheticShadow=!!t.synthetic,t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0){var i=t.adoptedStyleSheets.reduce((function(e,r){for(var i=[],o=0;o<r.cssRules.length;o++)i.push(r.cssRules[o].cssText);var a=n();return r.ownerHostNode=t.host,r.sheetId=a,e.push({id:a,rules:i}),e}),[]);r.adoptedStyleSheetsRules=i}return r}({shadowRoot:s,getNextId:o.b})),function(e){var t,n,r,i,o=e.node,a=e.shouldSuppressNode,s={};switch(o.nodeType){case Node.DOCUMENT_TYPE_NODE:t=o,s.name=""===t.name?"html":t.name,s.publicId=t.publicId,s.systemId=t.systemId;break;case Node.COMMENT_NODE:case Node.TEXT_NODE:i=hj.metrics.time(),n=hj.privacy.getSuppressedTextNode(o,a),hj.metrics.timeIncr("task-execution-time",{tag:{task:"node-suppression"},start:i}),a=n.shouldSuppressNode,s.textContent=n.content;break;case Node.ELEMENT_NODE:i=hj.metrics.time(),r=hj.privacy.getSuppressedNode(o,a),hj.metrics.timeIncr("task-execution-time",{tag:{task:"node-suppression"},start:i}),a=r.shouldSuppressNode,s.tagName=r.node.tagName,s.attributes=r.node.attributes,!s.attributes["data-hj-suppressed"]&&"IMG"===s.tagName&&o.currentSrc&&(s.attributes.src=o.currentSrc),"http://www.w3.org/1999/xhtml"!==(null==o?void 0:o.namespaceURI)&&(s.namespaceURI=o.namespaceURI)}return s}({node:e,shouldSuppressNode:r}));if(e.nodeType===Node.ELEMENT_NODE)if(hj.cssBlobs.handleBlobStyles(u),"SCRIPT"===u.tagName||"NOSCRIPT"===u.tagName)u.childNodes=[{nodeType:Node.TEXT_NODE,id:this.redactedContentId,textContent:""}],this.redactedContentId--;else if(t){u.childNodes=function(e){var t=e.node,n=e.initialChildNodes,r=e.shadowRoot,i=e.serialize,o=n||[];if(r&&r.childNodes.length){o=[];for(var a=r.firstChild;a;a=a.nextSibling){var s=i(a);s.isInShadowRoot=!0,o.push(s)}}if(t.childNodes.length)for(var c=t.firstChild;c;c=c.nextSibling)o.push(i(c));return o}({node:e,initialChildNodes:u.childNodes,shadowRoot:s,serialize:function(e){return i.serializeNode(e,!0,n,r)}})}return u},e.prototype.serializeAddedAndMoved=function(e,t,n){var r=this,i=e.concat(t).concat(n),o=new hj.MutationSummary.NodeMap;i.forEach((function(e){var t=e.parentNode,n=o.get(t);n||(n=new hj.MutationSummary.NodeMap,o.set(t,n)),n.set(e,!0)}));var a=[];return o.keys().forEach((function(e){for(var t=o.get(e),n=t.keys();n.length;){for(var i=n[0];i.previousSibling&&t.has(i.previousSibling);)i=i.previousSibling;for(;i&&t.has(i);){var s=r.serializeNode(i);s.previousSibling=r.serializeNode(i.previousSibling),i.parentNode&&i.parentNode.host&&i.parentNode.host instanceof HTMLElement?(s.parentNode=r.serializeNode(i.parentNode.host),s.isInShadowRoot=!0):s.parentNode=r.serializeNode(i.parentNode),a.push(s),t.deleteNode(i),i=i.nextSibling}n=t.keys()}})),a},e.prototype.serializeAttributeChanges=function(e){var t=this,n=new hj.MutationSummary.NodeMap;return Object.keys(e).forEach((function(r){e[r].forEach((function(e){var i=n.get(e);i||((i=t.serializeNode(e)).attributes={},n.set(e,i));var o=e.getAttribute(r);if(hj.cssBlobs.handleBlobStyles(e),"string"==typeof o&&o.length&&"src"!==r&&"class"!==r&&(o=o.replace(/-?\d+\.\d+%/g,(function(e){return Math.round(parseFloat(e))+"%"})).replace(/-?\d+\.\d+/g,(function(e){return parseFloat(e).toFixed(1)}))),"string"==typeof o){var a={value:o,name:r},s=hj.privacy.getSuppressedNodeAttribute(e,a,!1);s&&(i.attributes[s.name]=s.value)}else i.attributes[r]=o}))})),n.keys().map((function(e){return n.get(e)}))},e.prototype.applyChanged=function(e){var t=this,n=e[0],r=n.removed.filter((function(e){return void 0!==t.knownNodes.get(e)})).map((function(e){return t.serializeNode(e)})),i=this.serializeAddedAndMoved(n.added,n.reparented,n.reordered),o=this.serializeAttributeChanges(n.attributeChanged),a=n.characterDataChanged.map((function(e){var n=t.serializeNode(e);return n.textContent=hj.privacy.getSuppressedTextNode(e,!1).content,n}));this.mirror.applyChanged(r,i,o,a),n.removed.forEach((function(e){t.forgetNode(e)})),this.processPendingMutation()},e.prototype.getKnownNode=function(e){return this.knownNodes.get(e)},e.prototype.onTreeMirrorUpdate=function(e){this.pendingMutations.push(e)},e.prototype.processPendingMutation=function(){for(var e=void 0;e=this.pendingMutations.shift();)e()},e}),"TreeMirrorClient")();hj.treeMirror=hj.tryCatch((function(){var n,o,a={},s=[];function c(e,t,n,r){s.forEach((function(i){i(e,t,n,r)}))}return a.mutationObserverAvailable=void 0!==window.MutationObserver||void 0!==window.WebKitMutationObserver||void 0!==window.MozMutationObserver,a.getTree=hj.tryCatch((function(e,r){n&&a.mutationObserverAvailable?n.serializeTarget({target:document,onDemand:!0,onSerialization:e}):n=new t(document,{applyChanged:c},e);r&&a.mutationObserverAvailable&&s.push(r)}),"hj.treeMirror.manager.getTree"),a.getMirroredDocument=hj.tryCatch((function(t){var n,s;o||(o=new e(document.cloneNode(),{setAttribute:(n=function(e,t,n){e.setAttribute(t,n)},function(e,t,o){return"data-hj-suppressed"===t&&"object"===r(o)?(i(e,o,s),!0):n(e,t,o)})})),a.getTree((function(e,n){t(o.deserializeDocument(e,n,!0))}))}),"hj.treeMirror.manager.getMirroredDocument"),a.resetMutationListeners=function(){s=[]},a.disconnect=function(){n&&(n.disconnect(),n=null)},a.getNodeId=function(e){return n.getKnownNode(e)},a.onTreeMirrorUpdate=function(e){n.onTreeMirrorUpdate(e)},a}),"hj.treeMirror.manager")()}),"hj.treeMirror")()},function(e,t,n){"use strict";n.r(t);var r=n(80),i=n(142),o=n(55),a=n(3),s=n(29),c=n(25),u=n(23),l=!1;function h(e,t){return!!e&&(e.startsWith?e.startsWith(t):Object(c.a)(e,t))}var d="https://".concat(hj.insightsHost);try{hj.ui.retaker=Object.freeze({loadWidget:function(){document.getElementById(u.a)||(l=!1,window.opener.postMessage({type:u.d.ready,origin:window.location.origin},d))}})}catch(e){}var f=function(){!function(){if(hj.features.hasFeature("heatmap.continuous.manual_retaker")){var e="https://".concat(hj.insightsHost);if(h(document.referrer+"",e))if(!(!window.opener||!window.opener.postMessage)){window.addEventListener("message",(function(t){if(h(t.origin,e)&&t.data&&t.data.type===u.d.start){var n;if(l)return;sessionStorage.setItem(u.f,t.data.mode),sessionStorage.setItem(u.i,JSON.stringify(null!==(n=t.data.strings)&&void 0!==n?n:"{}")),Object(o.a)(a.f.HEATMAP_RETAKER),l=!0}}));try{t()}catch(e){window.addEventListener("load",(function(){t()}))}window.addEventListener("popstate",(function(){n()})),window.addEventListener("hashchange",(function(){n()}))}}function t(){window.opener.postMessage({type:u.d.ready,origin:window.location.origin},e)}function n(){document.getElementById(u.a)||(l=!1,t())}}()},g=n(117),p=n(12),v=n(130),m=n(71),y=n(79),j=n(7),b=n(60),w=n(16),S=n(173),_=n.n(S),E=n(174),C=n.n(E),O=n(4),I=n(21);function N(e){return(N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var T={maxLogsPerPage:100,_overflow:!1,_total:0,init:function(){hj.tryCatch((function(){hj.settings.session_capture_console_consent&&(hj.isPreview||Object(y.a)("error-capture.run")&&(window.addEventListener("error",T.onError),window.addEventListener("unhandledrejection",T.onUnhandledRejection)))}),"error-capture.run")()},onError:function(e){T.addError(e)},onUnhandledRejection:function(e){T.addError({error:e.reason,message:"Uncaught (in promise)",filename:"",lineno:0})},addError:function(e){hj.tryCatch((function(){var t=e.error,n=e.message,r=e.lineno,i=e.filename;if((t||n||r||i)&&!T.hasReachedOverflow()){var o=i&&"object"===N(i)?T.truncate(T.stringify(i),100):i,a="object"===N(r)?null:r,s=t&&t instanceof Error&&t.stack?C.a.parse(t).slice(0,20):null;Object(I.a)("error",{text:hj.privacy.suppressErrorMessage(T.getErrorMessage(e)),filename:o,lineno:a,trace:s,time:hj.time.getNow(),timestamp:O.a.now()})}}),"error-capture.addError")()},getErrorMessage:function(e){if(!e.error)return e.message||"";var t=T.truncate(T.stringify(e.error),1e3);return e.message?e.error instanceof Error&&-1!==e.message.indexOf(t)?t.replace(t,e.message):"".concat(e.message," ").concat(t):t},stringify:function(e){return e instanceof Error?e.toString():_()(e)},truncate:function(e,t){return e.length<=t?e:"".concat(e.substring(0,t-3),"...")},hasReachedOverflow:function(){return!T._overflow&&T._total<T.maxLogsPerPage?(T._total++,!1):(T._overflow=!0,!0)}},k=n(137);function R(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?R(Object(n),!0).forEach((function(t){x(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):R(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function x(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}hj.tryCatch((function(){void 0===hj.scriptLoaded&&(hj._init=hj.tryCatch((function(){var e={_sendVerifyInstallation:function(e){var t="".concat(hj.apiUrlBase,"/client/sites/").concat(hj.settings.site_id,"/verify-installation?sv=").concat(_hjSettings.hjsv||0);hj.ajax.post(t,{},hj.tryCatch(e,"init._sendVerifyInstallation"))},_verifyInstallationAuto:function(){if(hj.settings.tracking_code_verified)hj.log.debug("Tracking code verified.","init");else{hj.log.debug("Tracking code verified not found, updating first data.","init");var e="".concat(hj.apiUrlBase,"/client/sites/").concat(hj.settings.site_id,"/verify-installation/auto");hj.ajax.post(e,{},void 0,(function(t){hj.exceptions.log(new Error("Url: ".concat(e," - Status: ").concat(t.status)),"init._verifyInstallationAuto")}))}},_reportVerificationResults:function(e,t,n){if(e){var r="".concat(hj.apiUrlBase,"/tcvs/verification/").concat(e,"/result"),i=A(A({},n&&{error_detail:n}),{},{status:t});hj.ajax.post(r,i,(function(){hj.tcVerificationResultsSent=!0}),(function(e){e&&400!==e.status&&404!==e.status&&hj.exceptions.log(new Error("TCVS endpoint failed"),"init._reportVerificationResults"),hj.tcVerificationResultsSent=!0}))}},_verifyInstallation:function(){var t,n=hj.url.getParameter("hjVerifyInstall"),i=hj.url.getParameter("hjVerifyUUID"),o=function(){Object(r.a)({title:"Hotjar installation invalid",message:"The tracking code you are trying to verify does not match the one installed on this page. Please make sure you install the correct tracking code provided for this site.",status:"bad"}),e._reportVerificationResults(i,"wrong_code",{expected:String(hj.verifyInstall),actual:String(hjSiteSettings.site_id)});var t="Passed Site ID: ".concat(hj.verifyInstall," != Configured Site ").concat(hj.settings.site_id);hj.exceptions.log(new Error(t),"init._verifyInstallation")};try{t=sessionStorage.getItem("hjVerifyInstall")}catch(e){}if(n||t){hj.verifyInstall=parseInt(n||t);try{sessionStorage.setItem("hjVerifyInstall",n||t)}catch(e){}if(window.hjBootstrapCalled&&window.hjBootstrapCalled.length>1){var a,s=window.hjBootstrapCalled.filter((function(e,t){return window.hjBootstrapCalled.indexOf(e)===t})),c="You have "+window.hjBootstrapCalled.length+" tracking scripts installed on your site. ",u=2===window.hjBootstrapCalled.length?"script as this":"scripts as these",l=!!window.dataLayer;s.length>1?(a=c+"Please remove the duplicate "+u+" will cause issues.",Object(r.a)({title:"Multiple different Hotjar scripts detected",message:a,status:"bad"}),e._reportVerificationResults(i,"multiple_codes",{expected:String(hj.verifyInstall),actual:s,gtm:!1})):l?(a=c+"If you've installed Hotjar through GTM - please remove the duplicate "+u+" will cause issues.",e._reportVerificationResults(i,"multiple_codes",{expected:String(hj.verifyInstall),actual:s,gtm:!0}),Object(r.a)({title:"Multiple Hotjar scripts detected",message:a,status:"bad"})):hj.verifyInstall===hj.settings.site_id?(a=c+"This will not affect data collection, but we do suggest removing redundant scripts.",e._reportVerificationResults(i,"warning",{expected:String(hj.verifyInstall),reason:"multiple scripts"}),Object(r.a)({title:"Multiple Hotjar scripts detected",message:a,status:"warning"})):o();var h="Passed Site ID: "+hj.verifyInstall+" contains multiple scripts "+window.hjBootstrapCalled.join(", ");hj.exceptions.log(new Error(h),"init._verifyInstallation")}else hj.verifyInstall===hj.settings.site_id?(e._sendVerifyInstallation((function(e){e.success||hj.exceptions.log(new Error("Verify installation endpoint failed"),"init._verifyInstallation")})),Object(r.a)({title:"Hotjar installation verified",message:"The Hotjar tracking code has been properly installed on this page. Browse your site in this window if you wish to verify installation on any other pages.",status:"good"}),e._reportVerificationResults(i,"ok")):o()}}};return e._browserIsSupported=hj.tryCatch((function(){return!(Object(w.d)()<11)||(hj.log.debug("IE < 11 is not supported.","init"),"1"===hj.url.getParameter("hjVerifyInstallation")&&Object(r.a)({title:"Hotjar installation cannot be verified.",message:"Sorry – your browser is not supported.",status:"bad"}),!1)}),"init"),e._checkDebug=hj.tryCatch((function(){var e=hj.url.getParameter(j.a.items.DEBUG_FLAG.getKey());e&&("1"===e||"true"===e?hj.debug.on(!0):hj.debug.off()),"true"===j.a.items.DEBUG_FLAG.get()&&hj.debug.on(!1)}),"init"),e._checkEndSignal=hj.tryCatch((function(){"1"===hj.url.getParameter("hjEndSignal")&&hj.eventStream.queueEndSignal()}),"init"),e._canRun=hj.tryCatch((function(){return-1===navigator.userAgent.indexOf("Hotjar")&&(j.a.canUseCookies()?j.a.canUseLocalStorage()?!!j.a.canUseSessionStorage()||(hj.log.debug("sessionStorage is not available","init"),!1):(hj.log.debug("localStorage is not available","init"),!1):(hj.log.debug("Cookies are not enabled"),!1))}),"init"),e._configureStateChangeListenMode=function(){var e="manual";hj.settings&&hj.settings.state_change_listen_mode&&(e=hj.settings.state_change_listen_mode),hj.locationListener.setMode(e)},e.run=hj.tryCatch((function(t){hj.currentUrl=t,hj.scriptLoaded=!0,e._browserIsSupported()&&(e._checkDebug(),e._checkEndSignal(),e._canRun()?e._run(t):hj._init._verifyInstallation())}),"init"),e._run=hj.tryCatch((function(t){if("1"!==navigator.doNotTrack&&"1"!==window.doNotTrack&&"1"!==navigator.msDoNotTrack||(hj.log.debug("Visitor has opted out of tracking.","init"),hj.optOut=!0),hj.log.debug("Site settings","init",hj.settings),function(e){k.a.set(e)}(hj.currentUrl),f(),hj.settings.site_id){hj.isIncludedInSample=y.a;var n=Object(m.b)();n&&!n.existing&&p.a.setFirstSeen(),b.userAttributes.init(),T.init(),e._configureStateChangeListenMode(),e._runPage(t),e._verifyInstallationAuto(),e._verifyInstallation(),hj.command.activate(),"1"===hj.url.getParameter("hjIncludeInSample")&&Object(r.a)({title:"Hotjar tracking active.",message:"Hotjar tracking is active for your session.",status:"good"})}else hj.log.warn("Script execution for halted due to no site id: ".concat(window.location.href),"init")}),"init"),e.reinit=hj.tryCatch((function(t,n){hj.currentUrl=t,hj.widget.emptyMatchingWidgets(),hj.metrics.reset(),n&&(j.a.items.HAS_CACHED_USER_ATTRIBUTES.clear(),j.a.localStorage.USER_ATTRIBUTES.clear(),g.a.reset()),e._runPage(t)}),"init"),e._runPage=hj.tryCatch((function(e){Object(v.c)(),hj.optOut||(Object(i.b)(e)||Object(y.a)("init._runPage")&&hj.visitData.track(e),hj.visitData.trackView()),hj.hq.each(hj.loader.getModules(),(function(t,n){hj.optOut&&!n.nonTracking||(hj.log.debug("Running module","init",n.name),n.module.run(e))})),hj.widget.runLatestMatchingWidget(),hj.widget.runInlineEmbeddedWidgets()}),"init"),e}),"init")(),hj.hq(document).ready((function(){hj.log.debug("Document is ready. Initializing...","init"),hj.scriptContextId=Object(s.a)(),hj._init.run(location.href),(hj.metrics.getConfig("browser").inLab||hj.metrics.getState("isMetricsEnabled"))&&Object(o.a)(a.f.BROWSER_PERF)})))}),"init")()},,,function(e,t,n){"use strict";n.r(t);var r=n(60),i=n(128),o=n(71);function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){i=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"https://ask.hotjar.io/";return{v1:"".concat(e,"api/v1"),v2:"".concat(e,"api/v2")}}(_hjSettings.askUrl);n(229),n(230),function(){try{hj.request=function(){var e={},t={granted:null,callbacks:[],inProgress:!1},n=function(){return Object(o.a)(!0)||""},s=[],u=!1;function l(e,t,n,r){s.push([e,t,n,r]),u||function e(){if(0!==s.length){u=!0;var t=a(s.shift(),4),n=t[0],r=t[1],i=t[2],o=t[3];hj.ajax.post(n,r,(function(t){try{i&&i(t)}finally{e()}}),(function(t){try{o&&o(t)}finally{e()}}))}else u=!1}()}function h(e,t,i,o){var a=hj.ui.getWindowSize();i.window_width=i.window_width||a.width,i.window_height=i.window_height||a.height,i.user_id=t?n():void 0,i.url=document.location.href,r.userAttributes.flush((function(t,n,a){t?i.identify_user_id=r.userAttributes.get("user_id")||null:(i.identify_user_id=n,i.identify_attributes=a),l(e,i,(function(e){o&&o(e)}))}))}return e.getConsentGranted=hj.tryCatch((function(e){var r,i="?";null!==t.granted&&e?e(t.granted):(e&&t.callbacks.push(e),t.inProgress||(i+="user_id="+n(),t.inProgress=!0,hj.ajax.get("".concat(hj.apiUrlBase,"/sites/").concat(hj.settings.site_id,"/consent").concat(i),(function(e){for(t.granted=!!e.success&&-1!==e.scopes.indexOf("associate"),t.inProgress=!1;r=t.callbacks.pop();)hj.tryCatch(r,"ConsentData")(t.granted)}),(function(){for(t.granted=!1,t.inProgress=!1;r=t.callbacks.pop();)hj.tryCatch(r,"ConsentData")(t.granted)}))))}),"hj.request.getConsentGranted"),e.grantConsent=hj.tryCatch((function(e,r){e.user_id=n(),e.action="grant_for_response",l("".concat(hj.apiUrlBase,"/sites/").concat(hj.settings.site_id,"/consent/associate"),e,(function(e){t.granted=!!e.success,r&&hj.tryCatch(r,"GrantConsent")(t.granted)}),(function(){t.granted=!1}))}),"hj.request.getConsent"),e.saveFeedbackResponse=hj.tryCatch((function(e,t,n,r){e.action="feedback_widget_response",hj.event.signal("feedback.send",{id:t}),h("".concat(c.v1,"/client/sites/").concat(hj.settings.site_id,"/feedback/").concat(t),n,e,(function(n){var i;if(hj.tryCatch(r,"Data")(n),"number"==typeof(null===(i=e.response)||void 0===i?void 0:i.emotion)){var o={emotion:e.response.emotion,id:t,response_id:n.feedback_response_id};hj.event.signal("feedback.sentiment",o)}}))}),"data"),e.savePollResponse=hj.tryCatch((function(e,t,n,r){var o=hj.widget.pollsResponsesUUID[e];n.action="create_or_update_poll_response";var a=i.sessionEvents.get();a&&(n.events=hj.hq.stringify(a)),h("".concat(c.v2,"/client/sites/").concat(hj.settings.site_id,"/poll/").concat(e,"/response/").concat(o),t,n,(function(t){t.is_new_response&&hj.event.signal("poll.send",{id:e,response_id:t.poll_response_id}),null==r||r(t)}))}),"data"),e.completePollResponse=hj.tryCatch((function(e,t){var n=hj.widget.pollsResponsesUUID[e];l("".concat(c.v2,"/client/sites/").concat(hj.settings.site_id,"/poll/").concat(e,"/response/").concat(n),{action:"finish_poll_response",completion_time_from_engagement_ms:t.fromEngagement,completion_time_from_render_ms:t.fromRender})}),"data"),e}()}catch(e){hj.exceptions.log(e,"hj.request")}}(),n(129),n(106),n(251),n(149),n(231)},function(e,t,n){"use strict";n.r(t);var r=n(16),i=n(71),o={getAsNumber:function(){var e=Object(i.a)(!0);return(parseInt(e.slice(-10),16)+1)/Math.pow(2,40)},compareRatio:hj.tryCatch((function(e,t){return o.getAsNumber()*(t?100:1)<=e}),"identifier.compareRatio")},a=["af","ar","bg","ca","cs","cy","da","de","el","en","es","et","fa","fi","fr","he","hr","hu","id","it","ja","ko","lt","lv","mis","nb","nl","pl","pt_BR","pt","ro","ru","sk","sl","sq","sr","sv","sw","th","tl","tr","uk","vi","zh_CN","zh_TW"];hj.tryCatch((function(){var e,t,n,i,s,c,u;hj.widget=(n=["ar","fa","he"],i=[],s=[],c=[],u=!1,(t={}).ctrl=void 0,t.data=void 0,t.model={},t.activeLanguageDirection="ltr",t.widgetAttributePrefix="_hj-f5b2a1eb-9b07",t.ctaLinks={feedback:"https://www.hotjar.com/incoming-feedback?utm_source=client&utm_medium=incoming_feedback&utm_campaign=insights",polls:"https://www.hotjar.com/feedback-surveys?utm_source=client&utm_medium=poll&utm_campaign=insights",surveys:"https://www.hotjar.com/?utm_source=client&utm_medium=survey&utm_campaign=insights"},t.getActiveLanguage=function(){var t;return null!==(t=e)&&void 0!==t?t:"en"},t.addMatchingWidget=function(e,n,r,a,c,l,h){if(void 0===a||hj.isPreview||o.compareRatio(a,!0)){var d={type:e,id:n,created:r,runCallback:c,removeCallback:l,isInlineEmbedded:h};if(h)return s.push(d);u?t.setActiveWidget(d):i.push(d)}else hj.log.debug("Session identifier not in targeting percentage. Widget will not match.","targeting")},t.clearWidget=hj.tryCatch((function(){hj.hq("#_hj_poll_container,#_hj_feedback_container,._hj-widget-container").length>0&&(hj.log.debug("Removing previously shown widget from DOM","widgets"),hj.hq("#_hj_poll_container,#_hj_feedback_container,._hj-widget-container").remove())}),"widgets"),t.disableSubmit=hj.tryCatch((function(){hj.widget.ctrl.find("#_hj-f5b2a1eb-9b07_action_submit").addClass("_hj-f5b2a1eb-9b07_btn_disabled")}),"common"),t.emptyMatchingWidgets=function(){i=[],s=[],c.forEach((function(e){e.disconnect()})),c=[],hj.rendering.clearAllAbandonEvents(),u=!1},t.enableSubmit=hj.tryCatch((function(){hj.widget.ctrl.find("#_hj-f5b2a1eb-9b07_action_submit").removeClass("_hj-f5b2a1eb-9b07_btn_disabled")}),"common"),t.renderLegal=hj.tryCatch((function(e){var n=hj.settings.legal_name||"",r=hj.settings.privacy_policy_url||"",i="";return e&&""!==n&&""!==r&&(i=hj.rendering.renderTemplate('<div class="<%=p%>_widget_legal">                        <div class="<%=p%>_pull_left">                            <%=legalName%>                        </div>                        <div class="<%=p%>_pull_right">                            <a href="<%=privacyPolicyUrl%>" target="_blank" rel="noopener noreferrer">                                <%=hj.feedback.translate("privacy_policy")%>                            </a>                        </div>                        <div class="<%=p%>_clear_both"></div>                    </div>',{p:t.widgetAttributePrefix,legalName:n,privacyPolicyUrl:new hj.rendering.TrustedString(r)})),i})),t.enterFullScreen=hj.tryCatch((function(){var e,n;t.isPhoneOrTablet()&&((e=hj.hq("body")).addClass("_hj-f5b2a1eb-9b07_position_fixed"),0===hj.hq("#hotjar-viewport-meta").length&&((n=document.createElement("meta")).id="hotjar-viewport-meta",n.name="viewport",n.content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",document.getElementsByTagName("head")[0].appendChild(n)),e.addClass("_hj-f5b2a1eb-9b07_fullscreen_page"))}),"common"),t.exitFullScreen=hj.tryCatch((function(){hj.hq("#hotjar-viewport-meta").remove(),hj.hq("body").removeClass("_hj-f5b2a1eb-9b07_fullscreen_page").removeClass("_hj-f5b2a1eb-9b07_position_fixed")}),"common"),t.isPhoneOrTablet=hj.tryCatch((function(){return hj.widget.isVeryNarrowScreen()||"phone"===Object(r.a)()||"tablet"===Object(r.a)()}),"common"),t.isVeryNarrowScreen=hj.tryCatch((function(){return hj.hq(window).width()<=450}),"common"),t.removeActiveWidget=function(e){hj.widgetDelay.clear(),e=e||function(){},t.activeWidget?(t.activeWidget.removeCallback&&t.activeWidget.removeCallback(e),delete t.activeWidget):e()},t.runLatestMatchingWidget=function(){var e;i.forEach((function(t){(!e||e.created<t.created)&&(e=t)})),e?t.setActiveWidget(e):t.removeActiveWidget(),u=!0},t.runInlineEmbeddedWidgets=function(){s.forEach((function(e){e.runCallback()}))},t.setActiveWidget=function(e){t.activeWidget&&e.type===t.activeWidget.type&&e.id===t.activeWidget.id||t.removeActiveWidget((function(){e.runCallback(),t.activeWidget=e}))},t.setLanguage=hj.tryCatch((function(t){if(!a.includes(t))throw new Error('Invalid language "'+t+'"');e=t,hj.widget.activeLanguageDirection=n.indexOf(t)>-1?"rtl":"ltr",hj.widget.isActiveLanguageDirectionRtl="rtl"===hj.widget.activeLanguageDirection}),"common"),t.registerObserverForInlineWidget=function(e){c.push(e)},t),hj.widgetDelay=function(){var e={},t=null;return e.clear=hj.tryCatch((function(){clearTimeout(t),t=null}),"hj.widgetDelay.clear"),e.set=hj.tryCatch((function(n,r){e.clear(),t=setTimeout(n,r)}),"hj.widgetDelay.set"),e}()}),"widgets")()}]);
