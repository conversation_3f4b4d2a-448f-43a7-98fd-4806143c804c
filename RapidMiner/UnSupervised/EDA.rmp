<?xml version="1.0" encoding="UTF-8"?><process version="9.10.010">
  <context>
    <input/>
    <output/>
    <macros/>
  </context>
  <operator activated="true" class="process" compatibility="9.10.010" expanded="true" name="Process">
    <parameter key="logverbosity" value="init"/>
    <parameter key="random_seed" value="2001"/>
    <parameter key="send_mail" value="never"/>
    <parameter key="notification_email" value=""/>
    <parameter key="process_duration_for_mail" value="30"/>
    <parameter key="encoding" value="SYSTEM"/>
    <process expanded="true">
      <operator activated="true" class="retrieve" compatibility="9.10.010" expanded="true" height="68" name="Retrieve marketing_campaign_edited" width="90" x="45" y="34">
        <parameter key="repository_entry" value="//New Local Repository/Data/DESD/Case Study/marketing_campaign_edited"/>
      </operator>
      <operator activated="true" class="nominal_to_date" compatibility="9.10.010" expanded="true" height="82" name="Nominal to Date" width="90" x="179" y="34">
        <parameter key="attribute_name" value="Dt_Customer"/>
        <parameter key="date_type" value="date"/>
        <parameter key="date_format" value="dd-MM-yyyy"/>
        <parameter key="time_zone" value="SYSTEM"/>
        <parameter key="locale" value="English (United States)"/>
        <parameter key="keep_old_attribute" value="false"/>
      </operator>
      <operator activated="true" class="generate_attributes" compatibility="9.10.010" expanded="true" height="82" name="Generate Attributes" width="90" x="313" y="34">
        <list key="function_descriptions">
          <parameter key="Age" value="2022-Year_Birth"/>
          <parameter key="Kids" value="Kidhome+Teenhome"/>
          <parameter key="Expenses" value="MntFishProducts+MntFruits+MntGoldProds+MntMeatProducts+MntSweetProducts+MntWines"/>
          <parameter key="NumTotalPurchase" value="NumCatalogPurchases+NumDealsPurchases+NumStorePurchases+NumWebPurchases"/>
          <parameter key="TotalAcceptedCmp" value="AcceptedCmp1+AcceptedCmp2+AcceptedCmp3+AcceptedCmp4+AcceptedCmp5+Response"/>
          <parameter key="Day" value="floor(date_diff(Dt_Customer,date_parse_custom(&quot;30-06-2014&quot;,&quot;dd-MM-yyyy&quot;))/1000/60/60/24)"/>
          <parameter key="Income Category" value="if(Income&lt;40000,&quot;Low Income&quot;, if(Income&lt;60000,&quot;Medium Income&quot;,&quot;High Income&quot;))"/>
        </list>
        <parameter key="keep_all" value="true"/>
      </operator>
      <operator activated="true" class="generate_attributes" compatibility="9.10.010" expanded="true" height="82" name="Generate Attributes (2)" width="90" x="447" y="34">
        <list key="function_descriptions">
          <parameter key="AmountPerPurchase" value="Expenses/NumTotalPurchase"/>
          <parameter key="% of Wines" value="MntWines/Expenses*100"/>
          <parameter key="% of Fish" value="MntFishProducts/Expenses*100"/>
          <parameter key="% of Fruit" value="MntFruits/Expenses*100"/>
          <parameter key="% of Gold" value="MntGoldProds/Expenses*100"/>
          <parameter key="% of Meat" value="MntMeatProducts/Expenses*100"/>
          <parameter key="% of Sweet" value="MntSweetProducts/Expenses*100"/>
        </list>
        <parameter key="keep_all" value="true"/>
      </operator>
      <operator activated="true" class="filter_examples" compatibility="9.10.010" expanded="true" height="103" name="Filter Examples" width="90" x="581" y="34">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="custom_filters"/>
        <parameter key="invert_filter" value="false"/>
        <list key="filters_list">
          <parameter key="filters_entry_key" value="Age.lt.100"/>
          <parameter key="filters_entry_key" value="NumTotalPurchase.gt.1"/>
        </list>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="replace_missing_values" compatibility="9.10.010" expanded="true" height="103" name="Replace Missing Values" width="90" x="715" y="34">
        <parameter key="return_preprocessing_model" value="false"/>
        <parameter key="create_view" value="false"/>
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="Income"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="default" value="average"/>
        <list key="columns"/>
      </operator>
      <operator activated="true" class="concurrency:correlation_matrix" compatibility="9.10.010" expanded="true" height="103" name="Correlation Matrix" width="90" x="849" y="34">
        <parameter key="attribute_filter_type" value="subset"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value="NumCatalogPurchases|NumDealsPurchases|NumStorePurchases|NumWebPurchases|NumTotalPurchase|NumWebVisitsMonth|TotalAcceptedCmp"/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="true"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="normalize_weights" value="true"/>
        <parameter key="squared_correlation" value="false"/>
      </operator>
      <operator activated="true" class="numerical_to_polynominal" compatibility="9.10.010" expanded="true" height="82" name="Numerical to Polynominal" width="90" x="916" y="187">
        <parameter key="attribute_filter_type" value="subset"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value="Kids|TotalAcceptedCmp"/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
      </operator>
      <connect from_op="Retrieve marketing_campaign_edited" from_port="output" to_op="Nominal to Date" to_port="example set input"/>
      <connect from_op="Nominal to Date" from_port="example set output" to_op="Generate Attributes" to_port="example set input"/>
      <connect from_op="Generate Attributes" from_port="example set output" to_op="Generate Attributes (2)" to_port="example set input"/>
      <connect from_op="Generate Attributes (2)" from_port="example set output" to_op="Filter Examples" to_port="example set input"/>
      <connect from_op="Filter Examples" from_port="example set output" to_op="Replace Missing Values" to_port="example set input"/>
      <connect from_op="Replace Missing Values" from_port="example set output" to_op="Correlation Matrix" to_port="example set"/>
      <connect from_op="Correlation Matrix" from_port="example set" to_op="Numerical to Polynominal" to_port="example set input"/>
      <connect from_op="Correlation Matrix" from_port="matrix" to_port="result 1"/>
      <connect from_op="Numerical to Polynominal" from_port="example set output" to_port="result 2"/>
      <portSpacing port="source_input 1" spacing="0"/>
      <portSpacing port="sink_result 1" spacing="0"/>
      <portSpacing port="sink_result 2" spacing="0"/>
      <portSpacing port="sink_result 3" spacing="0"/>
    </process>
  </operator>
</process>
