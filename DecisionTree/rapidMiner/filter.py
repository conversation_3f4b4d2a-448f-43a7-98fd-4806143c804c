#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sun Oct 15 21:58:41 2023

@author: jaideepshah
"""

import pandas as pd

def filter_rows_from_csv(input_filename, output_filename, column_name, condition_value):
    """
    Reads a CSV file, deletes rows based on a condition in a specific column, and saves the filtered data.

    Parameters:
    - input_filename: The path to the input CSV file.
    - output_filename: The path where the filtered CSV will be saved.
    - column_name: The name of the column based on which rows will be filtered.
    - condition_value: The threshold value to use for filtering.
    """
    # Read the CSV file into a DataFrame
    df = pd.read_csv(input_filename)
    
    # Filter out rows where the column value is greater than the condition value
    df_filtered = df[df[column_name] <= condition_value]
    
    # Save the filtered DataFrame to a new CSV file
    df_filtered.to_csv(output_filename, index=False)
    print(f"Filtered data saved to {output_filename}")

# Note: To run the function, you can use the following line:
#filter_rows_from_csv('a.csv', 'out.csv', 'lead_time', 60)


import pandas as pd

def duplicate_rows(csv_file, column_name, value):
    # Read the CSV file into a pandas DataFrame
    df = pd.read_csv(csv_file)
    
    # Find rows where the column contains the specified value
    rows_to_duplicate = df[df[column_name] == value]
    
    # Concatenate these rows to the DataFrame
    df = pd.concat([df, rows_to_duplicate], ignore_index=True)
    
    # Save the modified DataFrame back to the CSV file
    df.to_csv(csv_file, index=False)

# Example usage:
duplicate_rows('a2.csv', 'booking_status', 'Canceled')





