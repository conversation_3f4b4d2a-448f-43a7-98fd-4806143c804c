<?xml version="1.0" encoding="UTF-8"?><process version="9.10.010">
  <context>
    <input/>
    <output/>
    <macros/>
  </context>
  <operator activated="true" class="process" compatibility="9.10.010" expanded="true" name="Process">
    <parameter key="logverbosity" value="init"/>
    <parameter key="random_seed" value="2001"/>
    <parameter key="send_mail" value="never"/>
    <parameter key="notification_email" value=""/>
    <parameter key="process_duration_for_mail" value="30"/>
    <parameter key="encoding" value="SYSTEM"/>
    <process expanded="true">
      <operator activated="true" class="retrieve" compatibility="9.10.010" expanded="true" height="68" name="Retrieve" width="90" x="45" y="34">
        <parameter key="repository_entry" value="//New Local Repository/Data/DESD/Case Study/marketing_campaign_edited"/>
      </operator>
      <operator activated="true" class="subprocess" compatibility="9.10.010" expanded="true" height="82" name="Pre-Processing" width="90" x="179" y="34">
        <process expanded="true">
          <operator activated="true" class="nominal_to_date" compatibility="9.10.010" expanded="true" height="82" name="Nominal to Date" width="90" x="179" y="34">
            <parameter key="attribute_name" value="Dt_Customer"/>
            <parameter key="date_type" value="date"/>
            <parameter key="date_format" value="dd-MM-yyyy"/>
            <parameter key="time_zone" value="SYSTEM"/>
            <parameter key="locale" value="English (United States)"/>
            <parameter key="keep_old_attribute" value="false"/>
          </operator>
          <operator activated="true" class="map" compatibility="9.10.010" expanded="true" height="82" name="Map" width="90" x="313" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Marital_Status"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="attribute_value"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="time"/>
            <parameter key="block_type" value="attribute_block"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="value_matrix_row_start"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <list key="value_mappings">
              <parameter key="Married" value="Relationship"/>
              <parameter key="Together" value="Relationship"/>
              <parameter key="Divorced" value="Single"/>
              <parameter key="Widow" value="Single"/>
            </list>
            <parameter key="consider_regular_expressions" value="false"/>
            <parameter key="add_default_mapping" value="false"/>
          </operator>
          <operator activated="true" class="generate_attributes" compatibility="9.10.010" expanded="true" height="82" name="Generate Attributes (3)" width="90" x="447" y="34">
            <list key="function_descriptions">
              <parameter key="Status" value="Marital_Status"/>
            </list>
            <parameter key="keep_all" value="true"/>
          </operator>
          <operator activated="true" class="map" compatibility="9.10.010" expanded="true" height="82" name="Map (2)" width="90" x="581" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Status"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="attribute_value"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="time"/>
            <parameter key="block_type" value="attribute_block"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="value_matrix_row_start"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <list key="value_mappings">
              <parameter key="Single" value="1"/>
              <parameter key="Relationship" value="2"/>
            </list>
            <parameter key="consider_regular_expressions" value="false"/>
            <parameter key="add_default_mapping" value="false"/>
          </operator>
          <operator activated="true" class="parse_numbers" compatibility="9.10.010" expanded="true" height="82" name="Parse Numbers" width="90" x="715" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Status"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <parameter key="decimal_character" value="."/>
            <parameter key="grouped_digits" value="false"/>
            <parameter key="grouping_character" value=","/>
            <parameter key="infinity_representation" value=""/>
            <parameter key="unparsable_value_handling" value="fail"/>
          </operator>
          <operator activated="true" class="generate_attributes" compatibility="9.10.010" expanded="true" height="82" name="Generate Attributes" width="90" x="849" y="34">
            <list key="function_descriptions">
              <parameter key="Age" value="2022-Year_Birth"/>
              <parameter key="Kids" value="Kidhome+Teenhome"/>
              <parameter key="Expenses" value="MntFishProducts+MntFruits+MntGoldProds+MntMeatProducts+MntSweetProducts+MntWines"/>
              <parameter key="NumTotalPurchase" value="NumCatalogPurchases+NumDealsPurchases+NumStorePurchases+NumWebPurchases"/>
              <parameter key="TotalAcceptedCmp" value="AcceptedCmp1+AcceptedCmp2+AcceptedCmp3+AcceptedCmp4+AcceptedCmp5+Response"/>
              <parameter key="Day" value="floor(date_diff(Dt_Customer,date_parse_custom(&quot;30-06-2014&quot;,&quot;dd-MM-yyyy&quot;))/1000/60/60/24)"/>
            </list>
            <parameter key="keep_all" value="true"/>
          </operator>
          <operator activated="true" class="generate_attributes" compatibility="9.10.010" expanded="true" height="82" name="Generate Attributes (2)" width="90" x="983" y="34">
            <list key="function_descriptions">
              <parameter key="Family Size" value="Status+Kids"/>
              <parameter key="AmountPerPurchase" value="Expenses/NumTotalPurchase"/>
            </list>
            <parameter key="keep_all" value="true"/>
          </operator>
          <operator activated="true" class="filter_examples" compatibility="9.10.010" expanded="true" height="103" name="Filter Examples" width="90" x="45" y="187">
            <parameter key="parameter_expression" value=""/>
            <parameter key="condition_class" value="custom_filters"/>
            <parameter key="invert_filter" value="false"/>
            <list key="filters_list">
              <parameter key="filters_entry_key" value="Age.lt.100"/>
              <parameter key="filters_entry_key" value="NumTotalPurchase.ne.0"/>
            </list>
            <parameter key="filters_logic_and" value="true"/>
            <parameter key="filters_check_metadata" value="true"/>
          </operator>
          <operator activated="true" class="replace_missing_values" compatibility="9.10.010" expanded="true" height="103" name="Replace Missing Values" width="90" x="179" y="187">
            <parameter key="return_preprocessing_model" value="false"/>
            <parameter key="create_view" value="false"/>
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Income"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="attribute_value"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="time"/>
            <parameter key="block_type" value="attribute_block"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="value_matrix_row_start"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <parameter key="default" value="average"/>
            <list key="columns"/>
            <parameter key="replenishment_value" value="51315"/>
          </operator>
          <connect from_port="in 1" to_op="Nominal to Date" to_port="example set input"/>
          <connect from_op="Nominal to Date" from_port="example set output" to_op="Map" to_port="example set input"/>
          <connect from_op="Map" from_port="example set output" to_op="Generate Attributes (3)" to_port="example set input"/>
          <connect from_op="Generate Attributes (3)" from_port="example set output" to_op="Map (2)" to_port="example set input"/>
          <connect from_op="Map (2)" from_port="example set output" to_op="Parse Numbers" to_port="example set input"/>
          <connect from_op="Parse Numbers" from_port="example set output" to_op="Generate Attributes" to_port="example set input"/>
          <connect from_op="Generate Attributes" from_port="example set output" to_op="Generate Attributes (2)" to_port="example set input"/>
          <connect from_op="Generate Attributes (2)" from_port="example set output" to_op="Filter Examples" to_port="example set input"/>
          <connect from_op="Filter Examples" from_port="example set output" to_op="Replace Missing Values" to_port="example set input"/>
          <connect from_op="Replace Missing Values" from_port="example set output" to_port="out 1"/>
          <portSpacing port="source_in 1" spacing="0"/>
          <portSpacing port="source_in 2" spacing="0"/>
          <portSpacing port="sink_out 1" spacing="0"/>
          <portSpacing port="sink_out 2" spacing="0"/>
        </process>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.010" expanded="true" height="103" name="Multiply" width="90" x="313" y="34"/>
      <operator activated="true" class="select_attributes" compatibility="9.10.010" expanded="true" height="82" name="Select Attributes" width="90" x="447" y="34">
        <parameter key="attribute_filter_type" value="subset"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value="AcceptedCmp1|AcceptedCmp2|AcceptedCmp3|AcceptedCmp4|AcceptedCmp5|Complain|Dt_Customer|Education|ID|Marital_Status|Response|Status|Year_Birth|Kids|Kidhome|Teenhome|Income|Age|Family Size"/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="true"/>
        <parameter key="include_special_attributes" value="false"/>
      </operator>
      <operator activated="true" class="subprocess" compatibility="9.10.010" expanded="true" height="208" name="Modelling" width="90" x="447" y="238">
        <process expanded="true">
          <operator activated="true" class="normalize" compatibility="9.10.010" expanded="true" height="103" name="Normalize" width="90" x="45" y="34">
            <parameter key="return_preprocessing_model" value="false"/>
            <parameter key="create_view" value="false"/>
            <parameter key="attribute_filter_type" value="all"/>
            <parameter key="attribute" value=""/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="numeric"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="real"/>
            <parameter key="block_type" value="value_series"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="value_series_end"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <parameter key="method" value="Z-transformation"/>
            <parameter key="min" value="0.0"/>
            <parameter key="max" value="1.0"/>
            <parameter key="allow_negative_values" value="false"/>
          </operator>
          <operator activated="true" class="principal_component_analysis" compatibility="9.10.010" expanded="true" height="103" name="PCA" width="90" x="179" y="34">
            <parameter key="dimensionality_reduction" value="keep variance"/>
            <parameter key="variance_threshold" value="1.0"/>
            <parameter key="number_of_components" value="1"/>
          </operator>
          <operator activated="true" class="denormalize" compatibility="9.10.010" expanded="true" height="82" name="De-Normalize" width="90" x="112" y="238">
            <parameter key="de-normalize_predictions" value="false"/>
            <parameter key="missing_attribute_handling" value="proceed on missing"/>
          </operator>
          <operator activated="true" class="concurrency:k_means" compatibility="9.10.010" expanded="true" height="82" name="Clustering" width="90" x="313" y="34">
            <parameter key="add_cluster_attribute" value="true"/>
            <parameter key="add_as_label" value="false"/>
            <parameter key="remove_unlabeled" value="false"/>
            <parameter key="k" value="5"/>
            <parameter key="max_runs" value="10"/>
            <parameter key="determine_good_start_values" value="true"/>
            <parameter key="measure_types" value="NumericalMeasures"/>
            <parameter key="mixed_measure" value="MixedEuclideanDistance"/>
            <parameter key="nominal_measure" value="NominalDistance"/>
            <parameter key="numerical_measure" value="EuclideanDistance"/>
            <parameter key="divergence" value="SquaredEuclideanDistance"/>
            <parameter key="kernel_type" value="radial"/>
            <parameter key="kernel_gamma" value="1.0"/>
            <parameter key="kernel_sigma1" value="1.0"/>
            <parameter key="kernel_sigma2" value="0.0"/>
            <parameter key="kernel_sigma3" value="2.0"/>
            <parameter key="kernel_degree" value="3.0"/>
            <parameter key="kernel_shift" value="1.0"/>
            <parameter key="kernel_a" value="1.0"/>
            <parameter key="kernel_b" value="0.0"/>
            <parameter key="max_optimization_steps" value="100"/>
            <parameter key="use_local_random_seed" value="true"/>
            <parameter key="local_random_seed" value="1"/>
          </operator>
          <operator activated="true" class="cluster_distance_performance" compatibility="9.10.010" expanded="true" height="103" name="Performance" width="90" x="447" y="34">
            <parameter key="main_criterion" value="Avg. within centroid distance"/>
            <parameter key="main_criterion_only" value="false"/>
            <parameter key="normalize" value="true"/>
            <parameter key="maximize" value="true"/>
          </operator>
          <operator activated="true" class="apply_model" compatibility="9.10.010" expanded="true" height="82" name="Apply Model" width="90" x="447" y="238">
            <list key="application_parameters"/>
            <parameter key="create_view" value="false"/>
          </operator>
          <operator activated="true" class="aggregate" compatibility="9.10.010" expanded="true" height="82" name="Aggregate" width="90" x="782" y="340">
            <parameter key="use_default_aggregation" value="true"/>
            <parameter key="attribute_filter_type" value="all"/>
            <parameter key="attribute" value=""/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="attribute_value"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="time"/>
            <parameter key="block_type" value="attribute_block"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="value_matrix_row_start"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <parameter key="default_aggregation_function" value="average"/>
            <list key="aggregation_attributes"/>
            <parameter key="group_by_attributes" value="cluster"/>
            <parameter key="count_all_combinations" value="false"/>
            <parameter key="only_distinct" value="false"/>
            <parameter key="ignore_missings" value="true"/>
          </operator>
          <connect from_port="in 1" to_op="Normalize" to_port="example set input"/>
          <connect from_op="Normalize" from_port="example set output" to_op="PCA" to_port="example set input"/>
          <connect from_op="Normalize" from_port="preprocessing model" to_op="De-Normalize" to_port="model input"/>
          <connect from_op="PCA" from_port="example set output" to_op="Clustering" to_port="example set"/>
          <connect from_op="PCA" from_port="original" to_port="out 6"/>
          <connect from_op="PCA" from_port="preprocessing model" to_port="out 7"/>
          <connect from_op="De-Normalize" from_port="model output" to_op="Apply Model" to_port="model"/>
          <connect from_op="Clustering" from_port="cluster model" to_op="Performance" to_port="cluster model"/>
          <connect from_op="Clustering" from_port="clustered set" to_op="Performance" to_port="example set"/>
          <connect from_op="Performance" from_port="performance" to_port="out 1"/>
          <connect from_op="Performance" from_port="example set" to_op="Apply Model" to_port="unlabelled data"/>
          <connect from_op="Performance" from_port="cluster model" to_port="out 2"/>
          <connect from_op="Apply Model" from_port="labelled data" to_op="Aggregate" to_port="example set input"/>
          <connect from_op="Apply Model" from_port="model" to_port="out 3"/>
          <connect from_op="Aggregate" from_port="example set output" to_port="out 4"/>
          <connect from_op="Aggregate" from_port="original" to_port="out 5"/>
          <portSpacing port="source_in 1" spacing="0"/>
          <portSpacing port="source_in 2" spacing="0"/>
          <portSpacing port="source_in 3" spacing="0"/>
          <portSpacing port="sink_out 1" spacing="0"/>
          <portSpacing port="sink_out 2" spacing="0"/>
          <portSpacing port="sink_out 3" spacing="0"/>
          <portSpacing port="sink_out 4" spacing="0"/>
          <portSpacing port="sink_out 5" spacing="0"/>
          <portSpacing port="sink_out 6" spacing="0"/>
          <portSpacing port="sink_out 7" spacing="0"/>
          <portSpacing port="sink_out 8" spacing="0"/>
        </process>
      </operator>
      <operator activated="true" class="write_excel" compatibility="9.10.010" expanded="true" height="103" name="Write Excel" width="90" x="849" y="391">
        <parameter key="excel_file" value="C:/Users/<USER>/Downloads/DESD Case Study/Profiling with Base Model with 5 Clusters.xlsx"/>
        <parameter key="file_format" value="xlsx"/>
        <enumeration key="sheet_names"/>
        <parameter key="sheet_name" value="RapidMiner Data"/>
        <parameter key="date_format" value="dd-MM-yyyy"/>
        <parameter key="number_format" value="#.0"/>
        <parameter key="encoding" value="SYSTEM"/>
      </operator>
      <connect from_op="Retrieve" from_port="output" to_op="Pre-Processing" to_port="in 1"/>
      <connect from_op="Pre-Processing" from_port="out 1" to_op="Multiply" to_port="input"/>
      <connect from_op="Multiply" from_port="output 1" to_op="Select Attributes" to_port="example set input"/>
      <connect from_op="Multiply" from_port="output 2" to_op="Modelling" to_port="in 2"/>
      <connect from_op="Select Attributes" from_port="example set output" to_op="Modelling" to_port="in 1"/>
      <connect from_op="Modelling" from_port="out 1" to_port="result 2"/>
      <connect from_op="Modelling" from_port="out 2" to_port="result 3"/>
      <connect from_op="Modelling" from_port="out 3" to_port="result 1"/>
      <connect from_op="Modelling" from_port="out 5" to_op="Write Excel" to_port="input"/>
      <portSpacing port="source_input 1" spacing="0"/>
      <portSpacing port="sink_result 1" spacing="0"/>
      <portSpacing port="sink_result 2" spacing="0"/>
      <portSpacing port="sink_result 3" spacing="0"/>
      <portSpacing port="sink_result 4" spacing="0"/>
    </process>
  </operator>
</process>
