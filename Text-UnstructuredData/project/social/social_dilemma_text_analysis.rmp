<?xml version="1.0" encoding="UTF-8"?><process version="9.10.011">
  <context>
    <input/>
    <output/>
    <macros/>
  </context>
  <operator activated="true" class="process" compatibility="9.10.011" expanded="true" name="Process">
    <parameter key="logverbosity" value="init"/>
    <parameter key="random_seed" value="2001"/>
    <parameter key="send_mail" value="never"/>
    <parameter key="notification_email" value=""/>
    <parameter key="process_duration_for_mail" value="30"/>
    <parameter key="encoding" value="SYSTEM"/>
    <process expanded="true">
      <operator activated="true" class="retrieve" compatibility="9.10.011" expanded="true" height="68" name="Retrieve TheSocialDilemma" width="90" x="45" y="34">
        <parameter key="repository_entry" value="social_dilemma_updated_final"/>
      </operator>
      <operator activated="true" class="select_attributes" compatibility="9.10.011" expanded="true" height="82" name="Select Attributes" width="90" x="179" y="34">
        <parameter key="attribute_filter_type" value="subset"/>
        <parameter key="attribute" value="text"/>
        <parameter key="attributes" value="text|user_name|user_location"/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
      </operator>
      <operator activated="true" class="nominal_to_text" compatibility="9.10.011" expanded="true" height="82" name="Nominal to Text" width="90" x="179" y="136">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="text"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="nominal"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="file_path"/>
        <parameter key="block_type" value="single_value"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="single_value"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
      </operator>
      <operator activated="true" class="subprocess" compatibility="9.10.011" expanded="true" height="82" name="Subprocess" width="90" x="313" y="34">
        <process expanded="true">
          <operator activated="true" class="replace" compatibility="9.10.011" expanded="true" height="82" name="Replace Number" width="90" x="45" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="text"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <parameter key="replace_what" value="\d+"/>
          </operator>
          <operator activated="true" class="replace" compatibility="9.10.011" expanded="true" height="82" name="Replace URL" width="90" x="179" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="text"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <parameter key="replace_what" value="http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&amp;+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+"/>
          </operator>
          <operator activated="true" class="replace" compatibility="9.10.011" expanded="true" height="82" name="Replace Mention" width="90" x="313" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="text"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <parameter key="replace_what" value="@\w+"/>
          </operator>
          <operator activated="true" class="replace" compatibility="9.10.011" expanded="true" height="82" name="Replace Special Characters" width="90" x="447" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="text"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="false"/>
            <parameter key="replace_what" value="[@_!#$%^&amp;*()&lt;&gt;?/\|ðŸ˜³}{~:â€™—£—£�œ¦&quot;'“»’‰†‘º«Š†Ž¥®“±žÂ¿š¡ï¸]"/>
          </operator>
          <connect from_port="in 1" to_op="Replace Number" to_port="example set input"/>
          <connect from_op="Replace Number" from_port="example set output" to_op="Replace URL" to_port="example set input"/>
          <connect from_op="Replace URL" from_port="example set output" to_op="Replace Mention" to_port="example set input"/>
          <connect from_op="Replace Mention" from_port="example set output" to_op="Replace Special Characters" to_port="example set input"/>
          <connect from_op="Replace Special Characters" from_port="example set output" to_port="out 1"/>
          <portSpacing port="source_in 1" spacing="0"/>
          <portSpacing port="source_in 2" spacing="0"/>
          <portSpacing port="sink_out 1" spacing="0"/>
          <portSpacing port="sink_out 2" spacing="0"/>
        </process>
      </operator>
      <operator activated="true" class="remove_duplicates" compatibility="9.10.011" expanded="true" height="103" name="Remove Duplicates" width="90" x="447" y="34">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="text"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="treat_missing_values_as_duplicates" value="false"/>
      </operator>
      <operator activated="true" class="declare_missing_value" compatibility="9.10.011" expanded="true" height="82" name="Declare Missing Value" width="90" x="447" y="187">
        <parameter key="attribute_filter_type" value="all"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="true"/>
        <parameter key="mode" value="nominal"/>
        <parameter key="nominal_value" value="?"/>
        <parameter key="expression_value" value=""/>
      </operator>
      <operator activated="true" class="filter_examples" compatibility="9.10.011" expanded="true" height="103" name="Filter Examples" width="90" x="447" y="340">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="no_missing_attributes"/>
        <parameter key="invert_filter" value="false"/>
        <list key="filters_list"/>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="operator_toolbox:extract_sentiment" compatibility="2.14.000" expanded="true" height="103" name="Extract Sentiment" width="90" x="648" y="34">
        <parameter key="model" value="vader"/>
        <parameter key="text_attribute" value="text"/>
        <parameter key="show_advanced_output" value="true"/>
        <parameter key="use_default_tokenization_regex" value="true"/>
        <list key="additional_words"/>
      </operator>
      <operator activated="true" class="generate_attributes" compatibility="9.10.011" expanded="true" height="82" name="Generate Attributes" width="90" x="849" y="34">
        <list key="function_descriptions">
          <parameter key="Sentiment" value="if(Score&gt;0,&#10;   &quot;Positive&quot;,&#10;   if(Score==0,&#10;    &quot;Neutral&quot;,&#10;    &quot;Negative&quot;&#10;    )&#10;    )"/>
        </list>
        <parameter key="keep_all" value="true"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.011" expanded="true" height="124" name="Multiply (Sentiment Analysis with score_all tweets)" width="90" x="849" y="136"/>
      <operator activated="true" class="filter_examples" compatibility="9.10.011" expanded="true" height="103" name="Filter Examples (Neutral tweets)" width="90" x="1318" y="442">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="custom_filters"/>
        <parameter key="invert_filter" value="false"/>
        <list key="filters_list">
          <parameter key="filters_entry_key" value="Sentiment.equals.Neutral"/>
        </list>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="operator_toolbox:merge" compatibility="2.14.000" expanded="true" height="103" name="Merge Attributes" width="90" x="1050" y="187">
        <parameter key="handling_of_duplicate_attributes" value="keep_only_first"/>
        <parameter key="handling_of_special_attributes" value="keep_first_special_other_regular"/>
        <parameter key="handling_of_duplicate_annotations" value="keep_only_first"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.011" expanded="true" height="103" name="Multiply" width="90" x="1184" y="187"/>
      <operator activated="true" class="filter_examples" compatibility="9.10.011" expanded="true" height="103" name="Filter Examples (Negative)" width="90" x="1318" y="289">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="custom_filters"/>
        <parameter key="invert_filter" value="false"/>
        <list key="filters_list">
          <parameter key="filters_entry_key" value="Sentiment.equals.Negative"/>
        </list>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="subprocess" compatibility="9.10.011" expanded="true" height="145" name="Subprocess (negative tweet)" width="90" x="1452" y="340">
        <process expanded="true">
          <operator activated="true" class="blending:sort" compatibility="9.10.011" expanded="true" height="82" name="Sort (2)" width="90" x="45" y="34">
            <list key="sort_by">
              <parameter key="Score" value="ascending"/>
            </list>
          </operator>
          <operator activated="true" class="filter_example_range" compatibility="9.10.011" expanded="true" height="82" name="Filter Example Range (2)" width="90" x="45" y="187">
            <parameter key="first_example" value="1"/>
            <parameter key="last_example" value="100"/>
            <parameter key="invert_filter" value="false"/>
          </operator>
          <operator activated="true" class="select_attributes" compatibility="9.10.011" expanded="true" height="82" name="Select Attributes (3)" width="90" x="45" y="340">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="attribute_value"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="time"/>
            <parameter key="block_type" value="attribute_block"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="value_matrix_row_start"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
          </operator>
          <operator activated="true" class="replace" compatibility="9.10.011" expanded="true" height="82" name="Replace (2)" width="90" x="179" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
            <parameter key="replace_what" value="[^A-Za-z\s]+"/>
          </operator>
          <operator activated="true" class="trim" compatibility="9.10.011" expanded="true" height="82" name="Trim (2)" width="90" x="313" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
          </operator>
          <operator activated="true" class="replace" compatibility="9.10.011" expanded="true" height="82" name="Replace (neg)" width="90" x="313" y="136">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
            <parameter key="replace_what" value="[\s]+"/>
            <parameter key="replace_by" value=" "/>
          </operator>
          <operator activated="true" class="nominal_to_text" compatibility="9.10.011" expanded="true" height="82" name="Nominal to Text (neg)" width="90" x="313" y="238">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
          </operator>
          <operator activated="true" class="multiply" compatibility="9.10.011" expanded="true" height="103" name="Multiply (4)" width="90" x="313" y="340"/>
          <operator activated="true" class="text:process_document_from_data" compatibility="9.4.000" expanded="true" height="82" name="Process Documents from Data (neg)" width="90" x="514" y="238">
            <parameter key="create_word_vector" value="true"/>
            <parameter key="vector_creation" value="Term Frequency"/>
            <parameter key="add_meta_information" value="true"/>
            <parameter key="keep_text" value="true"/>
            <parameter key="prune_method" value="absolute"/>
            <parameter key="prune_below_percent" value="3.0"/>
            <parameter key="prune_above_percent" value="30.0"/>
            <parameter key="prune_below_absolute" value="1"/>
            <parameter key="prune_above_absolute" value="3"/>
            <parameter key="prune_below_rank" value="0.05"/>
            <parameter key="prune_above_rank" value="0.95"/>
            <parameter key="datamanagement" value="double_sparse_array"/>
            <parameter key="data_management" value="auto"/>
            <parameter key="select_attributes_and_weights" value="true"/>
            <list key="specify_weights">
              <parameter key="Scoring String" value="1.0"/>
            </list>
            <process expanded="true">
              <operator activated="true" class="text:tokenize" compatibility="9.4.000" expanded="true" height="68" name="Tokenize (2)" width="90" x="45" y="34">
                <parameter key="mode" value="non letters"/>
                <parameter key="characters" value=".:"/>
                <parameter key="language" value="English"/>
                <parameter key="max_token_length" value="3"/>
              </operator>
              <operator activated="true" class="text:transform_cases" compatibility="9.4.000" expanded="true" height="68" name="Transform Cases (2)" width="90" x="179" y="34">
                <parameter key="transform_to" value="lower case"/>
              </operator>
              <operator activated="true" class="text:filter_stopwords_english" compatibility="9.4.000" expanded="true" height="68" name="Filter Stopwords (English) (2)" width="90" x="313" y="34"/>
              <operator activated="true" class="text:filter_by_length" compatibility="9.4.000" expanded="true" height="68" name="Filter Tokens (by Length) (2)" width="90" x="447" y="34">
                <parameter key="min_chars" value="3"/>
                <parameter key="max_chars" value="15"/>
              </operator>
              <connect from_port="document" to_op="Tokenize (2)" to_port="document"/>
              <connect from_op="Tokenize (2)" from_port="document" to_op="Transform Cases (2)" to_port="document"/>
              <connect from_op="Transform Cases (2)" from_port="document" to_op="Filter Stopwords (English) (2)" to_port="document"/>
              <connect from_op="Filter Stopwords (English) (2)" from_port="document" to_op="Filter Tokens (by Length) (2)" to_port="document"/>
              <connect from_op="Filter Tokens (by Length) (2)" from_port="document" to_port="document 1"/>
              <portSpacing port="source_document" spacing="0"/>
              <portSpacing port="sink_document 1" spacing="0"/>
              <portSpacing port="sink_document 2" spacing="0"/>
            </process>
          </operator>
          <operator activated="true" class="text:wordlist_to_data" compatibility="9.4.000" expanded="true" height="82" name="WordList to Data (neg)" width="90" x="648" y="187"/>
          <operator activated="true" class="blending:sort" compatibility="9.10.011" expanded="true" height="82" name="Sort (neg)" width="90" x="648" y="391">
            <list key="sort_by">
              <parameter key="total" value="descending"/>
            </list>
          </operator>
          <operator activated="true" class="text:process_document_from_data" compatibility="9.4.000" expanded="true" height="82" name="Process Documents from Data(pos) (3)" width="90" x="447" y="493">
            <parameter key="create_word_vector" value="true"/>
            <parameter key="vector_creation" value="Term Frequency"/>
            <parameter key="add_meta_information" value="true"/>
            <parameter key="keep_text" value="true"/>
            <parameter key="prune_method" value="absolute"/>
            <parameter key="prune_below_percent" value="3.0"/>
            <parameter key="prune_above_percent" value="30.0"/>
            <parameter key="prune_below_absolute" value="1"/>
            <parameter key="prune_above_absolute" value="3"/>
            <parameter key="prune_below_rank" value="0.05"/>
            <parameter key="prune_above_rank" value="0.95"/>
            <parameter key="datamanagement" value="double_sparse_array"/>
            <parameter key="data_management" value="auto"/>
            <parameter key="select_attributes_and_weights" value="true"/>
            <list key="specify_weights">
              <parameter key="Scoring String" value="1.0"/>
            </list>
            <process expanded="true">
              <operator activated="true" class="text:transform_cases" compatibility="9.4.000" expanded="true" height="68" name="Transform Cases (4)" width="90" x="112" y="34">
                <parameter key="transform_to" value="lower case"/>
              </operator>
              <operator activated="true" class="text:filter_stopwords_english" compatibility="9.4.000" expanded="true" height="68" name="Filter Stopwords (English) (4)" width="90" x="246" y="34"/>
              <operator activated="true" class="text:filter_by_length" compatibility="9.4.000" expanded="true" height="68" name="Filter Tokens (by Length) (3)" width="90" x="380" y="34">
                <parameter key="min_chars" value="4"/>
                <parameter key="max_chars" value="20"/>
              </operator>
              <operator activated="true" class="text:generate_n_grams_terms" compatibility="9.4.000" expanded="true" height="68" name="Generate n-Grams (Terms) (2)" width="90" x="514" y="34">
                <parameter key="max_length" value="2"/>
              </operator>
              <connect from_port="document" to_op="Transform Cases (4)" to_port="document"/>
              <connect from_op="Transform Cases (4)" from_port="document" to_op="Filter Stopwords (English) (4)" to_port="document"/>
              <connect from_op="Filter Stopwords (English) (4)" from_port="document" to_op="Filter Tokens (by Length) (3)" to_port="document"/>
              <connect from_op="Filter Tokens (by Length) (3)" from_port="document" to_op="Generate n-Grams (Terms) (2)" to_port="document"/>
              <connect from_op="Generate n-Grams (Terms) (2)" from_port="document" to_port="document 1"/>
              <portSpacing port="source_document" spacing="0"/>
              <portSpacing port="sink_document 1" spacing="0"/>
              <portSpacing port="sink_document 2" spacing="0"/>
            </process>
          </operator>
          <operator activated="true" class="text:wordlist_to_data" compatibility="9.4.000" expanded="true" height="82" name="WordList to Data(pos) (3)" width="90" x="581" y="493"/>
          <operator activated="true" class="blending:sort" compatibility="9.10.011" expanded="true" height="82" name="Sort (pos) (3)" width="90" x="581" y="595">
            <list key="sort_by">
              <parameter key="total" value="descending"/>
            </list>
          </operator>
          <operator activated="true" class="filter_example_range" compatibility="9.10.011" expanded="true" height="82" name="Filter Example Range (word_freq:neg_tweets)" width="90" x="849" y="493">
            <parameter key="first_example" value="1"/>
            <parameter key="last_example" value="20"/>
            <parameter key="invert_filter" value="false"/>
          </operator>
          <connect from_port="in 1" to_op="Sort (2)" to_port="example set input"/>
          <connect from_op="Sort (2)" from_port="example set output" to_op="Filter Example Range (2)" to_port="example set input"/>
          <connect from_op="Filter Example Range (2)" from_port="example set output" to_op="Select Attributes (3)" to_port="example set input"/>
          <connect from_op="Select Attributes (3)" from_port="example set output" to_op="Replace (2)" to_port="example set input"/>
          <connect from_op="Replace (2)" from_port="example set output" to_op="Trim (2)" to_port="example set input"/>
          <connect from_op="Trim (2)" from_port="example set output" to_op="Replace (neg)" to_port="example set input"/>
          <connect from_op="Replace (neg)" from_port="example set output" to_op="Nominal to Text (neg)" to_port="example set input"/>
          <connect from_op="Nominal to Text (neg)" from_port="example set output" to_op="Multiply (4)" to_port="input"/>
          <connect from_op="Multiply (4)" from_port="output 1" to_op="Process Documents from Data (neg)" to_port="example set"/>
          <connect from_op="Multiply (4)" from_port="output 2" to_op="Process Documents from Data(pos) (3)" to_port="example set"/>
          <connect from_op="Process Documents from Data (neg)" from_port="word list" to_op="WordList to Data (neg)" to_port="word list"/>
          <connect from_op="WordList to Data (neg)" from_port="word list" to_port="out 2"/>
          <connect from_op="WordList to Data (neg)" from_port="example set" to_op="Sort (neg)" to_port="example set input"/>
          <connect from_op="Sort (neg)" from_port="example set output" to_port="out 3"/>
          <connect from_op="Process Documents from Data(pos) (3)" from_port="word list" to_op="WordList to Data(pos) (3)" to_port="word list"/>
          <connect from_op="WordList to Data(pos) (3)" from_port="example set" to_op="Sort (pos) (3)" to_port="example set input"/>
          <connect from_op="Sort (pos) (3)" from_port="example set output" to_op="Filter Example Range (word_freq:neg_tweets)" to_port="example set input"/>
          <connect from_op="Filter Example Range (word_freq:neg_tweets)" from_port="example set output" to_port="out 4"/>
          <portSpacing port="source_in 1" spacing="0"/>
          <portSpacing port="source_in 2" spacing="0"/>
          <portSpacing port="sink_out 1" spacing="0"/>
          <portSpacing port="sink_out 2" spacing="0"/>
          <portSpacing port="sink_out 3" spacing="0"/>
          <portSpacing port="sink_out 4" spacing="0"/>
          <portSpacing port="sink_out 5" spacing="0"/>
        </process>
      </operator>
      <operator activated="true" class="filter_examples" compatibility="9.10.011" expanded="true" height="103" name="Filter Examples (Positive)" width="90" x="1318" y="136">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="custom_filters"/>
        <parameter key="invert_filter" value="false"/>
        <list key="filters_list">
          <parameter key="filters_entry_key" value="Sentiment.equals.Positive"/>
        </list>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="subprocess" compatibility="9.10.011" expanded="true" height="145" name="Subprocess (positive tweet)" width="90" x="1452" y="85">
        <process expanded="true">
          <operator activated="true" class="blending:sort" compatibility="9.10.011" expanded="true" height="82" name="Sort" width="90" x="45" y="34">
            <list key="sort_by">
              <parameter key="Score" value="descending"/>
            </list>
          </operator>
          <operator activated="true" class="filter_example_range" compatibility="9.10.011" expanded="true" height="82" name="Filter Example Range" width="90" x="45" y="187">
            <parameter key="first_example" value="1"/>
            <parameter key="last_example" value="100"/>
            <parameter key="invert_filter" value="false"/>
          </operator>
          <operator activated="true" class="select_attributes" compatibility="9.10.011" expanded="true" height="82" name="Select Attributes (2)" width="90" x="45" y="340">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="attribute_value"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="time"/>
            <parameter key="block_type" value="attribute_block"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="value_matrix_row_start"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
          </operator>
          <operator activated="true" class="replace" compatibility="9.10.011" expanded="true" height="82" name="Replace" width="90" x="179" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
            <parameter key="replace_what" value="[^A-Za-z\s]+"/>
          </operator>
          <operator activated="true" class="trim" compatibility="9.10.011" expanded="true" height="82" name="Trim" width="90" x="313" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
          </operator>
          <operator activated="true" class="replace" compatibility="9.10.011" expanded="true" height="82" name="Replace (pos)" width="90" x="313" y="136">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
            <parameter key="replace_what" value="[\s]+"/>
            <parameter key="replace_by" value=" "/>
          </operator>
          <operator activated="true" class="nominal_to_text" compatibility="9.10.011" expanded="true" height="82" name="Nominal to Text (pos)" width="90" x="313" y="238">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="Scoring String"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
          </operator>
          <operator activated="true" class="multiply" compatibility="9.10.011" expanded="true" height="103" name="Multiply (2)" width="90" x="313" y="340"/>
          <operator activated="true" class="text:process_document_from_data" compatibility="9.4.000" expanded="true" height="82" name="Process Documents from Data(pos) (2)" width="90" x="447" y="493">
            <parameter key="create_word_vector" value="true"/>
            <parameter key="vector_creation" value="Term Occurrences"/>
            <parameter key="add_meta_information" value="true"/>
            <parameter key="keep_text" value="true"/>
            <parameter key="prune_method" value="absolute"/>
            <parameter key="prune_below_percent" value="3.0"/>
            <parameter key="prune_above_percent" value="30.0"/>
            <parameter key="prune_below_absolute" value="1"/>
            <parameter key="prune_above_absolute" value="3"/>
            <parameter key="prune_below_rank" value="0.05"/>
            <parameter key="prune_above_rank" value="0.95"/>
            <parameter key="datamanagement" value="double_sparse_array"/>
            <parameter key="data_management" value="auto"/>
            <parameter key="select_attributes_and_weights" value="true"/>
            <list key="specify_weights">
              <parameter key="Scoring String" value="1.0"/>
            </list>
            <process expanded="true">
              <operator activated="true" class="text:transform_cases" compatibility="9.4.000" expanded="true" height="68" name="Transform Cases (3)" width="90" x="179" y="34">
                <parameter key="transform_to" value="lower case"/>
              </operator>
              <operator activated="true" class="text:filter_stopwords_english" compatibility="9.4.000" expanded="true" height="68" name="Filter Stopwords (English) (3)" width="90" x="313" y="34"/>
              <operator activated="true" class="text:filter_by_length" compatibility="9.4.000" expanded="true" height="68" name="Filter Tokens (by Length) (4)" width="90" x="447" y="34">
                <parameter key="min_chars" value="4"/>
                <parameter key="max_chars" value="15"/>
              </operator>
              <operator activated="true" class="text:generate_n_grams_terms" compatibility="9.4.000" expanded="true" height="68" name="Generate n-Grams (Terms)" width="90" x="581" y="34">
                <parameter key="max_length" value="2"/>
              </operator>
              <connect from_port="document" to_op="Transform Cases (3)" to_port="document"/>
              <connect from_op="Transform Cases (3)" from_port="document" to_op="Filter Stopwords (English) (3)" to_port="document"/>
              <connect from_op="Filter Stopwords (English) (3)" from_port="document" to_op="Filter Tokens (by Length) (4)" to_port="document"/>
              <connect from_op="Filter Tokens (by Length) (4)" from_port="document" to_op="Generate n-Grams (Terms)" to_port="document"/>
              <connect from_op="Generate n-Grams (Terms)" from_port="document" to_port="document 1"/>
              <portSpacing port="source_document" spacing="0"/>
              <portSpacing port="sink_document 1" spacing="0"/>
              <portSpacing port="sink_document 2" spacing="0"/>
            </process>
          </operator>
          <operator activated="true" class="text:wordlist_to_data" compatibility="9.4.000" expanded="true" height="82" name="WordList to Data(pos) (2)" width="90" x="581" y="493"/>
          <operator activated="true" class="blending:sort" compatibility="9.10.011" expanded="true" height="82" name="Sort (pos) (2)" width="90" x="581" y="595">
            <list key="sort_by">
              <parameter key="total" value="descending"/>
            </list>
          </operator>
          <operator activated="true" class="filter_example_range" compatibility="9.10.011" expanded="true" height="82" name="Filter Example Range (word_freq:pos_tweets)" width="90" x="715" y="544">
            <parameter key="first_example" value="1"/>
            <parameter key="last_example" value="20"/>
            <parameter key="invert_filter" value="false"/>
          </operator>
          <operator activated="true" class="text:process_document_from_data" compatibility="9.4.000" expanded="true" height="82" name="Process Documents from Data(pos)" width="90" x="514" y="238">
            <parameter key="create_word_vector" value="true"/>
            <parameter key="vector_creation" value="Term Frequency"/>
            <parameter key="add_meta_information" value="true"/>
            <parameter key="keep_text" value="true"/>
            <parameter key="prune_method" value="absolute"/>
            <parameter key="prune_below_percent" value="3.0"/>
            <parameter key="prune_above_percent" value="30.0"/>
            <parameter key="prune_below_absolute" value="1"/>
            <parameter key="prune_above_absolute" value="3"/>
            <parameter key="prune_below_rank" value="0.05"/>
            <parameter key="prune_above_rank" value="0.95"/>
            <parameter key="datamanagement" value="double_sparse_array"/>
            <parameter key="data_management" value="auto"/>
            <parameter key="select_attributes_and_weights" value="true"/>
            <list key="specify_weights">
              <parameter key="Scoring String" value="1.0"/>
            </list>
            <process expanded="true">
              <operator activated="true" class="text:tokenize" compatibility="9.4.000" expanded="true" height="68" name="Tokenize" width="90" x="45" y="34">
                <parameter key="mode" value="non letters"/>
                <parameter key="characters" value=".:"/>
                <parameter key="language" value="English"/>
                <parameter key="max_token_length" value="3"/>
              </operator>
              <operator activated="true" class="text:transform_cases" compatibility="9.4.000" expanded="true" height="68" name="Transform Cases" width="90" x="179" y="34">
                <parameter key="transform_to" value="lower case"/>
              </operator>
              <operator activated="true" class="text:filter_stopwords_english" compatibility="9.4.000" expanded="true" height="68" name="Filter Stopwords (English)" width="90" x="313" y="34"/>
              <operator activated="true" class="text:filter_by_length" compatibility="9.4.000" expanded="true" height="68" name="Filter Tokens (by Length)" width="90" x="447" y="34">
                <parameter key="min_chars" value="3"/>
                <parameter key="max_chars" value="15"/>
              </operator>
              <connect from_port="document" to_op="Tokenize" to_port="document"/>
              <connect from_op="Tokenize" from_port="document" to_op="Transform Cases" to_port="document"/>
              <connect from_op="Transform Cases" from_port="document" to_op="Filter Stopwords (English)" to_port="document"/>
              <connect from_op="Filter Stopwords (English)" from_port="document" to_op="Filter Tokens (by Length)" to_port="document"/>
              <connect from_op="Filter Tokens (by Length)" from_port="document" to_port="document 1"/>
              <portSpacing port="source_document" spacing="0"/>
              <portSpacing port="sink_document 1" spacing="0"/>
              <portSpacing port="sink_document 2" spacing="0"/>
            </process>
          </operator>
          <operator activated="true" class="text:wordlist_to_data" compatibility="9.4.000" expanded="true" height="82" name="WordList to Data(pos)" width="90" x="648" y="187"/>
          <operator activated="true" class="blending:sort" compatibility="9.10.011" expanded="true" height="82" name="Sort (pos)" width="90" x="648" y="391">
            <list key="sort_by">
              <parameter key="total" value="descending"/>
            </list>
          </operator>
          <connect from_port="in 1" to_op="Sort" to_port="example set input"/>
          <connect from_op="Sort" from_port="example set output" to_op="Filter Example Range" to_port="example set input"/>
          <connect from_op="Filter Example Range" from_port="example set output" to_op="Select Attributes (2)" to_port="example set input"/>
          <connect from_op="Select Attributes (2)" from_port="example set output" to_op="Replace" to_port="example set input"/>
          <connect from_op="Replace" from_port="example set output" to_op="Trim" to_port="example set input"/>
          <connect from_op="Trim" from_port="example set output" to_op="Replace (pos)" to_port="example set input"/>
          <connect from_op="Replace (pos)" from_port="example set output" to_op="Nominal to Text (pos)" to_port="example set input"/>
          <connect from_op="Nominal to Text (pos)" from_port="example set output" to_op="Multiply (2)" to_port="input"/>
          <connect from_op="Multiply (2)" from_port="output 1" to_op="Process Documents from Data(pos)" to_port="example set"/>
          <connect from_op="Multiply (2)" from_port="output 2" to_op="Process Documents from Data(pos) (2)" to_port="example set"/>
          <connect from_op="Process Documents from Data(pos) (2)" from_port="word list" to_op="WordList to Data(pos) (2)" to_port="word list"/>
          <connect from_op="WordList to Data(pos) (2)" from_port="example set" to_op="Sort (pos) (2)" to_port="example set input"/>
          <connect from_op="Sort (pos) (2)" from_port="example set output" to_op="Filter Example Range (word_freq:pos_tweets)" to_port="example set input"/>
          <connect from_op="Filter Example Range (word_freq:pos_tweets)" from_port="example set output" to_port="out 4"/>
          <connect from_op="Process Documents from Data(pos)" from_port="word list" to_op="WordList to Data(pos)" to_port="word list"/>
          <connect from_op="WordList to Data(pos)" from_port="word list" to_port="out 2"/>
          <connect from_op="WordList to Data(pos)" from_port="example set" to_op="Sort (pos)" to_port="example set input"/>
          <connect from_op="Sort (pos)" from_port="example set output" to_port="out 3"/>
          <portSpacing port="source_in 1" spacing="0"/>
          <portSpacing port="source_in 2" spacing="0"/>
          <portSpacing port="sink_out 1" spacing="0"/>
          <portSpacing port="sink_out 2" spacing="0"/>
          <portSpacing port="sink_out 3" spacing="0"/>
          <portSpacing port="sink_out 4" spacing="0"/>
          <portSpacing port="sink_out 5" spacing="0"/>
        </process>
      </operator>
      <operator activated="true" class="subprocess" compatibility="9.10.011" expanded="true" height="82" name="Subprocess (neutral tweet)" width="90" x="1452" y="544">
        <process expanded="true">
          <operator activated="true" class="nominal_to_text" compatibility="9.10.011" expanded="true" height="82" name="Nominal to Text (pos) (2)" width="90" x="112" y="34">
            <parameter key="attribute_filter_type" value="single"/>
            <parameter key="attribute" value="text"/>
            <parameter key="attributes" value=""/>
            <parameter key="use_except_expression" value="false"/>
            <parameter key="value_type" value="nominal"/>
            <parameter key="use_value_type_exception" value="false"/>
            <parameter key="except_value_type" value="file_path"/>
            <parameter key="block_type" value="single_value"/>
            <parameter key="use_block_type_exception" value="false"/>
            <parameter key="except_block_type" value="single_value"/>
            <parameter key="invert_selection" value="false"/>
            <parameter key="include_special_attributes" value="true"/>
          </operator>
          <operator activated="true" class="text:process_document_from_data" compatibility="9.4.000" expanded="true" height="82" name="Process Documents from Data(pos) (4)" width="90" x="246" y="34">
            <parameter key="create_word_vector" value="true"/>
            <parameter key="vector_creation" value="Term Frequency"/>
            <parameter key="add_meta_information" value="true"/>
            <parameter key="keep_text" value="true"/>
            <parameter key="prune_method" value="absolute"/>
            <parameter key="prune_below_percent" value="3.0"/>
            <parameter key="prune_above_percent" value="30.0"/>
            <parameter key="prune_below_absolute" value="1"/>
            <parameter key="prune_above_absolute" value="3"/>
            <parameter key="prune_below_rank" value="0.05"/>
            <parameter key="prune_above_rank" value="0.95"/>
            <parameter key="datamanagement" value="double_sparse_array"/>
            <parameter key="data_management" value="auto"/>
            <parameter key="select_attributes_and_weights" value="true"/>
            <list key="specify_weights">
              <parameter key="Scoring String" value="1.0"/>
            </list>
            <process expanded="true">
              <operator activated="true" class="text:tokenize" compatibility="9.4.000" expanded="true" height="68" name="Tokenize (3)" width="90" x="45" y="34">
                <parameter key="mode" value="non letters"/>
                <parameter key="characters" value=".:"/>
                <parameter key="language" value="English"/>
                <parameter key="max_token_length" value="3"/>
              </operator>
              <operator activated="true" class="text:transform_cases" compatibility="9.4.000" expanded="true" height="68" name="Transform Cases (5)" width="90" x="179" y="34">
                <parameter key="transform_to" value="lower case"/>
              </operator>
              <operator activated="true" class="text:filter_stopwords_english" compatibility="9.4.000" expanded="true" height="68" name="Filter Stopwords (English) (5)" width="90" x="313" y="34"/>
              <operator activated="true" class="text:filter_by_length" compatibility="9.4.000" expanded="true" height="68" name="Filter Tokens (by Length) (5)" width="90" x="447" y="34">
                <parameter key="min_chars" value="3"/>
                <parameter key="max_chars" value="15"/>
              </operator>
              <connect from_port="document" to_op="Tokenize (3)" to_port="document"/>
              <connect from_op="Tokenize (3)" from_port="document" to_op="Transform Cases (5)" to_port="document"/>
              <connect from_op="Transform Cases (5)" from_port="document" to_op="Filter Stopwords (English) (5)" to_port="document"/>
              <connect from_op="Filter Stopwords (English) (5)" from_port="document" to_op="Filter Tokens (by Length) (5)" to_port="document"/>
              <connect from_op="Filter Tokens (by Length) (5)" from_port="document" to_port="document 1"/>
              <portSpacing port="source_document" spacing="0"/>
              <portSpacing port="sink_document 1" spacing="0"/>
              <portSpacing port="sink_document 2" spacing="0"/>
            </process>
          </operator>
          <operator activated="true" class="text:wordlist_to_data" compatibility="9.4.000" expanded="true" height="82" name="WordList to Data(word_freq:neutral tweet)" width="90" x="380" y="34"/>
          <operator activated="true" class="blending:sort" compatibility="9.10.011" expanded="true" height="82" name="Sort (word_freq: neutral tweet)" width="90" x="514" y="34">
            <list key="sort_by">
              <parameter key="total" value="descending"/>
            </list>
          </operator>
          <connect from_port="in 1" to_op="Nominal to Text (pos) (2)" to_port="example set input"/>
          <connect from_op="Nominal to Text (pos) (2)" from_port="example set output" to_op="Process Documents from Data(pos) (4)" to_port="example set"/>
          <connect from_op="Process Documents from Data(pos) (4)" from_port="word list" to_op="WordList to Data(word_freq:neutral tweet)" to_port="word list"/>
          <connect from_op="WordList to Data(word_freq:neutral tweet)" from_port="example set" to_op="Sort (word_freq: neutral tweet)" to_port="example set input"/>
          <connect from_op="Sort (word_freq: neutral tweet)" from_port="example set output" to_port="out 1"/>
          <portSpacing port="source_in 1" spacing="0"/>
          <portSpacing port="source_in 2" spacing="0"/>
          <portSpacing port="sink_out 1" spacing="0"/>
          <portSpacing port="sink_out 2" spacing="0"/>
        </process>
      </operator>
      <connect from_op="Retrieve TheSocialDilemma" from_port="output" to_op="Select Attributes" to_port="example set input"/>
      <connect from_op="Select Attributes" from_port="example set output" to_op="Nominal to Text" to_port="example set input"/>
      <connect from_op="Nominal to Text" from_port="example set output" to_op="Subprocess" to_port="in 1"/>
      <connect from_op="Subprocess" from_port="out 1" to_op="Remove Duplicates" to_port="example set input"/>
      <connect from_op="Remove Duplicates" from_port="example set output" to_op="Declare Missing Value" to_port="example set input"/>
      <connect from_op="Declare Missing Value" from_port="example set output" to_op="Filter Examples" to_port="example set input"/>
      <connect from_op="Filter Examples" from_port="example set output" to_op="Extract Sentiment" to_port="exa"/>
      <connect from_op="Extract Sentiment" from_port="exa" to_op="Generate Attributes" to_port="example set input"/>
      <connect from_op="Generate Attributes" from_port="example set output" to_op="Multiply (Sentiment Analysis with score_all tweets)" to_port="input"/>
      <connect from_op="Generate Attributes" from_port="original" to_op="Merge Attributes" to_port="example set 2"/>
      <connect from_op="Multiply (Sentiment Analysis with score_all tweets)" from_port="output 1" to_op="Merge Attributes" to_port="example set 1"/>
      <connect from_op="Multiply (Sentiment Analysis with score_all tweets)" from_port="output 2" to_port="result 1"/>
      <connect from_op="Multiply (Sentiment Analysis with score_all tweets)" from_port="output 3" to_op="Filter Examples (Neutral tweets)" to_port="example set input"/>
      <connect from_op="Filter Examples (Neutral tweets)" from_port="example set output" to_op="Subprocess (neutral tweet)" to_port="in 1"/>
      <connect from_op="Merge Attributes" from_port="merged set" to_op="Multiply" to_port="input"/>
      <connect from_op="Multiply" from_port="output 1" to_op="Filter Examples (Positive)" to_port="example set input"/>
      <connect from_op="Multiply" from_port="output 2" to_op="Filter Examples (Negative)" to_port="example set input"/>
      <connect from_op="Filter Examples (Negative)" from_port="example set output" to_op="Subprocess (negative tweet)" to_port="in 1"/>
      <connect from_op="Subprocess (negative tweet)" from_port="out 1" to_port="result 5"/>
      <connect from_op="Subprocess (negative tweet)" from_port="out 2" to_port="result 6"/>
      <connect from_op="Subprocess (negative tweet)" from_port="out 3" to_port="result 7"/>
      <connect from_op="Subprocess (negative tweet)" from_port="out 4" to_port="result 9"/>
      <connect from_op="Filter Examples (Positive)" from_port="example set output" to_op="Subprocess (positive tweet)" to_port="in 1"/>
      <connect from_op="Subprocess (positive tweet)" from_port="out 1" to_port="result 2"/>
      <connect from_op="Subprocess (positive tweet)" from_port="out 2" to_port="result 3"/>
      <connect from_op="Subprocess (positive tweet)" from_port="out 3" to_port="result 4"/>
      <connect from_op="Subprocess (positive tweet)" from_port="out 4" to_port="result 8"/>
      <connect from_op="Subprocess (neutral tweet)" from_port="out 1" to_port="result 10"/>
      <portSpacing port="source_input 1" spacing="0"/>
      <portSpacing port="sink_result 1" spacing="0"/>
      <portSpacing port="sink_result 2" spacing="0"/>
      <portSpacing port="sink_result 3" spacing="0"/>
      <portSpacing port="sink_result 4" spacing="0"/>
      <portSpacing port="sink_result 5" spacing="0"/>
      <portSpacing port="sink_result 6" spacing="0"/>
      <portSpacing port="sink_result 7" spacing="0"/>
      <portSpacing port="sink_result 8" spacing="0"/>
      <portSpacing port="sink_result 9" spacing="0"/>
      <portSpacing port="sink_result 10" spacing="0"/>
      <portSpacing port="sink_result 11" spacing="0"/>
    </process>
  </operator>
</process>
