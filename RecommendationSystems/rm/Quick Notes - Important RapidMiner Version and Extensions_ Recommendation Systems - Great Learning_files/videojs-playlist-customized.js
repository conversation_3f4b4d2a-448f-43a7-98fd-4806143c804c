/**
 * Minified by jsDelivr using Terser v3.14.1.
 * Original file: /npm/videojs-playlist@4.3.1/dist/videojs-playlist.cjs.js
 * 
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
"use strict";function _interopDefault(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var videojs=window.videojs /* GL: since videojs is loaded prior */,validSeconds=function(e){return"number"==typeof e&&!isNaN(e)&&e>=0&&e<1/0},reset=function(e){var r=e.playlist.autoadvance_;r.timeout&&e.clearTimeout(r.timeout),r.trigger&&e.off("ended",r.trigger),r.timeout=null,r.trigger=null},setup=function e(r,t){reset(r),validSeconds(t)?(r.playlist.autoadvance_.delay=t,r.playlist.autoadvance_.trigger=function(){var n=function(){return e(r,t)};r.one("play",n),r.playlist.autoadvance_.timeout=r.setTimeout(function(){reset(r),r.off("play",n),r.playlist.next()},1e3*t)},r.one("ended",r.playlist.autoadvance_.trigger)):r.playlist.autoadvance_.delay=null},clearTracks=function(e){for(var r=e.remoteTextTracks(),t=r&&r.length||0;t--;)e.removeRemoteTextTrack(r[t])},playItem=function(e,r){var t=!e.paused()||e.ended();return e.trigger("beforeplaylistitem",r.originalValue||r),r.playlistItemId_&&(e.playlist.currentPlaylistItemId_=r.playlistItemId_),e.poster(r.poster||""),e.src(r.sources),clearTracks(e),e.ready(function(){if((r.textTracks||[]).forEach(e.addRemoteTextTrack.bind(e)),e.trigger("playlistitem",r.originalValue||r),t){var n=e.play();void 0!==n&&"function"==typeof n.then&&n.then(null,function(e){})}setup(e,e.playlist.autoadvance_.delay)}),e},isItemObject=function(e){return!!e&&"object"==typeof e},transformPrimitiveItems=function(e){var r,t=[];return e.forEach(function(e){isItemObject(e)?r=e:(r=Object(e)).originalValue=e,t.push(r)}),t},generatePlaylistItemId=function(e){var r=1;e.forEach(function(e){e.playlistItemId_=r++})},indexInPlaylistItemIds=function(e,r){for(var t=0;t<e.length;t++)if(e[t].playlistItemId_===r)return t;return-1},sourceEquals=function(e,r){var t=e,n=r;return"object"==typeof e&&(t=e.src),"object"==typeof r&&(n=r.src),/^\/\//.test(t)&&(n=n.slice(n.indexOf("//"))),/^\/\//.test(n)&&(t=t.slice(t.indexOf("//"))),t===n},indexInSources=function(e,r){for(var t=0;t<e.length;t++){var n=e[t].sources;if(Array.isArray(n))for(var i=0;i<n.length;i++){var a=n[i];if(a&&sourceEquals(a,r))return t}}return-1},randomize=function(e){for(var r=-1,t=e.length-1;++r<e.length;){var n=r+Math.floor(Math.random()*(t-r+1)),i=e[n];e[n]=e[r],e[r]=i}return e};function factory(e,r,t){void 0===t&&(t=0);var n=null,i=!1,a=e.playlist=function(r,t){if(void 0===t&&(t=0),i)throw new Error("do not call playlist() during a playlist change");if(Array.isArray(r)){var u=Array.isArray(n)?n.slice():null,l=r.slice();(n=l.slice()).filter(function(e){return isItemObject(e)}).length!==n.length&&(n=transformPrimitiveItems(n)),generatePlaylistItemId(n),i=!0,e.trigger({type:"duringplaylistchange",nextIndex:t,nextPlaylist:l,previousIndex:a.currentIndex_,previousPlaylist:u||[]}),i=!1,-1!==t&&a.currentItem(t),u&&e.setTimeout(function(){e.trigger("playlistchange")},0)}return n.map(function(e){return e.originalValue||e}).slice()};return e.on("loadstart",function(){-1===a.currentItem()&&reset(e)}),a.currentIndex_=-1,a.player_=e,a.autoadvance_={},a.repeat_=!1,a.currentPlaylistItemId_=null,a.currentItem=function(e){if(i)return a.currentIndex_;if("number"==typeof e&&a.currentIndex_!==e&&e>=0&&e<n.length)return a.currentIndex_=e,playItem(a.player_,n[a.currentIndex_]),a.currentIndex_;var r=a.player_.currentSrc()||"";if(a.currentPlaylistItemId_){var t=indexInPlaylistItemIds(n,a.currentPlaylistItemId_),u=n[t];if(u&&Array.isArray(u.sources)&&indexInSources([u],r)>-1)return a.currentIndex_=t,a.currentIndex_;a.currentPlaylistItemId_=null}return a.currentIndex_=a.indexOf(r),a.currentIndex_},a.contains=function(e){return-1!==a.indexOf(e)},a.indexOf=function(e){if("string"==typeof e)return indexInSources(n,e);for(var r=Array.isArray(e)?e:e.sources,t=0;t<r.length;t++){var i=r[t];if("string"==typeof i)return indexInSources(n,i);if(i.src)return indexInSources(n,i.src)}return-1},a.currentIndex=function(){return a.currentItem()},a.lastIndex=function(){return n.length-1},a.nextIndex=function(){var e=a.currentItem();if(-1===e)return-1;var r=a.lastIndex();return a.repeat_&&e===r?0:Math.min(e+1,r)},a.previousIndex=function(){var e=a.currentItem();return-1===e?-1:a.repeat_&&0===e?a.lastIndex():Math.max(e-1,0)},a.first=function(){if(!i){var e=a.currentItem(0);if(n.length)return n[e].originalValue||n[e];a.currentIndex_=-1}},a.last=function(){if(!i){var e=a.currentItem(a.lastIndex());if(n.length)return n[e].originalValue||n[e];a.currentIndex_=-1}},a.next=function(){if(!i){var e=a.nextIndex();if(e!==a.currentIndex_){var r=a.currentItem(e);return n[r].originalValue||n[r]}}},a.previous=function(){if(!i){var e=a.previousIndex();if(e!==a.currentIndex_){var r=a.currentItem(e);return n[r].originalValue||n[r]}}},a.autoadvance=function(e){setup(a.player_,e)},a.repeat=function(e){return void 0===e?a.repeat_:"boolean"==typeof e?(a.repeat_=!!e,a.repeat_):void videojs.log.error("videojs-playlist: Invalid value for repeat",e)},a.sort=function(r){n.length&&(n.sort(r),i||e.trigger("playlistsorted"))},a.reverse=function(){n.length&&(n.reverse(),i||e.trigger("playlistsorted"))},a.shuffle=function(r){var t=(void 0===r?{}:r).rest,u=0,l=n;if(t&&(u=a.currentIndex_+1,l=n.slice(u)),!(l.length<=1)){var o;if(randomize(l),t)(o=n).splice.apply(o,[u,l.length].concat(l));i||e.trigger("playlistsorted")}},Array.isArray(r)?a(r.slice(),t):n=[],a}var version="4.3.1",registerPlugin=videojs.registerPlugin||videojs.plugin,plugin=function(e,r){factory(this,e,r)};registerPlugin("playlist",plugin),plugin.VERSION=version,module.exports=plugin;
//# sourceMappingURL=/sm/149d3e1aec4186107166504d42792dca38c0e28877f0c58f31178dd9b8455dc8.map