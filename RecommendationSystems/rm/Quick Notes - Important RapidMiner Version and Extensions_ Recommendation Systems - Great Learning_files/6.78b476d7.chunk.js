(this.webpackJsonpdemo=this.webpackJsonpdemo||[]).push([[6],{1853:function(e,t,a){"use strict";var r=a(48),n=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a(0)),i=(0,r(a(51)).default)(o.createElement("path",{d:"M13.05 9.79L10 7.5v9l3.05-2.29L16 12l-2.95-2.21zm0 0L10 7.5v9l3.05-2.29L16 12l-2.95-2.21zm0 0L10 7.5v9l3.05-2.29L16 12l-2.95-2.21zM11 4.07V2.05c-2.01.2-3.84 1-5.32 2.21L7.1 5.69c1.11-.86 2.44-1.44 3.9-1.62zM5.69 7.1L4.26 5.68C3.05 7.16 2.25 8.99 2.05 11h2.02c.18-1.46.76-2.79 1.62-3.9zM4.07 13H2.05c.2 2.01 1 3.84 2.21 5.32l1.43-1.43c-.86-1.1-1.44-2.43-1.62-3.89zm1.61 6.74C7.16 20.95 9 21.75 11 21.95v-2.02c-1.46-.18-2.79-.76-3.9-1.62l-1.42 1.43zM22 12c0 5.16-3.92 9.42-8.95 9.95v-2.02C16.97 19.41 20 16.05 20 12s-3.03-7.41-6.95-7.93V2.05C18.08 2.58 22 6.84 22 12z"}),"SlowMotionVideoOutlined");t.default=i},1932:function(e,t,a){"use strict";var r=a(48),n=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a(0)),i=(0,r(a(51)).default)(o.createElement("path",{d:"M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V6h16v12zM6 10h2v2H6zm0 4h8v2H6zm10 0h2v2h-2zm-6-4h8v2h-8z"}),"SubtitlesOutlined");t.default=i},1933:function(e,t,a){"use strict";var r=a(48),n=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a(0)),i=(0,r(a(51)).default)(o.createElement("path",{d:"M19.43 12.98c.04-.32.07-.64.07-.98 0-.34-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.09-.16-.26-.25-.44-.25-.06 0-.12.01-.17.03l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.06-.02-.12-.03-.18-.03-.17 0-.34.09-.43.25l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98 0 .33.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.09.16.26.25.44.25.06 0 .12-.01.17-.03l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.06.02.12.03.18.03.17 0 .34-.09.43-.25l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zm-1.98-1.71c.04.31.05.52.05.73 0 .21-.02.43-.05.73l-.14 1.13.89.7 1.08.84-.7 1.21-1.27-.51-1.04-.42-.9.68c-.43.32-.84.56-1.25.73l-1.06.43-.16 1.13-.2 1.35h-1.4l-.19-1.35-.16-1.13-1.06-.43c-.43-.18-.83-.41-1.23-.71l-.91-.7-1.06.43-1.27.51-.7-1.21 1.08-.84.89-.7-.14-1.13c-.03-.31-.05-.54-.05-.74s.02-.43.05-.73l.14-1.13-.89-.7-1.08-.84.7-1.21 1.27.51 1.04.42.9-.68c.43-.32.84-.56 1.25-.73l1.06-.43.16-1.13.2-1.35h1.39l.19 1.35.16 1.13 1.06.43c.43.18.83.41 1.23.71l.91.7 1.06-.43 1.27-.51.7 1.21-1.07.85-.89.7.14 1.13zM12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"}),"SettingsOutlined");t.default=i},2154:function(e,t,a){"use strict";var r=a(48),n=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a(0)),i=(0,r(a(51)).default)(o.createElement("path",{d:"M6 19h4V5H6v14zm8-14v14h4V5h-4z"}),"Pause");t.default=i},2155:function(e,t,a){"use strict";var r=a(48),n=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a(0)),i=(0,r(a(51)).default)(o.createElement("circle",{cx:"12",cy:"12",r:"8"}),"FiberManualRecordRounded");t.default=i},2156:function(e,t,a){"use strict";var r=a(48),n=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a(0)),i=(0,r(a(51)).default)(o.createElement("path",{d:"M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM4 12h4v2H4v-2zm10 6H4v-2h10v2zm6 0h-4v-2h4v2zm0-4H10v-2h10v2z"}),"Subtitles");t.default=i},2157:function(e,t,a){"use strict";var r=a(48),n=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a(0)),i=(0,r(a(51)).default)(o.createElement("path",{d:"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"}),"FullscreenExitOutlined");t.default=i},2158:function(e,t,a){"use strict";var r=a(48),n=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a(0)),i=(0,r(a(51)).default)(o.createElement("path",{d:"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"}),"FullscreenOutlined");t.default=i},2159:function(e,t,a){"use strict";var r=a(48),n=a(53);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a(0)),i=(0,r(a(51)).default)(o.createElement("path",{d:"M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"}),"SkipNext");t.default=i},3045:function(e,t,a){"use strict";var r=a(260),n=a(129),o=a(16),i=a(7),l=a(0),c=a(14),u=a(22),s=a(167),d=a(62),v=a(382),m=a(176),f=a(183),b=a(79),p=a(37),h=a(220);var g=Object(u.a)((function(e){return{thumb:{"&$open":{"& $offset":{transform:"scale(1) translateY(-10px)"}}},open:{},offset:Object(i.a)({zIndex:1},e.typography.body2,{fontSize:e.typography.pxToRem(12),lineHeight:1.2,transition:e.transitions.create(["transform"],{duration:e.transitions.duration.shortest}),top:-34,transformOrigin:"bottom center",transform:"scale(0)",position:"absolute"}),circle:{display:"flex",alignItems:"center",justifyContent:"center",width:32,height:32,borderRadius:"50% 50% 50% 0",backgroundColor:"currentColor",transform:"rotate(-45deg)"},label:{color:e.palette.primary.contrastText,transform:"rotate(45deg)"}}}),{name:"PrivateValueLabel"})((function(e){var t=e.children,a=e.classes,r=e.className,n=e.open,o=e.value,i=e.valueLabelDisplay;return"off"===i?t:l.cloneElement(t,{className:Object(c.default)(t.props.className,(n||"on"===i)&&a.open,a.thumb)},l.createElement("span",{className:Object(c.default)(a.offset,r)},l.createElement("span",{className:a.circle},l.createElement("span",{className:a.label},o))))}));function x(e,t){return e-t}function y(e,t,a){return Math.min(Math.max(t,e),a)}function O(e,t){return e.reduce((function(e,a,r){var n=Math.abs(t-a);return null===e||n<e.distance||n===e.distance?{distance:n,index:r}:e}),null).index}function j(e,t){if(void 0!==t.current&&e.changedTouches){for(var a=0;a<e.changedTouches.length;a+=1){var r=e.changedTouches[a];if(r.identifier===t.current)return{x:r.clientX,y:r.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function L(e,t,a){return 100*(e-t)/(a-t)}function w(e,t,a){var r=Math.round((e-a)/t)*t+a;return Number(r.toFixed(function(e){if(Math.abs(e)<1){var t=e.toExponential().split("e-"),a=t[0].split(".")[1];return(a?a.length:0)+parseInt(t[1],10)}var r=e.toString().split(".")[1];return r?r.length:0}(t)))}function k(e){var t=e.values,a=e.source,r=e.newValue,n=e.index;if(t[n]===r)return a;var o=t.slice();return o[n]=r,o}function E(e){var t=e.sliderRef,a=e.activeIndex,r=e.setActive;t.current.contains(document.activeElement)&&Number(document.activeElement.getAttribute("data-index"))===a||t.current.querySelector('[role="slider"][data-index="'.concat(a,'"]')).focus(),r&&r(a)}var z={horizontal:{offset:function(e){return{left:"".concat(e,"%")}},leap:function(e){return{width:"".concat(e,"%")}}},"horizontal-reverse":{offset:function(e){return{right:"".concat(e,"%")}},leap:function(e){return{width:"".concat(e,"%")}}},vertical:{offset:function(e){return{bottom:"".concat(e,"%")}},leap:function(e){return{height:"".concat(e,"%")}}}},M=function(e){return e},V=l.forwardRef((function(e,t){var a=e["aria-label"],u=e["aria-labelledby"],d=e["aria-valuetext"],V=e.classes,C=e.className,A=e.color,S=void 0===A?"primary":A,R=e.component,N=void 0===R?"span":R,$=e.defaultValue,H=e.disabled,_=void 0!==H&&H,I=e.getAriaLabel,T=e.getAriaValueText,P=e.marks,D=void 0!==P&&P,F=e.max,B=void 0===F?100:F,Y=e.min,X=void 0===Y?0:Y,J=e.name,U=e.onChange,q=e.onChangeCommitted,K=e.onMouseDown,W=e.orientation,G=void 0===W?"horizontal":W,Q=e.scale,Z=void 0===Q?M:Q,ee=e.step,te=void 0===ee?1:ee,ae=e.ThumbComponent,re=void 0===ae?"span":ae,ne=e.track,oe=void 0===ne?"normal":ne,ie=e.value,le=e.ValueLabelComponent,ce=void 0===le?g:le,ue=e.valueLabelDisplay,se=void 0===ue?"off":ue,de=e.valueLabelFormat,ve=void 0===de?M:de,me=Object(o.a)(e,["aria-label","aria-labelledby","aria-valuetext","classes","className","color","component","defaultValue","disabled","getAriaLabel","getAriaValueText","marks","max","min","name","onChange","onChangeCommitted","onMouseDown","orientation","scale","step","ThumbComponent","track","value","ValueLabelComponent","valueLabelDisplay","valueLabelFormat"]),fe=Object(s.a)(),be=l.useRef(),pe=l.useState(-1),he=pe[0],ge=pe[1],xe=l.useState(-1),ye=xe[0],Oe=xe[1],je=Object(h.a)({controlled:ie,default:$,name:"Slider"}),Le=Object(n.a)(je,2),we=Le[0],ke=Le[1],Ee=Array.isArray(we),ze=Ee?we.slice().sort(x):[we];ze=ze.map((function(e){return y(e,X,B)}));var Me=!0===D&&null!==te?Object(r.a)(Array(Math.floor((B-X)/te)+1)).map((function(e,t){return{value:X+te*t}})):D||[],Ve=Object(v.a)(),Ce=Ve.isFocusVisible,Ae=Ve.onBlurVisible,Se=Ve.ref,Re=l.useState(-1),Ne=Re[0],$e=Re[1],He=l.useRef(),_e=Object(b.a)(Se,He),Ie=Object(b.a)(t,_e),Te=Object(f.a)((function(e){var t=Number(e.currentTarget.getAttribute("data-index"));Ce(e)&&$e(t),Oe(t)})),Pe=Object(f.a)((function(){-1!==Ne&&($e(-1),Ae()),Oe(-1)})),De=Object(f.a)((function(e){var t=Number(e.currentTarget.getAttribute("data-index"));Oe(t)})),Fe=Object(f.a)((function(){Oe(-1)})),Be="rtl"===fe.direction,Ye=Object(f.a)((function(e){var t,a=Number(e.currentTarget.getAttribute("data-index")),r=ze[a],n=(B-X)/10,o=Me.map((function(e){return e.value})),i=o.indexOf(r),l=Be?"ArrowLeft":"ArrowRight",c=Be?"ArrowRight":"ArrowLeft";switch(e.key){case"Home":t=X;break;case"End":t=B;break;case"PageUp":te&&(t=r+n);break;case"PageDown":te&&(t=r-n);break;case l:case"ArrowUp":t=te?r+te:o[i+1]||o[o.length-1];break;case c:case"ArrowDown":t=te?r-te:o[i-1]||o[0];break;default:return}if(e.preventDefault(),te&&(t=w(t,te,X)),t=y(t,X,B),Ee){var u=t;t=k({values:ze,source:we,newValue:t,index:a}).sort(x),E({sliderRef:He,activeIndex:t.indexOf(u)})}ke(t),$e(a),U&&U(e,t),q&&q(e,t)})),Xe=l.useRef(),Je=G;Be&&"vertical"!==G&&(Je+="-reverse");var Ue=function(e){var t,a,r=e.finger,n=e.move,o=void 0!==n&&n,i=e.values,l=e.source,c=He.current.getBoundingClientRect(),u=c.width,s=c.height,d=c.bottom,v=c.left;if(t=0===Je.indexOf("vertical")?(d-r.y)/s:(r.x-v)/u,-1!==Je.indexOf("-reverse")&&(t=1-t),a=function(e,t,a){return(a-t)*e+t}(t,X,B),te)a=w(a,te,X);else{var m=Me.map((function(e){return e.value}));a=m[O(m,a)]}a=y(a,X,B);var f=0;if(Ee){var b=a;f=(a=k({values:i,source:l,newValue:a,index:f=o?Xe.current:O(i,a)}).sort(x)).indexOf(b),Xe.current=f}return{newValue:a,activeIndex:f}},qe=Object(f.a)((function(e){var t=j(e,be);if(t){var a=Ue({finger:t,move:!0,values:ze,source:we}),r=a.newValue,n=a.activeIndex;E({sliderRef:He,activeIndex:n,setActive:ge}),ke(r),U&&U(e,r)}})),Ke=Object(f.a)((function(e){var t=j(e,be);if(t){var a=Ue({finger:t,values:ze,source:we}).newValue;ge(-1),"touchend"===e.type&&Oe(-1),q&&q(e,a),be.current=void 0;var r=Object(m.a)(He.current);r.removeEventListener("mousemove",qe),r.removeEventListener("mouseup",Ke),r.removeEventListener("touchmove",qe),r.removeEventListener("touchend",Ke)}})),We=Object(f.a)((function(e){e.preventDefault();var t=e.changedTouches[0];null!=t&&(be.current=t.identifier);var a=j(e,be),r=Ue({finger:a,values:ze,source:we}),n=r.newValue,o=r.activeIndex;E({sliderRef:He,activeIndex:o,setActive:ge}),ke(n),U&&U(e,n);var i=Object(m.a)(He.current);i.addEventListener("touchmove",qe),i.addEventListener("touchend",Ke)}));l.useEffect((function(){var e=He.current;e.addEventListener("touchstart",We);var t=Object(m.a)(e);return function(){e.removeEventListener("touchstart",We),t.removeEventListener("mousemove",qe),t.removeEventListener("mouseup",Ke),t.removeEventListener("touchmove",qe),t.removeEventListener("touchend",Ke)}}),[Ke,qe,We]);var Ge=Object(f.a)((function(e){K&&K(e),e.preventDefault();var t=j(e,be),a=Ue({finger:t,values:ze,source:we}),r=a.newValue,n=a.activeIndex;E({sliderRef:He,activeIndex:n,setActive:ge}),ke(r),U&&U(e,r);var o=Object(m.a)(He.current);o.addEventListener("mousemove",qe),o.addEventListener("mouseup",Ke)})),Qe=L(Ee?ze[0]:X,X,B),Ze=L(ze[ze.length-1],X,B)-Qe,et=Object(i.a)({},z[Je].offset(Qe),z[Je].leap(Ze));return l.createElement(N,Object(i.a)({ref:Ie,className:Object(c.default)(V.root,V["color".concat(Object(p.a)(S))],C,_&&V.disabled,Me.length>0&&Me.some((function(e){return e.label}))&&V.marked,!1===oe&&V.trackFalse,"vertical"===G&&V.vertical,"inverted"===oe&&V.trackInverted),onMouseDown:Ge},me),l.createElement("span",{className:V.rail}),l.createElement("span",{className:V.track,style:et}),l.createElement("input",{value:ze.join(","),name:J,type:"hidden"}),Me.map((function(e,t){var a,r=L(e.value,X,B),n=z[Je].offset(r);return a=!1===oe?-1!==ze.indexOf(e.value):"normal"===oe&&(Ee?e.value>=ze[0]&&e.value<=ze[ze.length-1]:e.value<=ze[0])||"inverted"===oe&&(Ee?e.value<=ze[0]||e.value>=ze[ze.length-1]:e.value>=ze[0]),l.createElement(l.Fragment,{key:e.value},l.createElement("span",{style:n,"data-index":t,className:Object(c.default)(V.mark,a&&V.markActive)}),null!=e.label?l.createElement("span",{"aria-hidden":!0,"data-index":t,style:n,className:Object(c.default)(V.markLabel,a&&V.markLabelActive)},e.label):null)})),ze.map((function(e,t){var r=L(e,X,B),n=z[Je].offset(r);return l.createElement(ce,{key:t,valueLabelFormat:ve,valueLabelDisplay:se,className:V.valueLabel,value:"function"===typeof ve?ve(Z(e),t):ve,index:t,open:ye===t||he===t||"on"===se,disabled:_},l.createElement(re,{className:Object(c.default)(V.thumb,V["thumbColor".concat(Object(p.a)(S))],he===t&&V.active,_&&V.disabled,Ne===t&&V.focusVisible),tabIndex:_?null:0,role:"slider",style:n,"data-index":t,"aria-label":I?I(t):a,"aria-labelledby":u,"aria-orientation":G,"aria-valuemax":Z(B),"aria-valuemin":Z(X),"aria-valuenow":Z(e),"aria-valuetext":T?T(Z(e),t):d,onKeyDown:Ye,onFocus:Te,onBlur:Pe,onMouseOver:De,onMouseLeave:Fe}))})))}));t.a=Object(u.a)((function(e){return{root:{height:2,width:"100%",boxSizing:"content-box",padding:"13px 0",display:"inline-block",position:"relative",cursor:"pointer",touchAction:"none",color:e.palette.primary.main,WebkitTapHighlightColor:"transparent","&$disabled":{pointerEvents:"none",cursor:"default",color:e.palette.grey[400]},"&$vertical":{width:2,height:"100%",padding:"0 13px"},"@media (pointer: coarse)":{padding:"20px 0","&$vertical":{padding:"0 20px"}},"@media print":{colorAdjust:"exact"}},colorPrimary:{},colorSecondary:{color:e.palette.secondary.main},marked:{marginBottom:20,"&$vertical":{marginBottom:"auto",marginRight:20}},vertical:{},disabled:{},rail:{display:"block",position:"absolute",width:"100%",height:2,borderRadius:1,backgroundColor:"currentColor",opacity:.38,"$vertical &":{height:"100%",width:2}},track:{display:"block",position:"absolute",height:2,borderRadius:1,backgroundColor:"currentColor","$vertical &":{width:2}},trackFalse:{"& $track":{display:"none"}},trackInverted:{"& $track":{backgroundColor:"light"===e.palette.type?Object(d.f)(e.palette.primary.main,.62):Object(d.b)(e.palette.primary.main,.5)},"& $rail":{opacity:1}},thumb:{position:"absolute",width:12,height:12,marginLeft:-6,marginTop:-5,boxSizing:"border-box",borderRadius:"50%",outline:0,backgroundColor:"currentColor",display:"flex",alignItems:"center",justifyContent:"center",transition:e.transitions.create(["box-shadow"],{duration:e.transitions.duration.shortest}),"&::after":{position:"absolute",content:'""',borderRadius:"50%",left:-15,top:-15,right:-15,bottom:-15},"&$focusVisible,&:hover":{boxShadow:"0px 0px 0px 8px ".concat(Object(d.a)(e.palette.primary.main,.16)),"@media (hover: none)":{boxShadow:"none"}},"&$active":{boxShadow:"0px 0px 0px 14px ".concat(Object(d.a)(e.palette.primary.main,.16))},"&$disabled":{width:8,height:8,marginLeft:-4,marginTop:-3,"&:hover":{boxShadow:"none"}},"$vertical &":{marginLeft:-5,marginBottom:-6},"$vertical &$disabled":{marginLeft:-3,marginBottom:-4}},thumbColorPrimary:{},thumbColorSecondary:{"&$focusVisible,&:hover":{boxShadow:"0px 0px 0px 8px ".concat(Object(d.a)(e.palette.secondary.main,.16))},"&$active":{boxShadow:"0px 0px 0px 14px ".concat(Object(d.a)(e.palette.secondary.main,.16))}},active:{},focusVisible:{},valueLabel:{left:"calc(-50% - 4px)"},mark:{position:"absolute",width:2,height:2,borderRadius:1,backgroundColor:"currentColor"},markActive:{backgroundColor:e.palette.background.paper,opacity:.8},markLabel:Object(i.a)({},e.typography.body2,{color:e.palette.text.secondary,position:"absolute",top:26,transform:"translateX(-50%)",whiteSpace:"nowrap","$vertical &":{top:"auto",left:26,transform:"translateY(50%)"},"@media (pointer: coarse)":{top:40,"$vertical &":{left:31}}}),markLabelActive:{color:e.palette.text.primary}}}),{name:"MuiSlider"})(V)}}]);