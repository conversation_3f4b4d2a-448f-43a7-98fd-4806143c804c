<?xml version="1.0" encoding="UTF-8"?><process version="9.10.013">
  <context>
    <input/>
    <output/>
    <macros/>
  </context>
  <operator activated="true" class="process" compatibility="9.10.013" expanded="true" name="Process">
    <parameter key="logverbosity" value="init"/>
    <parameter key="random_seed" value="2001"/>
    <parameter key="send_mail" value="never"/>
    <parameter key="notification_email" value=""/>
    <parameter key="process_duration_for_mail" value="30"/>
    <parameter key="encoding" value="SYSTEM"/>
    <process expanded="true">
      <operator activated="true" class="retrieve" compatibility="9.10.013" expanded="true" height="68" name="Retrieve" width="90" x="45" y="34">
        <parameter key="repository_entry" value="//Local Repository/data/a"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.013" expanded="true" height="103" name="Multiply (2)" width="90" x="45" y="391"/>
      <operator activated="true" class="filter_examples" compatibility="9.10.013" expanded="true" height="103" name="Filter Examples" width="90" x="179" y="340">
        <parameter key="parameter_expression" value=""/>
        <parameter key="condition_class" value="custom_filters"/>
        <parameter key="invert_filter" value="false"/>
        <list key="filters_list">
          <parameter key="filters_entry_key" value="booking_status.equals.Canceled"/>
        </list>
        <parameter key="filters_logic_and" value="true"/>
        <parameter key="filters_check_metadata" value="true"/>
      </operator>
      <operator activated="true" class="union" compatibility="9.10.013" expanded="true" height="82" name="Union" width="90" x="313" y="442"/>
      <operator activated="true" class="select_attributes" compatibility="9.10.013" expanded="true" height="82" name="Select Attributes" width="90" x="380" y="340">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="Booking_IDcorr"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="true"/>
        <parameter key="include_special_attributes" value="false"/>
      </operator>
      <operator activated="true" class="set_role" compatibility="9.10.013" expanded="true" height="82" name="Set Role" width="90" x="313" y="238">
        <parameter key="attribute_name" value="booking_status"/>
        <parameter key="target_role" value="label"/>
        <list key="set_additional_roles"/>
      </operator>
      <operator activated="true" class="concurrency:optimize_parameters_grid" compatibility="9.10.013" expanded="true" height="166" name="Optimize Parameters (Grid)" width="90" x="447" y="85">
        <list key="parameters">
          <parameter key="Random Forest.maximal_depth" value="[13;16;3;linear]"/>
          <parameter key="Random Forest.minimal_leaf_size" value="[6;12;3;linear]"/>
          <parameter key="Random Forest.minimal_size_for_split" value="[45;55;3;linear]"/>
          <parameter key="Random Forest.number_of_trees" value="[60;80;3;linear]"/>
        </list>
        <parameter key="error_handling" value="fail on error"/>
        <parameter key="log_performance" value="true"/>
        <parameter key="log_all_criteria" value="false"/>
        <parameter key="synchronize" value="false"/>
        <parameter key="enable_parallel_execution" value="true"/>
        <process expanded="true">
          <operator activated="true" class="split_data" compatibility="9.10.013" expanded="true" height="103" name="Split Data" width="90" x="45" y="34">
            <enumeration key="partitions">
              <parameter key="ratio" value="0.7"/>
              <parameter key="ratio" value="0.3"/>
            </enumeration>
            <parameter key="sampling_type" value="stratified sampling"/>
            <parameter key="use_local_random_seed" value="true"/>
            <parameter key="local_random_seed" value="1"/>
          </operator>
          <operator activated="true" class="concurrency:parallel_random_forest" compatibility="9.10.013" expanded="true" height="103" name="Random Forest" width="90" x="179" y="34">
            <parameter key="number_of_trees" value="100"/>
            <parameter key="criterion" value="gini_index"/>
            <parameter key="maximal_depth" value="100"/>
            <parameter key="apply_pruning" value="true"/>
            <parameter key="confidence" value="0.1"/>
            <parameter key="apply_prepruning" value="true"/>
            <parameter key="minimal_gain" value="0.01"/>
            <parameter key="minimal_leaf_size" value="2"/>
            <parameter key="minimal_size_for_split" value="4"/>
            <parameter key="number_of_prepruning_alternatives" value="3"/>
            <parameter key="random_splits" value="false"/>
            <parameter key="guess_subset_ratio" value="true"/>
            <parameter key="subset_ratio" value="0.2"/>
            <parameter key="voting_strategy" value="confidence vote"/>
            <parameter key="use_local_random_seed" value="false"/>
            <parameter key="local_random_seed" value="1992"/>
            <parameter key="enable_parallel_execution" value="true"/>
          </operator>
          <operator activated="true" class="multiply" compatibility="9.10.013" expanded="true" height="103" name="Multiply" width="90" x="313" y="136"/>
          <operator activated="true" class="apply_model" compatibility="9.10.013" expanded="true" height="82" name="Apply Model - Training Set" width="90" x="447" y="34">
            <list key="application_parameters"/>
            <parameter key="create_view" value="false"/>
          </operator>
          <operator activated="true" class="performance_classification" compatibility="9.10.013" expanded="true" height="82" name="Performance Training Set" width="90" x="581" y="34">
            <parameter key="main_criterion" value="first"/>
            <parameter key="accuracy" value="true"/>
            <parameter key="classification_error" value="false"/>
            <parameter key="kappa" value="false"/>
            <parameter key="weighted_mean_recall" value="true"/>
            <parameter key="weighted_mean_precision" value="false"/>
            <parameter key="spearman_rho" value="false"/>
            <parameter key="kendall_tau" value="false"/>
            <parameter key="absolute_error" value="false"/>
            <parameter key="relative_error" value="false"/>
            <parameter key="relative_error_lenient" value="false"/>
            <parameter key="relative_error_strict" value="false"/>
            <parameter key="normalized_absolute_error" value="false"/>
            <parameter key="root_mean_squared_error" value="false"/>
            <parameter key="root_relative_squared_error" value="false"/>
            <parameter key="squared_error" value="false"/>
            <parameter key="correlation" value="false"/>
            <parameter key="squared_correlation" value="false"/>
            <parameter key="cross-entropy" value="false"/>
            <parameter key="margin" value="false"/>
            <parameter key="soft_margin_loss" value="false"/>
            <parameter key="logistic_loss" value="false"/>
            <parameter key="skip_undefined_labels" value="true"/>
            <parameter key="use_example_weights" value="true"/>
            <list key="class_weights"/>
          </operator>
          <connect from_port="input 1" to_op="Split Data" to_port="example set"/>
          <connect from_op="Split Data" from_port="partition 1" to_op="Random Forest" to_port="training set"/>
          <connect from_op="Split Data" from_port="partition 2" to_port="output 1"/>
          <connect from_op="Random Forest" from_port="model" to_op="Multiply" to_port="input"/>
          <connect from_op="Random Forest" from_port="exampleSet" to_op="Apply Model - Training Set" to_port="unlabelled data"/>
          <connect from_op="Random Forest" from_port="weights" to_port="output 2"/>
          <connect from_op="Multiply" from_port="output 1" to_op="Apply Model - Training Set" to_port="model"/>
          <connect from_op="Multiply" from_port="output 2" to_port="model"/>
          <connect from_op="Apply Model - Training Set" from_port="labelled data" to_op="Performance Training Set" to_port="labelled data"/>
          <connect from_op="Performance Training Set" from_port="performance" to_port="performance"/>
          <portSpacing port="source_input 1" spacing="0"/>
          <portSpacing port="source_input 2" spacing="0"/>
          <portSpacing port="sink_performance" spacing="0"/>
          <portSpacing port="sink_model" spacing="0"/>
          <portSpacing port="sink_output 1" spacing="0"/>
          <portSpacing port="sink_output 2" spacing="0"/>
          <portSpacing port="sink_output 3" spacing="0"/>
        </process>
      </operator>
      <operator activated="true" class="apply_model" compatibility="9.10.013" expanded="true" height="82" name="Apply Model - Testing Set" width="90" x="581" y="136">
        <list key="application_parameters"/>
        <parameter key="create_view" value="false"/>
      </operator>
      <operator activated="true" class="performance_classification" compatibility="9.10.013" expanded="true" height="82" name="Performance - Testing Set" width="90" x="715" y="136">
        <parameter key="main_criterion" value="first"/>
        <parameter key="accuracy" value="true"/>
        <parameter key="classification_error" value="false"/>
        <parameter key="kappa" value="false"/>
        <parameter key="weighted_mean_recall" value="false"/>
        <parameter key="weighted_mean_precision" value="false"/>
        <parameter key="spearman_rho" value="false"/>
        <parameter key="kendall_tau" value="false"/>
        <parameter key="absolute_error" value="false"/>
        <parameter key="relative_error" value="false"/>
        <parameter key="relative_error_lenient" value="false"/>
        <parameter key="relative_error_strict" value="false"/>
        <parameter key="normalized_absolute_error" value="false"/>
        <parameter key="root_mean_squared_error" value="false"/>
        <parameter key="root_relative_squared_error" value="false"/>
        <parameter key="squared_error" value="false"/>
        <parameter key="correlation" value="false"/>
        <parameter key="squared_correlation" value="false"/>
        <parameter key="cross-entropy" value="false"/>
        <parameter key="margin" value="false"/>
        <parameter key="soft_margin_loss" value="false"/>
        <parameter key="logistic_loss" value="false"/>
        <parameter key="skip_undefined_labels" value="true"/>
        <parameter key="use_example_weights" value="true"/>
        <list key="class_weights"/>
      </operator>
      <connect from_op="Retrieve" from_port="output" to_op="Multiply (2)" to_port="input"/>
      <connect from_op="Multiply (2)" from_port="output 1" to_op="Filter Examples" to_port="example set input"/>
      <connect from_op="Multiply (2)" from_port="output 2" to_op="Union" to_port="example set 2"/>
      <connect from_op="Filter Examples" from_port="example set output" to_op="Union" to_port="example set 1"/>
      <connect from_op="Union" from_port="union" to_op="Select Attributes" to_port="example set input"/>
      <connect from_op="Select Attributes" from_port="example set output" to_op="Set Role" to_port="example set input"/>
      <connect from_op="Set Role" from_port="example set output" to_op="Optimize Parameters (Grid)" to_port="input 1"/>
      <connect from_op="Optimize Parameters (Grid)" from_port="performance" to_port="result 1"/>
      <connect from_op="Optimize Parameters (Grid)" from_port="model" to_op="Apply Model - Testing Set" to_port="model"/>
      <connect from_op="Optimize Parameters (Grid)" from_port="parameter set" to_port="result 4"/>
      <connect from_op="Optimize Parameters (Grid)" from_port="output 1" to_op="Apply Model - Testing Set" to_port="unlabelled data"/>
      <connect from_op="Optimize Parameters (Grid)" from_port="output 2" to_port="result 5"/>
      <connect from_op="Apply Model - Testing Set" from_port="labelled data" to_op="Performance - Testing Set" to_port="labelled data"/>
      <connect from_op="Apply Model - Testing Set" from_port="model" to_port="result 3"/>
      <connect from_op="Performance - Testing Set" from_port="performance" to_port="result 2"/>
      <portSpacing port="source_input 1" spacing="0"/>
      <portSpacing port="sink_result 1" spacing="0"/>
      <portSpacing port="sink_result 2" spacing="0"/>
      <portSpacing port="sink_result 3" spacing="0"/>
      <portSpacing port="sink_result 4" spacing="0"/>
      <portSpacing port="sink_result 5" spacing="0"/>
      <portSpacing port="sink_result 6" spacing="0"/>
    </process>
  </operator>
</process>
