<?xml version="1.0" encoding="UTF-8"?><process version="9.10.011">
  <context>
    <input/>
    <output/>
    <macros/>
  </context>
  <operator activated="true" class="process" compatibility="9.10.011" expanded="true" name="Process">
    <parameter key="logverbosity" value="init"/>
    <parameter key="random_seed" value="2001"/>
    <parameter key="send_mail" value="never"/>
    <parameter key="notification_email" value=""/>
    <parameter key="process_duration_for_mail" value="30"/>
    <parameter key="encoding" value="SYSTEM"/>
    <process expanded="true">
      <operator activated="true" class="retrieve" compatibility="9.10.011" expanded="true" height="68" name="Retrieve" width="90" x="45" y="34">
        <parameter key="repository_entry" value="../../Data/Time-Series/amazon_stocks_prices"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.011" expanded="true" height="103" name="Multiply (3)" width="90" x="179" y="34"/>
      <operator activated="true" class="time_series:logarithm" compatibility="9.10.009" expanded="true" height="68" name="Logarithm" width="90" x="179" y="187">
        <parameter key="attribute_filter_type" value="all"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="true"/>
        <parameter key="new_attributes_postfix" value="_logarithm"/>
        <parameter key="logarithm_type" value="ln"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.011" expanded="true" height="103" name="Multiply" width="90" x="313" y="187"/>
      <operator activated="true" class="time_series:differentiation" compatibility="9.10.009" expanded="true" height="68" name="Differentiate" width="90" x="447" y="289">
        <parameter key="attribute_filter_type" value="all"/>
        <parameter key="attribute" value=""/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="true"/>
        <parameter key="new_attributes_postfix" value="_differentiated"/>
        <parameter key="lag" value="1"/>
        <parameter key="differentiation_method" value="subtraction"/>
      </operator>
      <operator activated="true" class="time_series:moving_average_filter" compatibility="9.10.009" expanded="true" height="68" name="Moving Average Filter - Mean" width="90" x="313" y="34">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="close"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="false"/>
        <parameter key="new_attributes_postfix" value="Price Rolling Mean"/>
        <parameter key="aggregation_method" value="mean"/>
        <parameter key="filter_type" value="simple"/>
        <parameter key="filter_size_left" value="12"/>
        <parameter key="filter_size_right" value="1"/>
        <parameter key="filter_size" value="1"/>
        <parameter key="ignore_invalid_values" value="false"/>
      </operator>
      <operator activated="true" class="time_series:moving_average_filter" compatibility="9.10.009" expanded="true" height="68" name="Moving Average Filter - SD" width="90" x="447" y="34">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="close"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="false"/>
        <parameter key="new_attributes_postfix" value="Price Rolling SD"/>
        <parameter key="aggregation_method" value="standard deviation"/>
        <parameter key="filter_type" value="simple"/>
        <parameter key="filter_size_left" value="12"/>
        <parameter key="filter_size_right" value="1"/>
        <parameter key="filter_size" value="1"/>
        <parameter key="ignore_invalid_values" value="false"/>
      </operator>
      <operator activated="true" class="time_series:moving_average_filter" compatibility="9.10.009" expanded="true" height="68" name="Moving Average Filter - Mean (Log)" width="90" x="447" y="136">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="close"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="false"/>
        <parameter key="new_attributes_postfix" value="Price Rolling Mean"/>
        <parameter key="aggregation_method" value="mean"/>
        <parameter key="filter_type" value="simple"/>
        <parameter key="filter_size_left" value="12"/>
        <parameter key="filter_size_right" value="1"/>
        <parameter key="filter_size" value="1"/>
        <parameter key="ignore_invalid_values" value="false"/>
      </operator>
      <operator activated="true" class="time_series:moving_average_filter" compatibility="9.10.009" expanded="true" height="68" name="Moving Average Filter - SD (Log)" width="90" x="581" y="136">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="close"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="false"/>
        <parameter key="new_attributes_postfix" value="Price Rolling SD"/>
        <parameter key="aggregation_method" value="standard deviation"/>
        <parameter key="filter_type" value="simple"/>
        <parameter key="filter_size_left" value="12"/>
        <parameter key="filter_size_right" value="1"/>
        <parameter key="filter_size" value="1"/>
        <parameter key="ignore_invalid_values" value="false"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.011" expanded="true" height="82" name="Multiply (2)" width="90" x="581" y="289"/>
      <operator activated="true" class="time_series:moving_average_filter" compatibility="9.10.009" expanded="true" height="68" name="Moving Average Filter - Mean (Log- Diff)" width="90" x="715" y="289">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="close"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="false"/>
        <parameter key="new_attributes_postfix" value="Price Rolling Mean"/>
        <parameter key="aggregation_method" value="mean"/>
        <parameter key="filter_type" value="simple"/>
        <parameter key="filter_size_left" value="12"/>
        <parameter key="filter_size_right" value="1"/>
        <parameter key="filter_size" value="1"/>
        <parameter key="ignore_invalid_values" value="false"/>
      </operator>
      <operator activated="true" class="time_series:moving_average_filter" compatibility="9.10.009" expanded="true" height="68" name="Moving Average Filter - SD (Log-Diff) ()" width="90" x="849" y="289">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="close"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="numeric"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="real"/>
        <parameter key="block_type" value="value_series"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_series_end"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
        <parameter key="overwrite_attributes" value="false"/>
        <parameter key="new_attributes_postfix" value="Price Rolling SD"/>
        <parameter key="aggregation_method" value="standard deviation"/>
        <parameter key="filter_type" value="simple"/>
        <parameter key="filter_size_left" value="12"/>
        <parameter key="filter_size_right" value="1"/>
        <parameter key="filter_size" value="1"/>
        <parameter key="ignore_invalid_values" value="false"/>
      </operator>
      <connect from_op="Retrieve" from_port="output" to_op="Multiply (3)" to_port="input"/>
      <connect from_op="Multiply (3)" from_port="output 1" to_op="Moving Average Filter - Mean" to_port="example set"/>
      <connect from_op="Multiply (3)" from_port="output 2" to_op="Logarithm" to_port="example set"/>
      <connect from_op="Logarithm" from_port="example set" to_op="Multiply" to_port="input"/>
      <connect from_op="Multiply" from_port="output 1" to_op="Moving Average Filter - Mean (Log)" to_port="example set"/>
      <connect from_op="Multiply" from_port="output 2" to_op="Differentiate" to_port="example set"/>
      <connect from_op="Differentiate" from_port="example set" to_op="Multiply (2)" to_port="input"/>
      <connect from_op="Moving Average Filter - Mean" from_port="example set" to_op="Moving Average Filter - SD" to_port="example set"/>
      <connect from_op="Moving Average Filter - SD" from_port="example set" to_port="result 1"/>
      <connect from_op="Moving Average Filter - Mean (Log)" from_port="example set" to_op="Moving Average Filter - SD (Log)" to_port="example set"/>
      <connect from_op="Moving Average Filter - SD (Log)" from_port="example set" to_port="result 2"/>
      <connect from_op="Multiply (2)" from_port="output 1" to_op="Moving Average Filter - Mean (Log- Diff)" to_port="example set"/>
      <connect from_op="Moving Average Filter - Mean (Log- Diff)" from_port="example set" to_op="Moving Average Filter - SD (Log-Diff) ()" to_port="example set"/>
      <connect from_op="Moving Average Filter - SD (Log-Diff) ()" from_port="example set" to_port="result 3"/>
      <portSpacing port="source_input 1" spacing="0"/>
      <portSpacing port="sink_result 1" spacing="0"/>
      <portSpacing port="sink_result 2" spacing="0"/>
      <portSpacing port="sink_result 3" spacing="0"/>
      <portSpacing port="sink_result 4" spacing="0"/>
    </process>
  </operator>
</process>
