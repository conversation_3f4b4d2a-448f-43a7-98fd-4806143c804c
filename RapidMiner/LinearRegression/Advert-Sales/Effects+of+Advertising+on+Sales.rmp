<?xml version="1.0" encoding="UTF-8"?><process version="9.10.011">
  <context>
    <input/>
    <output/>
    <macros/>
  </context>
  <operator activated="true" class="process" compatibility="9.10.011" expanded="true" name="Process">
    <parameter key="logverbosity" value="init"/>
    <parameter key="random_seed" value="2001"/>
    <parameter key="send_mail" value="never"/>
    <parameter key="notification_email" value=""/>
    <parameter key="process_duration_for_mail" value="30"/>
    <parameter key="encoding" value="SYSTEM"/>
    <process expanded="true">
      <operator activated="true" class="retrieve" compatibility="9.10.011" expanded="true" height="68" name="Retrieve Advertising" width="90" x="45" y="34">
        <parameter key="repository_entry" value="//New Local Repository/Data/Regression/Add. Practice Case Study/Advertising"/>
      </operator>
      <operator activated="true" class="set_role" compatibility="9.10.011" expanded="true" height="82" name="Set Role" width="90" x="179" y="34">
        <parameter key="attribute_name" value="Sales"/>
        <parameter key="target_role" value="label"/>
        <list key="set_additional_roles"/>
      </operator>
      <operator activated="true" class="multiply" compatibility="9.10.011" expanded="true" height="250" name="Multiply" width="90" x="313" y="34"/>
      <operator activated="true" class="concurrency:cross_validation" compatibility="9.10.011" expanded="true" height="145" name="LOO Cross Validation" width="90" x="447" y="850">
        <parameter key="split_on_batch_attribute" value="false"/>
        <parameter key="leave_one_out" value="true"/>
        <parameter key="number_of_folds" value="10"/>
        <parameter key="sampling_type" value="shuffled sampling"/>
        <parameter key="use_local_random_seed" value="true"/>
        <parameter key="local_random_seed" value="1"/>
        <parameter key="enable_parallel_execution" value="true"/>
        <process expanded="true">
          <operator activated="true" class="linear_regression" compatibility="9.10.011" expanded="true" height="103" name="Linear Regression (LOOCV)" width="90" x="45" y="34">
            <parameter key="feature_selection" value="M5 prime"/>
            <parameter key="alpha" value="0.05"/>
            <parameter key="max_iterations" value="10"/>
            <parameter key="forward_alpha" value="0.05"/>
            <parameter key="backward_alpha" value="0.05"/>
            <parameter key="eliminate_colinear_features" value="false"/>
            <parameter key="min_tolerance" value="0.05"/>
            <parameter key="use_bias" value="true"/>
            <parameter key="ridge" value="1.0E-8"/>
          </operator>
          <connect from_port="training set" to_op="Linear Regression (LOOCV)" to_port="training set"/>
          <connect from_op="Linear Regression (LOOCV)" from_port="model" to_port="model"/>
          <portSpacing port="source_training set" spacing="0"/>
          <portSpacing port="sink_model" spacing="0"/>
          <portSpacing port="sink_through 1" spacing="0"/>
        </process>
        <process expanded="true">
          <operator activated="true" class="apply_model" compatibility="9.10.011" expanded="true" height="82" name="Apply Model (LOOCV)" width="90" x="45" y="34">
            <list key="application_parameters"/>
            <parameter key="create_view" value="false"/>
          </operator>
          <operator activated="true" class="performance_regression" compatibility="9.10.011" expanded="true" height="82" name="Performance (LOOCV)" width="90" x="246" y="34">
            <parameter key="main_criterion" value="first"/>
            <parameter key="root_mean_squared_error" value="true"/>
            <parameter key="absolute_error" value="true"/>
            <parameter key="relative_error" value="false"/>
            <parameter key="relative_error_lenient" value="false"/>
            <parameter key="relative_error_strict" value="false"/>
            <parameter key="normalized_absolute_error" value="false"/>
            <parameter key="root_relative_squared_error" value="false"/>
            <parameter key="squared_error" value="false"/>
            <parameter key="correlation" value="false"/>
            <parameter key="squared_correlation" value="true"/>
            <parameter key="prediction_average" value="false"/>
            <parameter key="spearman_rho" value="false"/>
            <parameter key="kendall_tau" value="false"/>
            <parameter key="skip_undefined_labels" value="true"/>
            <parameter key="use_example_weights" value="true"/>
          </operator>
          <connect from_port="model" to_op="Apply Model (LOOCV)" to_port="model"/>
          <connect from_port="test set" to_op="Apply Model (LOOCV)" to_port="unlabelled data"/>
          <connect from_op="Apply Model (LOOCV)" from_port="labelled data" to_op="Performance (LOOCV)" to_port="labelled data"/>
          <connect from_op="Performance (LOOCV)" from_port="performance" to_port="performance 1"/>
          <portSpacing port="source_model" spacing="0"/>
          <portSpacing port="source_test set" spacing="0"/>
          <portSpacing port="source_through 1" spacing="0"/>
          <portSpacing port="sink_test set results" spacing="0"/>
          <portSpacing port="sink_performance 1" spacing="0"/>
          <portSpacing port="sink_performance 2" spacing="0"/>
        </process>
      </operator>
      <operator activated="true" class="concurrency:cross_validation" compatibility="9.10.011" expanded="true" height="145" name="K Fold Cross Validation" width="90" x="447" y="646">
        <parameter key="split_on_batch_attribute" value="false"/>
        <parameter key="leave_one_out" value="false"/>
        <parameter key="number_of_folds" value="10"/>
        <parameter key="sampling_type" value="shuffled sampling"/>
        <parameter key="use_local_random_seed" value="true"/>
        <parameter key="local_random_seed" value="1"/>
        <parameter key="enable_parallel_execution" value="true"/>
        <process expanded="true">
          <operator activated="true" class="linear_regression" compatibility="9.10.011" expanded="true" height="103" name="Linear Regression (K-Fold)" width="90" x="45" y="34">
            <parameter key="feature_selection" value="M5 prime"/>
            <parameter key="alpha" value="0.05"/>
            <parameter key="max_iterations" value="10"/>
            <parameter key="forward_alpha" value="0.05"/>
            <parameter key="backward_alpha" value="0.05"/>
            <parameter key="eliminate_colinear_features" value="false"/>
            <parameter key="min_tolerance" value="0.05"/>
            <parameter key="use_bias" value="true"/>
            <parameter key="ridge" value="1.0E-8"/>
          </operator>
          <connect from_port="training set" to_op="Linear Regression (K-Fold)" to_port="training set"/>
          <connect from_op="Linear Regression (K-Fold)" from_port="model" to_port="model"/>
          <portSpacing port="source_training set" spacing="0"/>
          <portSpacing port="sink_model" spacing="0"/>
          <portSpacing port="sink_through 1" spacing="0"/>
        </process>
        <process expanded="true">
          <operator activated="true" class="apply_model" compatibility="9.10.011" expanded="true" height="82" name="Apply Model (K-Fold)" width="90" x="45" y="34">
            <list key="application_parameters"/>
            <parameter key="create_view" value="false"/>
          </operator>
          <operator activated="true" class="performance_regression" compatibility="9.10.011" expanded="true" height="82" name="Performance (K-Fold)" width="90" x="246" y="34">
            <parameter key="main_criterion" value="first"/>
            <parameter key="root_mean_squared_error" value="true"/>
            <parameter key="absolute_error" value="true"/>
            <parameter key="relative_error" value="false"/>
            <parameter key="relative_error_lenient" value="false"/>
            <parameter key="relative_error_strict" value="false"/>
            <parameter key="normalized_absolute_error" value="false"/>
            <parameter key="root_relative_squared_error" value="false"/>
            <parameter key="squared_error" value="false"/>
            <parameter key="correlation" value="false"/>
            <parameter key="squared_correlation" value="true"/>
            <parameter key="prediction_average" value="false"/>
            <parameter key="spearman_rho" value="false"/>
            <parameter key="kendall_tau" value="false"/>
            <parameter key="skip_undefined_labels" value="true"/>
            <parameter key="use_example_weights" value="true"/>
          </operator>
          <connect from_port="model" to_op="Apply Model (K-Fold)" to_port="model"/>
          <connect from_port="test set" to_op="Apply Model (K-Fold)" to_port="unlabelled data"/>
          <connect from_op="Apply Model (K-Fold)" from_port="labelled data" to_op="Performance (K-Fold)" to_port="labelled data"/>
          <connect from_op="Performance (K-Fold)" from_port="performance" to_port="performance 1"/>
          <portSpacing port="source_model" spacing="0"/>
          <portSpacing port="source_test set" spacing="0"/>
          <portSpacing port="source_through 1" spacing="0"/>
          <portSpacing port="sink_test set results" spacing="0"/>
          <portSpacing port="sink_performance 1" spacing="0"/>
          <portSpacing port="sink_performance 2" spacing="0"/>
        </process>
      </operator>
      <operator activated="true" class="generate_attributes" compatibility="9.10.011" expanded="true" height="82" name="Generate Attributes" width="90" x="447" y="544">
        <list key="function_descriptions">
          <parameter key="TVandRadio" value="TV*Radio"/>
        </list>
        <parameter key="keep_all" value="true"/>
      </operator>
      <operator activated="true" class="select_attributes" compatibility="9.10.011" expanded="true" height="82" name="TV" width="90" x="447" y="238">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="TV"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
      </operator>
      <operator activated="true" class="linear_regression" compatibility="9.10.011" expanded="true" height="103" name="Simple Linear Regression - TV" width="90" x="581" y="238">
        <parameter key="feature_selection" value="none"/>
        <parameter key="alpha" value="0.05"/>
        <parameter key="max_iterations" value="10"/>
        <parameter key="forward_alpha" value="0.05"/>
        <parameter key="backward_alpha" value="0.05"/>
        <parameter key="eliminate_colinear_features" value="false"/>
        <parameter key="min_tolerance" value="0.05"/>
        <parameter key="use_bias" value="true"/>
        <parameter key="ridge" value="1.0E-8"/>
      </operator>
      <operator activated="true" class="select_attributes" compatibility="9.10.011" expanded="true" height="82" name="Radio" width="90" x="447" y="136">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="Radio"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
      </operator>
      <operator activated="true" class="linear_regression" compatibility="9.10.011" expanded="true" height="103" name="Simple Linear Regression - Radio" width="90" x="581" y="136">
        <parameter key="feature_selection" value="none"/>
        <parameter key="alpha" value="0.05"/>
        <parameter key="max_iterations" value="10"/>
        <parameter key="forward_alpha" value="0.05"/>
        <parameter key="backward_alpha" value="0.05"/>
        <parameter key="eliminate_colinear_features" value="false"/>
        <parameter key="min_tolerance" value="0.05"/>
        <parameter key="use_bias" value="true"/>
        <parameter key="ridge" value="1.0E-8"/>
      </operator>
      <operator activated="true" class="select_attributes" compatibility="9.10.011" expanded="true" height="82" name="Newspaper" width="90" x="447" y="34">
        <parameter key="attribute_filter_type" value="single"/>
        <parameter key="attribute" value="Newspaper"/>
        <parameter key="attributes" value=""/>
        <parameter key="use_except_expression" value="false"/>
        <parameter key="value_type" value="attribute_value"/>
        <parameter key="use_value_type_exception" value="false"/>
        <parameter key="except_value_type" value="time"/>
        <parameter key="block_type" value="attribute_block"/>
        <parameter key="use_block_type_exception" value="false"/>
        <parameter key="except_block_type" value="value_matrix_row_start"/>
        <parameter key="invert_selection" value="false"/>
        <parameter key="include_special_attributes" value="false"/>
      </operator>
      <operator activated="true" class="linear_regression" compatibility="9.10.011" expanded="true" height="103" name="Simple Linear Regression - Newspaper" width="90" x="581" y="34">
        <parameter key="feature_selection" value="none"/>
        <parameter key="alpha" value="0.05"/>
        <parameter key="max_iterations" value="10"/>
        <parameter key="forward_alpha" value="0.05"/>
        <parameter key="backward_alpha" value="0.05"/>
        <parameter key="eliminate_colinear_features" value="false"/>
        <parameter key="min_tolerance" value="0.05"/>
        <parameter key="use_bias" value="true"/>
        <parameter key="ridge" value="1.0E-8"/>
      </operator>
      <operator activated="true" class="apply_model" compatibility="9.10.011" expanded="true" height="82" name="Apply Model - Newspaper" width="90" x="715" y="34">
        <list key="application_parameters"/>
        <parameter key="create_view" value="false"/>
      </operator>
      <operator activated="true" class="performance_regression" compatibility="9.10.011" expanded="true" height="82" name="Performance - Newspaper" width="90" x="849" y="34">
        <parameter key="main_criterion" value="first"/>
        <parameter key="root_mean_squared_error" value="true"/>
        <parameter key="absolute_error" value="true"/>
        <parameter key="relative_error" value="false"/>
        <parameter key="relative_error_lenient" value="false"/>
        <parameter key="relative_error_strict" value="false"/>
        <parameter key="normalized_absolute_error" value="false"/>
        <parameter key="root_relative_squared_error" value="false"/>
        <parameter key="squared_error" value="false"/>
        <parameter key="correlation" value="false"/>
        <parameter key="squared_correlation" value="true"/>
        <parameter key="prediction_average" value="false"/>
        <parameter key="spearman_rho" value="false"/>
        <parameter key="kendall_tau" value="false"/>
        <parameter key="skip_undefined_labels" value="true"/>
        <parameter key="use_example_weights" value="true"/>
      </operator>
      <operator activated="true" class="apply_model" compatibility="9.10.011" expanded="true" height="82" name="Apply Model - Radio" width="90" x="715" y="136">
        <list key="application_parameters"/>
        <parameter key="create_view" value="false"/>
      </operator>
      <operator activated="true" class="performance_regression" compatibility="9.10.011" expanded="true" height="82" name="Performance - Radio" width="90" x="849" y="136">
        <parameter key="main_criterion" value="first"/>
        <parameter key="root_mean_squared_error" value="true"/>
        <parameter key="absolute_error" value="true"/>
        <parameter key="relative_error" value="false"/>
        <parameter key="relative_error_lenient" value="false"/>
        <parameter key="relative_error_strict" value="false"/>
        <parameter key="normalized_absolute_error" value="false"/>
        <parameter key="root_relative_squared_error" value="false"/>
        <parameter key="squared_error" value="false"/>
        <parameter key="correlation" value="false"/>
        <parameter key="squared_correlation" value="true"/>
        <parameter key="prediction_average" value="false"/>
        <parameter key="spearman_rho" value="false"/>
        <parameter key="kendall_tau" value="false"/>
        <parameter key="skip_undefined_labels" value="true"/>
        <parameter key="use_example_weights" value="true"/>
      </operator>
      <operator activated="true" class="apply_model" compatibility="9.10.011" expanded="true" height="82" name="Apply Model - TV" width="90" x="715" y="238">
        <list key="application_parameters"/>
        <parameter key="create_view" value="false"/>
      </operator>
      <operator activated="true" class="performance_regression" compatibility="9.10.011" expanded="true" height="82" name="Performance - TV" width="90" x="849" y="238">
        <parameter key="main_criterion" value="first"/>
        <parameter key="root_mean_squared_error" value="true"/>
        <parameter key="absolute_error" value="true"/>
        <parameter key="relative_error" value="false"/>
        <parameter key="relative_error_lenient" value="false"/>
        <parameter key="relative_error_strict" value="false"/>
        <parameter key="normalized_absolute_error" value="false"/>
        <parameter key="root_relative_squared_error" value="false"/>
        <parameter key="squared_error" value="false"/>
        <parameter key="correlation" value="false"/>
        <parameter key="squared_correlation" value="true"/>
        <parameter key="prediction_average" value="false"/>
        <parameter key="spearman_rho" value="false"/>
        <parameter key="kendall_tau" value="false"/>
        <parameter key="skip_undefined_labels" value="true"/>
        <parameter key="use_example_weights" value="true"/>
      </operator>
      <operator activated="true" class="linear_regression" compatibility="9.10.011" expanded="true" height="103" name="Multiple Linear Regression" width="90" x="447" y="391">
        <parameter key="feature_selection" value="none"/>
        <parameter key="alpha" value="0.05"/>
        <parameter key="max_iterations" value="10"/>
        <parameter key="forward_alpha" value="0.05"/>
        <parameter key="backward_alpha" value="0.05"/>
        <parameter key="eliminate_colinear_features" value="false"/>
        <parameter key="min_tolerance" value="0.05"/>
        <parameter key="use_bias" value="true"/>
        <parameter key="ridge" value="1.0E-8"/>
      </operator>
      <operator activated="true" class="apply_model" compatibility="9.10.011" expanded="true" height="82" name="Apply Model" width="90" x="581" y="391">
        <list key="application_parameters"/>
        <parameter key="create_view" value="false"/>
      </operator>
      <operator activated="true" class="performance_regression" compatibility="9.10.011" expanded="true" height="82" name="Performance" width="90" x="715" y="391">
        <parameter key="main_criterion" value="first"/>
        <parameter key="root_mean_squared_error" value="true"/>
        <parameter key="absolute_error" value="true"/>
        <parameter key="relative_error" value="false"/>
        <parameter key="relative_error_lenient" value="false"/>
        <parameter key="relative_error_strict" value="false"/>
        <parameter key="normalized_absolute_error" value="false"/>
        <parameter key="root_relative_squared_error" value="false"/>
        <parameter key="squared_error" value="false"/>
        <parameter key="correlation" value="false"/>
        <parameter key="squared_correlation" value="true"/>
        <parameter key="prediction_average" value="false"/>
        <parameter key="spearman_rho" value="false"/>
        <parameter key="kendall_tau" value="false"/>
        <parameter key="skip_undefined_labels" value="true"/>
        <parameter key="use_example_weights" value="true"/>
      </operator>
      <operator activated="true" class="linear_regression" compatibility="9.10.011" expanded="true" height="103" name="Multiple Linear Regression - TVandRadio" width="90" x="581" y="544">
        <parameter key="feature_selection" value="none"/>
        <parameter key="alpha" value="0.05"/>
        <parameter key="max_iterations" value="10"/>
        <parameter key="forward_alpha" value="0.05"/>
        <parameter key="backward_alpha" value="0.05"/>
        <parameter key="eliminate_colinear_features" value="false"/>
        <parameter key="min_tolerance" value="0.05"/>
        <parameter key="use_bias" value="true"/>
        <parameter key="ridge" value="1.0E-8"/>
      </operator>
      <operator activated="true" class="apply_model" compatibility="9.10.011" expanded="true" height="82" name="Apply Model - TVandRadio" width="90" x="715" y="544">
        <list key="application_parameters"/>
        <parameter key="create_view" value="false"/>
      </operator>
      <operator activated="true" class="performance_regression" compatibility="9.10.011" expanded="true" height="82" name="Performance - TVandRadio" width="90" x="849" y="544">
        <parameter key="main_criterion" value="first"/>
        <parameter key="root_mean_squared_error" value="true"/>
        <parameter key="absolute_error" value="true"/>
        <parameter key="relative_error" value="false"/>
        <parameter key="relative_error_lenient" value="false"/>
        <parameter key="relative_error_strict" value="false"/>
        <parameter key="normalized_absolute_error" value="false"/>
        <parameter key="root_relative_squared_error" value="false"/>
        <parameter key="squared_error" value="false"/>
        <parameter key="correlation" value="false"/>
        <parameter key="squared_correlation" value="true"/>
        <parameter key="prediction_average" value="false"/>
        <parameter key="spearman_rho" value="false"/>
        <parameter key="kendall_tau" value="false"/>
        <parameter key="skip_undefined_labels" value="true"/>
        <parameter key="use_example_weights" value="true"/>
      </operator>
      <connect from_op="Retrieve Advertising" from_port="output" to_op="Set Role" to_port="example set input"/>
      <connect from_op="Set Role" from_port="example set output" to_op="Multiply" to_port="input"/>
      <connect from_op="Multiply" from_port="output 1" to_op="Newspaper" to_port="example set input"/>
      <connect from_op="Multiply" from_port="output 2" to_op="Radio" to_port="example set input"/>
      <connect from_op="Multiply" from_port="output 3" to_op="TV" to_port="example set input"/>
      <connect from_op="Multiply" from_port="output 4" to_op="Multiple Linear Regression" to_port="training set"/>
      <connect from_op="Multiply" from_port="output 5" to_port="result 9"/>
      <connect from_op="Multiply" from_port="output 6" to_op="Generate Attributes" to_port="example set input"/>
      <connect from_op="Multiply" from_port="output 7" to_port="result 12"/>
      <connect from_op="Multiply" from_port="output 8" to_op="K Fold Cross Validation" to_port="example set"/>
      <connect from_op="Multiply" from_port="output 9" to_op="LOO Cross Validation" to_port="example set"/>
      <connect from_op="LOO Cross Validation" from_port="model" to_port="result 15"/>
      <connect from_op="LOO Cross Validation" from_port="performance 1" to_port="result 16"/>
      <connect from_op="K Fold Cross Validation" from_port="model" to_port="result 13"/>
      <connect from_op="K Fold Cross Validation" from_port="performance 1" to_port="result 14"/>
      <connect from_op="Generate Attributes" from_port="example set output" to_op="Multiple Linear Regression - TVandRadio" to_port="training set"/>
      <connect from_op="TV" from_port="example set output" to_op="Simple Linear Regression - TV" to_port="training set"/>
      <connect from_op="Simple Linear Regression - TV" from_port="model" to_op="Apply Model - TV" to_port="model"/>
      <connect from_op="Simple Linear Regression - TV" from_port="exampleSet" to_op="Apply Model - TV" to_port="unlabelled data"/>
      <connect from_op="Radio" from_port="example set output" to_op="Simple Linear Regression - Radio" to_port="training set"/>
      <connect from_op="Simple Linear Regression - Radio" from_port="model" to_op="Apply Model - Radio" to_port="model"/>
      <connect from_op="Simple Linear Regression - Radio" from_port="exampleSet" to_op="Apply Model - Radio" to_port="unlabelled data"/>
      <connect from_op="Newspaper" from_port="example set output" to_op="Simple Linear Regression - Newspaper" to_port="training set"/>
      <connect from_op="Simple Linear Regression - Newspaper" from_port="model" to_op="Apply Model - Newspaper" to_port="model"/>
      <connect from_op="Simple Linear Regression - Newspaper" from_port="exampleSet" to_op="Apply Model - Newspaper" to_port="unlabelled data"/>
      <connect from_op="Apply Model - Newspaper" from_port="labelled data" to_op="Performance - Newspaper" to_port="labelled data"/>
      <connect from_op="Apply Model - Newspaper" from_port="model" to_port="result 2"/>
      <connect from_op="Performance - Newspaper" from_port="performance" to_port="result 1"/>
      <connect from_op="Apply Model - Radio" from_port="labelled data" to_op="Performance - Radio" to_port="labelled data"/>
      <connect from_op="Apply Model - Radio" from_port="model" to_port="result 4"/>
      <connect from_op="Performance - Radio" from_port="performance" to_port="result 3"/>
      <connect from_op="Apply Model - TV" from_port="labelled data" to_op="Performance - TV" to_port="labelled data"/>
      <connect from_op="Apply Model - TV" from_port="model" to_port="result 6"/>
      <connect from_op="Performance - TV" from_port="performance" to_port="result 5"/>
      <connect from_op="Multiple Linear Regression" from_port="model" to_op="Apply Model" to_port="model"/>
      <connect from_op="Multiple Linear Regression" from_port="exampleSet" to_op="Apply Model" to_port="unlabelled data"/>
      <connect from_op="Apply Model" from_port="labelled data" to_op="Performance" to_port="labelled data"/>
      <connect from_op="Apply Model" from_port="model" to_port="result 8"/>
      <connect from_op="Performance" from_port="performance" to_port="result 7"/>
      <connect from_op="Multiple Linear Regression - TVandRadio" from_port="model" to_op="Apply Model - TVandRadio" to_port="model"/>
      <connect from_op="Multiple Linear Regression - TVandRadio" from_port="exampleSet" to_op="Apply Model - TVandRadio" to_port="unlabelled data"/>
      <connect from_op="Apply Model - TVandRadio" from_port="labelled data" to_op="Performance - TVandRadio" to_port="labelled data"/>
      <connect from_op="Apply Model - TVandRadio" from_port="model" to_port="result 11"/>
      <connect from_op="Performance - TVandRadio" from_port="performance" to_port="result 10"/>
      <portSpacing port="source_input 1" spacing="0"/>
      <portSpacing port="sink_result 1" spacing="0"/>
      <portSpacing port="sink_result 2" spacing="0"/>
      <portSpacing port="sink_result 3" spacing="0"/>
      <portSpacing port="sink_result 4" spacing="0"/>
      <portSpacing port="sink_result 5" spacing="0"/>
      <portSpacing port="sink_result 6" spacing="0"/>
      <portSpacing port="sink_result 7" spacing="0"/>
      <portSpacing port="sink_result 8" spacing="0"/>
      <portSpacing port="sink_result 9" spacing="0"/>
      <portSpacing port="sink_result 10" spacing="0"/>
      <portSpacing port="sink_result 11" spacing="0"/>
      <portSpacing port="sink_result 12" spacing="0"/>
      <portSpacing port="sink_result 13" spacing="0"/>
      <portSpacing port="sink_result 14" spacing="0"/>
      <portSpacing port="sink_result 15" spacing="0"/>
      <portSpacing port="sink_result 16" spacing="0"/>
      <portSpacing port="sink_result 17" spacing="0"/>
    </process>
  </operator>
</process>
